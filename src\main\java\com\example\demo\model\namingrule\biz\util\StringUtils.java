package com.example.demo.model.namingrule.biz.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtils extends org.apache.commons.lang3.StringUtils {

    private static final transient Logger LOGGER = LoggerFactory.getLogger(StringUtils.class);

    /**
     * <p>获取字符串指定位置的字符</p>
     *
     * @param str     原字符串
     * @param postion 指定字符串的位置
     * @return 字符串指定位置的字符
     * <AUTHOR>
     */
    public static String subString(String str, int postion) {
        LOGGER.debug("要截取的字符串：{},截取位置：{}", str, postion);
        Assert.hasText(str, "当前字符串不能为 null且必须至少包含一个非空格的字符");
        Assert.isTrue(str.trim().length() >= postion, "要截取的字符串长度不够");
        Assert.isTrue(postion >= 1, "postion必须大于等于1");
        return StringUtils.substring(str.trim(), postion - 1, postion);
    }

    /**
     * <p>获取字符串指定位置的多个字符</p>
     *
     * @param str      原字符串
     * @param postions 指定字符串的多个位置
     * @return 字符串指定位置的多个字符组成的新字符串
     * <AUTHOR>
     */
    public static String subString(String str, int... postions) {
        LOGGER.debug("要截取的字符串：{},截取位置：{}", str, postions);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < postions.length; i++) {
            sb.append(subString(str, postions[i]));
        }
        return sb.toString();
    }

    /**
     * 传入任意对象，遍历对象中的字符串属性，做trim操作
     */
    public static void doTrim(Object obj) {
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.getGenericType().toString().equals("class java.lang.String")) {
                field.setAccessible(true);
                try {
                    String value = (String) field.get(obj);
                    if (value != null) {
                        field.set(obj, value.trim());
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 正则表达式匹配两个指定字符串中间的内容
     *
     * @param soap
     * @return
     */
    public static List<String> getSubUtil(String soap, String rgex) {
        List<String> list = new ArrayList<String>();
        Pattern pattern = Pattern.compile(rgex); // 匹配的模式
        Matcher m = pattern.matcher(soap);
        while (m.find()) {
            int i = 1;
            list.add(m.group(i));
            i++;
        }
        return list;
    }

    /**
     * 返回单个字符串，若匹配到多个的话就返回第一个，方法与getSubUtil一样
     *
     * @param soap
     * @param rgex
     * @return
     */
    public static String getSubUtilSimple(String soap, String rgex) {
        Pattern pattern = Pattern.compile(rgex); // 匹配的模式
        Matcher m = pattern.matcher(soap);
        while (m.find()) {
            return m.group(0);
        }
        return "";
    }

}
