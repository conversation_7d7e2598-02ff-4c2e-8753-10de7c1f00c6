package com.example.demo.model.namingrule.biz.domain.namingRule.model;


import com.example.demo.model.namingrule.biz.domain.namingRule.proxy.NamingRuleParameterSrValueDataProxy;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTableType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Erupt(
        name = "编码规则序号值",desc = "记录某个命名规则下的某个SR类型参数，当前的依赖参数值对应的当前序号",
        dataProxy = NamingRuleParameterSrValueDataProxy.class
)
@Table(name = "naming_rule_parameter_sr_value",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"sr_parameter_id", "srParameterVersion"})
        }
)
@Entity
@Getter
@Setter
public class NamingRuleParameterSrValue extends BaseModel {

//    关系字段，与关系表中的JoinColumn中指定的字段名对应
    @Comment("引用字段，对应namingruleParamter的ID")
    @EruptField(
            views = @View(title = "规则参数", column = "parameterName"), //关联表中所需显示的字段名
            edit = @Edit(title = "规则参数", readonly = @Readonly,notNull = true, search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "parameterName")
            )
    )
    @ManyToOne
    private NamingRuleParameter srParameter;

    @EruptField(
            views = @View(title = "参数名称"),
            edit = @Edit(title = "参数名称",readonly = @Readonly, notNull = true, search = @Search(vague = true), inputType = @InputType(fullSpan = true)
            )
    )
    private String srParameterName;

    @EruptField(
            views = @View(title = "序号值版本"),
            edit = @Edit(title = "序号值版本",readonly = @Readonly, notNull = true, search = @Search(vague = true), inputType = @InputType(fullSpan = true)
            )
    )
    private String srParameterVersion;

    @EruptField(
            views = @View(title = "序号值"),
            edit = @Edit(title = "序号值", type = EditType.NUMBER
            )
    )
    @Comment("序号值")
    private Integer srNumber;

    @Comment("四位年，与后面的日/月结合使用，用于版本的定时清理")
    private String yearText;

    @Comment("2位月")
    private String monthText;

    @Comment("2位日")
    private String dayText;


}
