package com.example.demo.model.namingrule.biz.domain.namingRule.service;

import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRule;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;

@Service
public class NamingRuleDomainService {
    @Resource
    private EruptDao eruptDao;
    @Comment("根据命名规则编码获取命名规则")
    public NamingRule getParametersByCode(String ruleCode){
        CriteriaBuilder criteriaBuilder = eruptDao.getEntityManager().getCriteriaBuilder();
        CriteriaQuery<NamingRule> criteriaQuery = criteriaBuilder.createQuery(NamingRule.class);
        Root<NamingRule> namingRuleRoot = criteriaQuery.from(NamingRule.class);
        criteriaQuery.where(
                criteriaBuilder.and(
                        criteriaBuilder.equal(namingRuleRoot.get("namingRuleCode"), ruleCode)
                )
        );
        return eruptDao.getEntityManager().createQuery(criteriaQuery).getSingleResult();
    }
}
