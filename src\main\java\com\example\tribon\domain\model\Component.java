package com.example.tribon.domain.model;


import com.example.tribon.domain.enumeration.StructTypeEnum;
import com.example.tribon.domain.model.subModel.StructureExtractForm;
import com.example.tribon.domain.model.subModel.StructureShowRouteForm;
import com.example.tribon.handler.ExistsRemoteModeOperationHandlerImpl;
import com.example.tribon.handler.ExtractStructureOperationHandlerImpl;
import com.example.tribon.handler.HideRouteMapOperationHandlerImpl;
import com.example.tribon.handler.ShowRouteMapOperationHandlerImpl;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.Table;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Erupt(
        name = "组件"

)
@Table(name = "tribon_component"
)
@Entity
@Getter
@Setter
public class Component extends BaseModel {

    @EruptField(
            views = @View(title = "组件编码"),
            edit = @Edit(title = "组件编码", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private String code;

    /**
     * 安装件类型
     * 设备、扁条、托架、支架、贯穿件
     */
    @EruptField(
        views = @View(title = "组件类型"),
        edit = @Edit(title = "组件类型", notNull = false, search = @Search(vague = true),
        // 枚举
        type = EditType.CHOICE,
        choiceType = @ChoiceType(
                fetchHandler = StructTypeEnum.ChoiceFetch.class
        )
        )
    )
    private String componentType;

    /*
    * 长
    * */
    @EruptField(
            views = @View(title = "长"),
            edit = @Edit(title = "长", type = EditType.NUMBER, notNull = false,
                    numberType = @xyz.erupt.annotation.sub_field.sub_edit.NumberType(min = 0, max = 100000))
    )
    private Double length;

    /*
     * 宽
     * */
    @EruptField(
            views = @View(title = "宽"),
            edit = @Edit(title = "宽", type = EditType.NUMBER, notNull = false,
                    numberType = @xyz.erupt.annotation.sub_field.sub_edit.NumberType(min = 0, max = 100000))
    )
    private Double width;

    /*
     * 高
     * */
    @EruptField(
            views = @View(title = "高"),
            edit = @Edit(title = "高", type = EditType.NUMBER, notNull = false,
                    numberType = @xyz.erupt.annotation.sub_field.sub_edit.NumberType(min = 0, max = 100000))
    )
    private Double height;

    @EruptField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;


}
