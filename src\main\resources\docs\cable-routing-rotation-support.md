# 电缆走线算法旋转支持实现

## 🎯 问题背景

原始算法假设所有托架都是轴对齐的（没有旋转），直接使用长宽计算边缘坐标：

```java
// 原始算法 - 假设无旋转
double halfL = t.length / 2;
double halfW = t.width / 2;
edges.add(new Edge(t.id, 0, t.x-halfL, t.x+halfL, t.y-halfW, t.y-halfW, z)); // 下边
```

**问题**：当托架在三维空间中有旋转角度时，计算出的边缘坐标完全错误，导致连接点不准确。

## 🔧 解决方案设计

### 1. 扩展 Tray 类支持旋转

```java
public static class Tray {
    public final double rotX, rotY, rotZ; // 旋转角度（弧度）
    
    public Tray(int id, double x, double y, double zCenter,
                double length, double width, double height,
                double rotX, double rotY, double rotZ) {
        // 存储旋转信息
        this.rotX = rotX;
        this.rotY = rotY;
        this.rotZ = rotZ;
    }
}
```

### 2. 三维旋转变换实现

采用 **Z-Y-X 欧拉角** 旋转顺序：

```java
public static Point3D applyRotation(Point3D point, double rotX, double rotY, double rotZ) {
    double x = point.x, y = point.y, z = point.z;
    
    // 1. Z轴旋转（偏航角）
    if (rotZ != 0) {
        double cosZ = Math.cos(rotZ), sinZ = Math.sin(rotZ);
        double newX = x * cosZ - y * sinZ;
        double newY = x * sinZ + y * cosZ;
        x = newX; y = newY;
    }
    
    // 2. Y轴旋转（俯仰角）
    if (rotY != 0) {
        double cosY = Math.cos(rotY), sinY = Math.sin(rotY);
        double newX = x * cosY + z * sinY;
        double newZ = -x * sinY + z * cosY;
        x = newX; z = newZ;
    }
    
    // 3. X轴旋转（滚转角）
    if (rotX != 0) {
        double cosX = Math.cos(rotX), sinX = Math.sin(rotX);
        double newY = y * cosX - z * sinX;
        double newZ = y * sinX + z * cosX;
        y = newY; z = newZ;
    }
    
    return new Point3D(x, y, z);
}
```

### 3. 顶点和边缘计算

```java
public Point3D[] getTopVertices() {
    double halfL = length / 2;
    double halfW = width / 2;
    
    // 原始顶点（相对于托架中心，未旋转）
    Point3D[] originalVertices = {
        new Point3D(-halfL, -halfW, height / 2), // 左下
        new Point3D(halfL, -halfW, height / 2),  // 右下
        new Point3D(halfL, halfW, height / 2),   // 右上
        new Point3D(-halfL, halfW, height / 2)   // 左上
    };
    
    // 应用旋转变换并平移到世界坐标
    Point3D[] rotatedVertices = new Point3D[4];
    for (int i = 0; i < 4; i++) {
        rotatedVertices[i] = applyRotation(originalVertices[i], rotX, rotY, rotZ);
        rotatedVertices[i] = new Point3D(
            rotatedVertices[i].x + x,
            rotatedVertices[i].y + y,
            rotatedVertices[i].z + (zTop - height / 2)
        );
    }
    
    return rotatedVertices;
}
```

### 4. 边缘生成

```java
public Edge[] getTopEdges() {
    Point3D[] vertices = getTopVertices();
    return new Edge[] {
        new Edge(id, 0, vertices[0], vertices[1]), // 下边：左下 → 右下
        new Edge(id, 1, vertices[2], vertices[3]), // 上边：右上 → 左上
        new Edge(id, 2, vertices[3], vertices[0]), // 左边：左上 → 左下
        new Edge(id, 3, vertices[1], vertices[2])  // 右边：右下 → 右上
    };
}
```

## 📊 数据流程

### 1. 输入数据解析

```java
// Structure 模型中的 rot 属性
String rot = "45.0,0.0,30.0"; // X轴45度，Y轴0度，Z轴30度

// 解析为弧度
double[] rotation = parseRotation(rot);
// rotation = [0.785, 0.0, 0.524] (弧度)
```

### 2. 托架创建

```java
CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(
    id, x, y, zCenter, length, width, height,
    rotation[0], rotation[1], rotation[2] // 旋转角度
);
```

### 3. 边缘计算示例

假设托架：
- 中心：(0, 0, 0)
- 尺寸：100×50×20
- 旋转：Z轴45度

**原始顶点**（相对坐标）：
```
左下: (-50, -25, 10)
右下: (50, -25, 10)
右上: (50, 25, 10)
左上: (-50, 25, 10)
```

**Z轴旋转45度后**：
```
左下: (-17.68, -53.03, 10)
右下: (17.68, -53.03, 10)
右上: (53.03, 17.68, 10)
左上: (-53.03, 17.68, 10)
```

## 🧪 测试验证

### 1. 基础旋转测试

```java
@Test
void testRotationParsing() {
    Point3D originalPoint = new Point3D(1, 0, 0);
    Point3D rotatedPoint = applyRotation(originalPoint, 0, 0, Math.toRadians(90));
    
    // Z轴旋转90度后，(1,0,0) 应该变成 (0,1,0)
    assertEquals(0.0, rotatedPoint.x, 0.001);
    assertEquals(1.0, rotatedPoint.y, 0.001);
    assertEquals(0.0, rotatedPoint.z, 0.001);
}
```

### 2. 旋转托架连接测试

```java
@Test
void testRotatedTrayConnection() {
    List<Tray> trays = Arrays.asList(
        new Tray(0, 0, 0, 0, 100, 50, 20),                    // 无旋转
        new Tray(1, 200, 0, 0, 100, 50, 20, Math.toRadians(45), 0, 0) // X轴旋转45度
    );
    
    List<Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
    assertFalse(segments.isEmpty(), "应该能找到路径");
}
```

## 🎯 关键改进

### 1. 精确的几何计算
- ✅ 支持任意角度的三维旋转
- ✅ 准确计算旋转后的顶点坐标
- ✅ 正确生成旋转后的边缘

### 2. 兼容性保持
- ✅ 保留原有构造函数（默认无旋转）
- ✅ 兼容现有的边缘表示方式
- ✅ 向后兼容现有代码

### 3. 灵活的边缘处理
```java
public class Edge {
    public final Point3D start, end; // 支持任意方向的边
    
    public Point3D getClosestPointTo(Point3D point) {
        // 计算边上距离指定点最近的点
        // 支持任意方向的边
    }
}
```

## 📋 使用示例

### 1. 前端数据格式
```json
{
    "startStructCode": "TRAY001",
    "endStructCode": "TRAY002"
}
```

### 2. Structure 数据
```
TRAY001:
- cog: "-938.33,-209.84,1612.31"
- rot: "45.0,0.0,30.0"
- componentName: "TRAY_950x500x240"

TRAY002:
- cog: "1391.67,-989.16,1601.97"
- rot: "0.0,15.0,0.0"
- componentName: "TRAY_950x500x240"
```

### 3. 计算结果
```json
{
    "startPoint": {
        "x": -1234.56,
        "y": -123.45,
        "z": 1732.31
    },
    "endPoint": {
        "x": 1567.89,
        "y": -876.54,
        "z": 1721.97
    }
}
```

## 🚀 总结

通过引入旋转支持，电缆走线算法现在能够：

1. **✅ 准确处理旋转托架**：支持任意角度的三维旋转
2. **✅ 精确计算连接点**：基于真实的几何形状计算边缘
3. **✅ 保持算法稳定性**：兼容现有代码，向后兼容
4. **✅ 提供真实结果**：返回的坐标点位于实际的托架边缘

这个改进解决了之前坐标点不正确的根本问题，为船舶设计中的电缆布线提供了更加精确和可靠的路径规划能力。

**关键优势**：
- 🎯 **几何精确性**：考虑托架的实际空间姿态
- 🔧 **算法健壮性**：处理复杂的三维旋转场景
- 📐 **工程实用性**：符合实际船舶设计需求
- 🚀 **性能优化**：高效的旋转变换计算
