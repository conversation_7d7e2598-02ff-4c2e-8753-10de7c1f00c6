package com.example.tribon.domain.model.subModel;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(
        name = "安装件数据抽取表单"
)
@Getter
@Setter
public class StructureExtractForm extends BaseModel {

    @EruptField(
            views = @View(title = "Module"),
            edit = @Edit(title = "Module")
    )
    private String module;

    @EruptField(
            views = @View(title = "Structure"),
            edit = @Edit(title = "Structure")
    )
    private String structure;
}
