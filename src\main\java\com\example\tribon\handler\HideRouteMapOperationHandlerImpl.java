package com.example.tribon.handler;

import com.example.tribon.domain.model.Structure;
import com.example.tribon.domain.model.dto.StructureDto;
import com.example.tribon.domain.model.StructureRelationship;
import com.example.tribon.domain.repository.StructureRelationshipRepository;
import com.example.tribon.domain.repository.StructureRepository;
import com.example.tribon.domain.model.subModel.StructureShowRouteForm;
import com.example.tribon.dto.SockeShowRouteDto;
import com.example.tribon.service.TribonService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.dao.EruptJpaDao;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Transactional
public class HideRouteMapOperationHandlerImpl implements OperationHandler<Structure, StructureShowRouteForm> {

    @Resource
    private EruptJpaDao eruptJpaDao;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private StructureRelationshipRepository structureRelationshipRepository;

    @Resource
    private StructureRepository structureRepository;

    @Resource
    private TribonService tribonService;

    @Override
    public String exec(List<Structure> data, StructureShowRouteForm structureShowRouteForm, String[] param) {
        try (Socket socket = new Socket("***************", 8000)) {
            PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
            BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));

            // 构造请求参数
            SockeShowRouteDto dto = new SockeShowRouteDto();
            dto.setRequestData(new SockeShowRouteDto.RequestData(getHandles(structureShowRouteForm.getModule(), structureShowRouteForm.getStructure()), "SHOW"));
            ObjectMapper objectMapper = new ObjectMapper();
            out.println(objectMapper.writeValueAsString(dto)); // 发送消息
            StringBuilder responseBuilder = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                responseBuilder.append(line).append(System.lineSeparator());
                if (line.endsWith("%EOR%")) {
                    break;
                }
            }
            System.out.println("服务器回复: " + responseBuilder.toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            tribonService.existRemoteMode();
        }
        return null;
    }

    private List<Long> getHandles(String module, String structureCode) {
        // 查询StructureRelationship中符合structureCode的记录
        // 根据structureCode 查询StructureRelationship 中匹配structCodeA 或者structCodeB 的记录，如果structrueCode中包含*，使用like查询，否则使用=
        // 写一个jpa repository查询
//        String query = "";
//        List<StructureRelationship> structureRelationships = new ArrayList<>();
//        List<StructureDto> structureDtos = new ArrayList<>();
//        if (structureCode.contains("*")) {
//            structureCode = structureCode.replace("*", "%");
//            structureRelationships = structureRelationshipRepository.getListLike(structureCode, structureCode);
//            structureDtos = structureRepository.getStructureXLike(structureCode);
//        } else {
//            structureRelationships = structureRelationshipRepository.getListExact(structureCode, structureCode);
//            structureDtos = structureRepository.getStructureXExact(structureCode);
//        }
//        // 将structureRelationships中不为空的handles转为List<Long>
//        List<Long> lineHandles = structureRelationships.stream()
//                .map(StructureRelationship::getLineHandle)
//                .filter(Objects::nonNull)  // 过滤掉 null 值
//                .collect(Collectors.toList());
//        // 将structureDtos中不为空的cogXMarkHandle2和cogXMarkHandle1转为List<Long>
//        List<Long> structureHandles = structureDtos.stream()
//                .flatMap(dto -> Stream.of(dto.getCogXMarkHandle1(), dto.getCogXMarkHandle2()))
//                .filter(Objects::nonNull)  // 过滤掉 null 值
//                .collect(Collectors.toList());
//        // 合并两个List<Long>
//        List<Long> allHandles = new ArrayList<>(lineHandles);
//        allHandles.addAll(structureHandles);
//        // 去重
//        return allHandles.stream().distinct().collect(Collectors.toList());
        return null;
    }

    @Override
    public StructureShowRouteForm eruptFormValue(List<Structure> data, StructureShowRouteForm structureShowRouteForm, String[] param) {
        return structureShowRouteForm;
    }
}
