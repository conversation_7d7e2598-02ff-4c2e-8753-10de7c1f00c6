///*
// * Licensed to the Apache Software Foundation (ASF) under one or more
// * contributor license agreements.  See the NOTICE file distributed with
// * this work for additional information regarding copyright ownership.
// * The ASF licenses this file to You under the Apache License, Version 2.0
// * (the "License"); you may not use this file except in compliance with
// * the License.  You may obtain a copy of the License at
// *
// *     http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//package com.example.restController;
//
//import org.apache.dubbo.config.ReferenceConfig;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.apache.dubbo.rpc.service.GenericService;
//import xyz.erupt.core.service.EruptCoreService;
//import xyz.erupt.core.view.EruptModel;
//
//import javax.ws.rs.core.MultivaluedMap;
//
//@DubboService()
//public class GeneralServiceImpl implements GeneralService {
//
//    @Override
//    public Object generalGet(String interfaceName, String function) {
////        String interfaceName = "com.example.wom.order_api.OrderService";
//        ReferenceConfig<GenericService> reference = new ReferenceConfig<GenericService>();
//        reference.setInterface(interfaceName);
//        reference.setTimeout(3000);
//        reference.setGeneric(true);
//        GenericService genericService = reference.get();
//        Object results = genericService.$invoke(function, new String[]{"java.lang.String"}, new Object[]{"generalPost-Invoke"});
//        return results;
//    }
//
//    @Override
//    public Object generalPost(String interfaceName, String function, MultivaluedMap<String, String> formParams){
////        String interfaceName = "com.example.wom.order_api.OrderService";
//        ReferenceConfig<GenericService> reference = new ReferenceConfig<GenericService>();
//        reference.setInterface(interfaceName);
//        reference.setTimeout(30000);
//        reference.setGeneric(true);
//        GenericService genericService = reference.get();
//        Object results = genericService.$invoke(function, new String[]{"java.lang.Object"}, new Object[]{formParams});
//        return results;
//    }
//
//    @Override
//    public Object modelGet(String interfaceName, String function, String modelName, Object id){
//        ReferenceConfig<GenericService> reference = new ReferenceConfig<GenericService>();
//        reference.setInterface(interfaceName);
//        reference.setTimeout(30000);
//        reference.setGeneric(true);
//        GenericService genericService = reference.get();
//        EruptModel eruptModel = EruptCoreService.getErupt(modelName);
//        Object results = genericService.$invoke(function, new String[]{"java.lang.String","java.lang.Object"}, new Object[]{modelName, id});
//        return results;
//
//
//    }
//
//
//
//
//}
