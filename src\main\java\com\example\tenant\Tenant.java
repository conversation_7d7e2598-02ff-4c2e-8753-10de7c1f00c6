package com.example.tenant;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.upms.model.base.HyperModel;

import javax.persistence.*;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2020/12/15 12:06
 */
@Erupt(
        name = "租户"
)
@Table(name = "fd_tenant")
@Entity
public class Tenant extends HyperModel {


    @EruptField(
            views = @View(title = "企业名称"),
            edit = @Edit(title = "企业名称", notNull = true, search = @Search(vague = true), inputType = @InputType(fullSpan = true))
    )
    private String name;


    @EruptField(
        views = @View(title = "生效状态"),
        edit = @Edit(title = "生效状态", notNull = true, type = EditType.CHOICE,
                choiceType = @ChoiceType(
                        vl = {
                                @VL(label = "活动", value = "Y"),
                                @VL(label = "停用", value = "N")
                        }
                ))
    )
    private String state;

    @EruptField(
            views = @View(title = "生效日期"),
            edit = @Edit(title = "生效日期", type = EditType.DATE, dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date effectiveDate;

    @EruptField(
            views = @View(title = "失效日期"),
            edit = @Edit(title = "失效日期", type = EditType.DATE, dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date expireDate;

    @OneToMany
    @EruptField(
            views = @View(title = "模块"),
            edit = @Edit(title = "模块", notNull = true, search = @Search, type = EditType.TAB_TABLE_REFER)
    )
    private Set<FabModule> fabModule;

}
