# Structure Luckysheet 故障排除指南

## 常见问题及解决方案

### 1. "luckysheet.loadUrl is not a function" 错误

**问题描述**: 页面报错 `luckysheet.loadUrl is not a function`

**原因**: Luckysheet 2.x 版本中没有 `loadUrl` 方法

**解决方案**: 
- 已修复：使用 `luckysheet.destroy()` 和 `luckysheet.create()` 重新创建实例
- 测试页面：访问 `/tpl/structure-luckysheet-test.html` 验证基本功能

### 2. 页面无法加载

**问题描述**: 访问页面时显示 404 或页面空白

**可能原因**:
- 应用未正常启动
- 模板路径配置错误
- 静态资源加载失败

**解决方案**:
1. 检查应用启动日志
2. 确认访问路径：
   - 主页面：`http://localhost:8080/tpl/structure-luckysheet.html`
   - 测试页面：`http://localhost:8080/tpl/structure-luckysheet-test.html`
3. 检查网络连接，确保能访问 CDN 资源

### 3. 数据加载失败

**问题描述**: 点击"加载数据"按钮后显示加载失败

**可能原因**:
- 后端 API 接口异常
- 数据库连接问题
- 数据格式转换错误

**解决方案**:
1. 检查浏览器控制台错误信息
2. 检查网络请求状态：
   ```
   POST /api/structure-luckysheet/data
   ```
3. 查看应用日志中的异常信息
4. 确认数据库中有 Structure 数据

### 4. 保存数据失败

**问题描述**: 点击"保存"按钮后显示保存失败

**可能原因**:
- 数据验证失败
- 数据库写入权限问题
- 数据格式不正确

**解决方案**:
1. 检查数据格式是否正确
2. 确认必填字段已填写
3. 检查数据库连接和权限
4. 查看后端日志中的详细错误信息

### 5. Luckysheet 表格显示异常

**问题描述**: 表格显示不正常或功能异常

**可能原因**:
- CDN 资源加载失败
- 浏览器兼容性问题
- 初始化参数错误

**解决方案**:
1. 检查浏览器控制台是否有 JavaScript 错误
2. 尝试刷新页面或清除浏览器缓存
3. 使用现代浏览器（Chrome、Firefox、Safari）
4. 检查网络连接，确保 CDN 资源可访问

### 6. 查询功能无效

**问题描述**: 输入查询条件后无法正确过滤数据

**可能原因**:
- 查询参数传递错误
- 后端查询逻辑问题
- 数据库字段不匹配

**解决方案**:
1. 检查查询参数是否正确传递
2. 确认数据库中字段名称和类型
3. 查看后端查询 SQL 是否正确
4. 测试不同的查询条件组合

## 调试方法

### 1. 浏览器调试

**打开开发者工具**:
- Chrome: F12 或 Ctrl+Shift+I
- Firefox: F12 或 Ctrl+Shift+I
- Safari: Cmd+Option+I

**检查项目**:
- Console 标签：查看 JavaScript 错误
- Network 标签：检查 API 请求状态
- Elements 标签：检查 DOM 结构

### 2. 后端日志

**日志位置**: `logs/application.log`

**关键日志关键字**:
- `StructureLuckysheet`
- `ERROR`
- `Exception`

**日志级别设置**:
```yaml
logging:
  level:
    com.example.tribon: DEBUG
```

### 3. 数据库检查

**检查 Structure 表**:
```sql
SELECT COUNT(*) FROM tribon_structure;
SELECT * FROM tribon_structure LIMIT 10;
```

**检查表结构**:
```sql
DESCRIBE tribon_structure;
```

## 性能优化

### 1. 大数据量处理

**问题**: 数据量过大导致页面卡顿

**解决方案**:
- 实现分页查询
- 添加数据过滤条件
- 使用虚拟滚动

### 2. 网络优化

**问题**: 页面加载缓慢

**解决方案**:
- 使用本地 CDN 或下载资源到本地
- 启用 GZIP 压缩
- 优化 API 响应大小

## 版本兼容性

### Luckysheet 版本
- 当前使用：2.1.13
- 兼容性：支持现代浏览器
- 已知问题：某些 API 在不同版本间有差异

### 浏览器支持
- ✅ Chrome 70+
- ✅ Firefox 65+
- ✅ Safari 12+
- ✅ Edge 79+
- ❌ IE 11 及以下

### Spring Boot 版本
- 当前使用：2.7.10
- Erupt 版本：1.12.20
- Java 版本：8+

## 联系支持

如果以上解决方案无法解决问题，请：

1. **收集信息**:
   - 浏览器类型和版本
   - 错误截图
   - 浏览器控制台错误信息
   - 后端日志相关部分

2. **测试步骤**:
   - 访问测试页面确认基本功能
   - 尝试不同的操作步骤
   - 记录具体的错误现象

3. **提供反馈**:
   - 详细描述问题现象
   - 提供错误信息和日志
   - 说明期望的行为

## 更新日志

### v1.0.1 (修复版本)
- ✅ 修复 `luckysheet.loadUrl` 不存在的问题
- ✅ 修复数据转换格式问题
- ✅ 添加测试页面
- ✅ 改进错误处理

### v1.0.0 (初始版本)
- ❌ 存在 API 调用问题
- ❌ 数据格式转换错误
- ✅ 基本功能实现
