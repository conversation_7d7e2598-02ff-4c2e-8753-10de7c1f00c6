package com.example.demo.model.namingrule.biz.domain.namingRule.model;


import com.example.demo.model.namingrule.api.enumeration.NamingRuleEnum;
import com.example.demo.model.namingrule.biz.domain.namingRule.proxy.NamingRuleParameterDataProxy;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;
import java.util.Set;

@Erupt(
        name = "规则编码参数", dataProxy = NamingRuleParameterDataProxy.class
)
@Table(name = "naming_rule_parameter",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"naming_rule_id", "parameterName"})
        }
)
@Entity
@Getter
@Setter
public class NamingRuleParameter extends BaseModel {

//    关系字段，与关系表中的JoinColumn中指定的字段名对应
    @Comment("引用字段，对应namingrule的ID")
    @EruptField(
            views = @View(title = "编码规则", column = "namingRuleCode"),
            edit = @Edit(title = "编码规则", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true),
                 type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "namingRuleCode")
            )
    )
    @ManyToOne
    private NamingRule namingRule;

    @EruptField(
            views = @View(title = "参数编码"),
            edit = @Edit(title = "参数编码", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true)
            )
    )
    private String parameterName;

    @EruptField(
            views = @View(title = "参数类型"),
            edit = @Edit(title = "参数类型", notNull = true,type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(
                    fetchHandler = NamingRuleEnum.class
            )
            )
    )
    private String parameterType;

    @EruptField(
            views = @View(title = "依赖参数"),
            edit = @Edit(title = "依赖参数", type = EditType.INPUT,
                    showBy = @ShowBy(dependField = "parameterType", expr = "value == 'SR'")

            )
    )
    @Comment("该参数依赖哪些其他参数，参数类型为SR时使用，多个参数以逗号分隔")
    private String dependParameters;

    @EruptField(
            views = @View(title = "参数序号"),
            edit = @Edit(title = "参数序号", type = EditType.NUMBER
            )
    )
    @Comment("参数序号")
    private Integer parameterSequence;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "sr_parameter_id")
    @EruptField(
            views = @View(title = "序号值列表", column = "namingRuleParameterSrValueSet"
                    ),
            edit = @Edit(title = "序号值列表",
                    type = EditType.TAB_TABLE_ADD,
                    showBy = @ShowBy(dependField = "parameterType", expr = "value == 'SR'"))
    )
    @Comment("该参数下生成的各个版本的序号值")
    private Set<NamingRuleParameterSrValue> namingRuleParameterSrValueSet;

    @EruptField(
            views = @View(title = "常量值"),
            edit = @Edit(title = "常量值", type = EditType.INPUT,
            showBy = @ShowBy(dependField = "parameterType", expr = "value == 'CS'"))
    )
    @Comment("常量值，当参数类型为CS时使用")
    private String constantValue;

    @EruptField(
            views = @View(title = "起始值"),
            edit = @Edit(title = "起始值", type = EditType.NUMBER,
            showBy = @ShowBy(dependField = "parameterType", expr = "value == 'SR'"))
    )
    @Comment("序号的起始值，当参数类型为SR时使用")
    private Integer startNumber;

    @EruptField(
            views = @View(title = "序号位数"),
            edit = @Edit(title = "序号位数", type = EditType.NUMBER,
            showBy = @ShowBy(dependField = "parameterType", expr = "value == 'SR'"))
    )
    @Comment("序号所占位数，不足时左侧补0，生成到满位数值后，从起始值开始重新生成")
    private Integer lengthNumber;

    @EruptField(
            edit = @Edit(title = "说明", notNull = false,
                    type = EditType.TEXTAREA)
    )
    private String desciption;

}
