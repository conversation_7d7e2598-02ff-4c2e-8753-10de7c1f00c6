package com.example.tribon.remote.rest;

import com.example.tribon.domain.model.RuleRouteMap;
import com.example.tribon.domain.model.Structure;
import com.example.tribon.domain.model.StructureRelationship;
import com.example.tribon.domain.service.StructureDomainService;
import com.example.tribon.dto.CreateRelationReceiveDto;
import com.example.tribon.dto.CreateRelationResponseDto;
import com.example.tribon.dto.CreateRouteMapDto;
import com.example.tribon.dto.CreateRouteMapResponseDto;
import com.example.tribon.dto.GenerateRouteMapDto;
import com.example.tribon.dto.GenerateRouteMapResponseDto;
import com.example.tribon.dto.PathRequestDto;
import com.example.tribon.dto.PathResponseDto;
import com.example.tribon.service.CableRoutingSolverService;
import com.example.tribon.service.RouteMapRuleService;
import com.example.tribon.service.RouteMapService;
import com.example.tribon.service.RuleRouteMapConverterService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.dao.EruptJpaDao;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class StructRestService {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptJpaDao eruptJpaDao;

    @Resource
    private StructureDomainService structureDomainService;

    @Resource
    private CableRoutingSolverService cableRoutingSolverService;


    /**
     * 接收交互端上传的struct关系，保存关系标记flag
     * 返回一个数组，标识每个LineHandle是否新建，如果已存在，处理端需删除
     * @return List of Struct objects
     */
    @PostMapping("rest/createStructRelation")
    public ResponseEntity<CreateRelationResponseDto> createStructRelation(@RequestBody Map<String, Object> requestBody) throws JsonProcessingException {
        CreateRelationResponseDto createRelationResponseDto = new CreateRelationResponseDto();
        ObjectMapper objectMapper = new ObjectMapper();
        CreateRelationReceiveDto dto = objectMapper.convertValue(requestBody, CreateRelationReceiveDto.class);


        // 保存structCodeA和structCodeB到StructureRelationship中，注意StructureRelationship记录的也是code，不需要查询出对象
        StructureRelationship relationship = eruptDao.queryEntity(StructureRelationship.class, "structCodeA = :structCodeA AND structCodeB = :structCodeB", new HashMap<String, Object>() {{
            put("structCodeA", dto.getStartStruct().getStructCode());
            put("structCodeB", dto.getEndStruct().getStructCode());
        }});
        if (relationship == null) {
            // 不存在时创建
            relationship = new StructureRelationship();
            relationship.setStructCodeA(dto.getStartStruct().getStructCode());
            relationship.setStructCodeB(dto.getEndStruct().getStructCode());
            relationship.setEdgeStructCode(dto.getEdgeFlagStructCode());
            eruptJpaDao.addEntity(StructRestService.class,relationship);
        } else {
            if (relationship.getEdgeStructCode() != null) {
                // 如果已有数据，则返回处理端已存在的标识，处理端删除
                createRelationResponseDto.setRelationLineExistsFlag(true);
            } else {
                relationship.setEdgeStructCode(dto.getEdgeFlagStructCode());
                eruptJpaDao.editEntity(StructRestService.class, relationship);
            }
        }
        // 更新Structure的重心X标记handle
        Structure structA = eruptDao.queryEntity(Structure.class, "code = :code", new HashMap<String, Object>() {{
            put("code", dto.getStartStruct().getStructCode());
        }});
        Structure structB = eruptDao.queryEntity(Structure.class, "code = :code", new HashMap<String, Object>() {{
            put("code", dto.getEndStruct().getStructCode());
        }});
        // 判断是否存在，如果不存在则创建，存在则返回处理端已存在的标识，处理端删除
        if (structA != null && structA.getCogXMark1StructCode() != null) {
            createRelationResponseDto.setStructCodeAExistFlag(true);
        } else {
            structA.setCogXMark1StructCode(dto.getStartStruct().getX1FlagStructCode());
            structA.setCogXMark2StructCode(dto.getStartStruct().getX2FlagStructCode());
            eruptJpaDao.editEntity(StructRestService.class, structA);
        }
        if (structB != null && structB.getCogXMark1StructCode() != null) {
            createRelationResponseDto.setStructCodeBExistFlag(true);
        } else {
            structB.setCogXMark1StructCode(dto.getEndStruct().getX1FlagStructCode());
            structB.setCogXMark2StructCode(dto.getEndStruct().getX2FlagStructCode());
            eruptJpaDao.editEntity(StructRestService.class, structB);
        }
        // 返回
        return ResponseEntity.ok(createRelationResponseDto);

    }

    /**
     * 接收交互端上传的一组RouteMap，解析出Struct关系，创建关系，并更新标记StructCode到Structure中
     * @param requestBody
     * @return
     */
    @PostMapping("rest/createRouteMap")
    public ResponseEntity<CreateRouteMapResponseDto> createRouteMap(@RequestBody Map<String, Object> requestBody) throws JsonProcessingException {
        CreateRouteMapResponseDto createRouteMapResponseDto = new CreateRouteMapResponseDto();
        ObjectMapper objectMapper = new ObjectMapper();
        CreateRouteMapDto dto = objectMapper.convertValue(requestBody, CreateRouteMapDto.class);

        // 解析RouteMap中的Struct关系
        List<StructureRelationship> relationships = structureDomainService.parseRouteMapToRelationships(dto);
        // 保存关系到StructureRelationship中
        for (StructureRelationship relationship : relationships) {
            StructureRelationship existingRelationship = eruptDao.queryEntity(StructureRelationship.class, "structCodeA = :structCodeA AND structCodeB = :structCodeB", new HashMap<String, Object>() {{
                put("structCodeA", relationship.getStructCodeA());
                put("structCodeB", relationship.getStructCodeB());
            }});
            Structure structA = eruptDao.queryEntity(Structure.class, "code = :code", new HashMap<String, Object>() {{
                put("code", relationship.getStructCodeA());
            }});
            Structure structB = eruptDao.queryEntity(Structure.class, "code = :code", new HashMap<String, Object>() {{
                put("code", relationship.getStructCodeB());
            }});
            if (existingRelationship == null) {
                // 不存在时创建关系
                eruptJpaDao.addEntity(StructRestService.class, relationship);
                // 更新Structure中的标记

                if (structA != null) {
                    structA.setCogXMark1StructCode(relationship.getCogXMark1StructCodeA());
                    structA.setCogXMark2StructCode(relationship.getCogXMark2StructCodeA());
                    structA.setFlagX1HandlesA(relationship.getFlagX1HandlesA());
                    structA.setFlagX2HandlesA(relationship.getFlagX2HandlesA());
                    eruptJpaDao.editEntity(StructRestService.class, structA);
                }
                if (structB != null) {
                    structB.setCogXMark1StructCode(relationship.getCogXMark1StructCodeB());
                    structB.setCogXMark2StructCode(relationship.getCogXMark2StructCodeB());
                    structB.setFlagX1HandlesB(relationship.getFlagX1HandlesB());
                    structB.setFlagX2HandlesB(relationship.getFlagX2HandlesB());
                    eruptJpaDao.editEntity(StructRestService.class, structB);
                }
            } else {
                // 存在时更新，同时将被覆盖的structCode和handles发送到交互端进行删除
                if (createRouteMapResponseDto.getRemoveStructCodeList() == null) {
                    createRouteMapResponseDto.setRemoveStructCodeList(new ArrayList<>());
                }
                if (createRouteMapResponseDto.getRemoveHandlesList() == null) {
                    createRouteMapResponseDto.setRemoveHandlesList(new ArrayList<>());
                }
                createRouteMapResponseDto.getRemoveStructCodeList().add(existingRelationship.getEdgeStructCode());
                createRouteMapResponseDto.getRemoveStructCodeList().add(existingRelationship.getCogXMark1StructCodeA());
                createRouteMapResponseDto.getRemoveStructCodeList().add(existingRelationship.getCogXMark2StructCodeA());
                createRouteMapResponseDto.getRemoveStructCodeList().add(existingRelationship.getCogXMark1StructCodeB());
                createRouteMapResponseDto.getRemoveStructCodeList().add(existingRelationship.getCogXMark2StructCodeB());
                createRouteMapResponseDto.getRemoveHandlesList().addAll(existingRelationship.getEdgeStructHandle());
                if (structA != null) {
                    createRouteMapResponseDto.getRemoveHandlesList().addAll(structA.getFlagX1HandlesA());
                }
                if (structB != null) {
                    createRouteMapResponseDto.getRemoveHandlesList().addAll( structB.getFlagX1HandlesB());
                }
                // 更新关系
                eruptJpaDao.editEntity(StructRestService.class, existingRelationship);
                // 更新Structure
                if (structA != null) {
                    structA.setCogXMark1StructCode(relationship.getCogXMark1StructCodeA());
                    structA.setCogXMark2StructCode(relationship.getCogXMark2StructCodeA());
                    structA.setFlagX1HandlesA(relationship.getFlagX1HandlesA());
                    structA.setFlagX2HandlesA(relationship.getFlagX2HandlesA());
                    eruptJpaDao.editEntity(StructRestService.class, structA);
                }
                if (structB != null) {
                    structB.setCogXMark1StructCode(relationship.getCogXMark1StructCodeB());
                    structB.setCogXMark2StructCode(relationship.getCogXMark2StructCodeB());
                    structB.setFlagX1HandlesB(relationship.getFlagX1HandlesB());
                    structB.setFlagX2HandlesB(relationship.getFlagX2HandlesB());
                    eruptJpaDao.editEntity(StructRestService.class, structB);
                }

            }
        }
        return ResponseEntity.ok(createRouteMapResponseDto);
    }

    /*     * 接收交互端上传的RouteMap，生成路线图
     * 根据传入的structCodes查询出所有的Structure对象
     * 查第一条规则
     * 转换为RouteMapService可接收的参数类型
     * 生成路径
     * 返回生成的路线图
     */
    @PostMapping("rest/GenerateRouteMap")
    public ResponseEntity<GenerateRouteMapResponseDto> generateRouteMap(@RequestBody Map<String, Object> requestBody){
        GenerateRouteMapResponseDto responseDto = new GenerateRouteMapResponseDto();
        ObjectMapper objectMapper = new ObjectMapper();
        GenerateRouteMapDto dto = objectMapper.convertValue(requestBody, GenerateRouteMapDto.class);

        // 生成路线图
        // 根据传入的structCodes查询出所有的Structure对象
        List<Structure> structures = eruptDao.queryEntityList(Structure.class, "code in (:codes)", new HashMap<String, Object>() {{
            put("codes", dto.getStructCodes());
        }});
        // 转换为RouteMapService可接收的参数类型
        List<RouteMapService.Node> nodes = structures.stream().map(RouteMapService::convertStructToNode).collect(Collectors.toList());
        // 输出待创建路径的nodes
        log.info("待创建路径的nodes: " + nodes);

        // 1. 获取规则库（优先使用数据库规则，否则使用默认规则）
        RouteMapRuleService.AdvancedRuleLibrary ruleLibrary;
        try {
            // 查第一条规则
            RuleRouteMap ruleRouteMap = eruptDao.queryEntity(RuleRouteMap.class, "1 = 1", new HashMap<String, Object>() {{}});
            if (ruleRouteMap != null) {
                RuleRouteMapConverterService converterService = new RuleRouteMapConverterService();
                ruleLibrary = converterService.convertToAdvancedRuleLibrary(ruleRouteMap);
            } else {
                // 如果数据库中没有规则，使用默认规则
                RuleRouteMapConverterService converterService = new RuleRouteMapConverterService();
                ruleLibrary = converterService.getDefaultRuleLibrary();
            }
        } catch (Exception e) {
            log.warn("获取数据库规则失败，使用代码中的默认规则: " + e.getMessage());
            // 如果获取数据库规则失败，使用代码中的默认规则
            ruleLibrary = RouteMapRuleService.RuleLibraryBuilder.create()
                    .addConnectionRule("DEFAULT", "DEFAULT", 1)
                    .setMaxDistanceForConnection("DEFAULT", "DEFAULT", 10000000.0)
                    .setConnectionDirection("DEFAULT", "DEFAULT", RouteMapRuleService.ConnectionDirection.UNIDIRECTIONAL)
                    .build();
        }
        // 2. 使用优化算法计算路线
//        GenerateRouteMapResponseDto generateRouteMapResponseDto = RouteMapService.findConnections(nodes, ruleLibrary, 1);
        GenerateRouteMapResponseDto generateRouteMapResponseDto = RouteMapService.findConnectionsAdvanced(
                nodes, ruleLibrary, RouteMapRuleService.ConnectionStrategy.valueOf(ruleLibrary.getConnectionStrategy()));
        // 3.性能分析
//        RouteMapService.PerformanceReport report = RouteMapService.PerformanceAnalyzer.analyzePerformance(
//                nodes, ruleLibrary, RouteMapService.ConnectionStrategy.NEAREST_FIRST);

        responseDto.setRoutePathList(generateRouteMapResponseDto.getRoutePathList());

        // 返回生成的路线图
        return ResponseEntity.ok(responseDto);
    }

    // 根据创建Struct类型，由处理端生成起唯一标识并返回
    @PostMapping("rest/retrieveStructIdentityCode")
    public ResponseEntity<List<String>> retrieveStructIdentityCode(@RequestBody Map<String, Object> requestBody){
        String structType = (String) requestBody.get("structType");
        Long requestAmount = (Long) requestBody.get("requestAmount");
        String projectName = "DEFAULT"; // 假设项目名称为默认值，实际应用中可以从请求中获取或其他方式获取
        // 生成唯一标识
        List<String> uniqueCodes = structureDomainService.generateStructureCode(
                structType,
                projectName,
                requestAmount
        );
        // 返回唯一标识
        return ResponseEntity.ok(uniqueCodes);
    }

    // 接收交互端传过来的两个托架，计算托架顶面边缘之间计算电缆走线的最短路径，并返回每段线的起点/终点坐标
    @PostMapping("rest/retrieveTrayShortestPath")
    public ResponseEntity<PathResponseDto> retrieveTrayShortestPath(@RequestBody Map<String, Object> requestBody) {
        PathResponseDto pathResponseDto = new PathResponseDto();
        ObjectMapper objectMapper = new ObjectMapper();
        PathRequestDto dto = objectMapper.convertValue(requestBody, PathRequestDto.class);

        // 获取安装件信息
        Structure startStruct = eruptDao.queryEntity(Structure.class, "code = :code", new HashMap<String, Object>() {{
            put("code", dto.getStartStructCode());
        }});

        Structure endStruct = eruptDao.queryEntity(Structure.class, "code = :code", new HashMap<String, Object>() {{
            put("code", dto.getEndStructCode());
        }});

        // 使用CableRoutingSolverService， 通过startStruct结合Component定义的长，宽，高，计算这两个安装件之间顶面边缘之间计算电缆走线的最短路径，并返回每段线的起点/终点坐标PathResponseDto对象

        try {
            // 1. 解析起点和终点的中心坐标
            double[] startCoords = parseCoordinates(startStruct.getCog());
            double[] endCoords = parseCoordinates(endStruct.getCog());

            // 2. 获取起点和终点的Component信息
            com.example.tribon.domain.model.Component startComponent = getComponentByName(startStruct.getComponentName());
            com.example.tribon.domain.model.Component endComponent = getComponentByName(endStruct.getComponentName());

            // 3. 创建Tray对象列表
            List<CableRoutingSolverService.Tray> trays = new ArrayList<>();

            // 添加起点托架
            trays.add(new CableRoutingSolverService.Tray(
                0, // 起点ID
                startCoords[0], startCoords[1], startCoords[2], // 中心坐标
                startComponent.getLength() != null ? startComponent.getLength() : 100.0, // 长度，默认100
                startComponent.getWidth() != null ? startComponent.getWidth() : 50.0,   // 宽度，默认50
                startComponent.getHeight() != null ? startComponent.getHeight() : 20.0  // 高度，默认20
            ));

            // 添加终点托架
            trays.add(new CableRoutingSolverService.Tray(
                1, // 终点ID
                endCoords[0], endCoords[1], endCoords[2], // 中心坐标
                endComponent.getLength() != null ? endComponent.getLength() : 100.0, // 长度，默认100
                endComponent.getWidth() != null ? endComponent.getWidth() : 50.0,   // 宽度，默认50
                endComponent.getHeight() != null ? endComponent.getHeight() : 20.0  // 高度，默认20
            ));

            // 4. 使用CableRoutingSolverService计算最短路径
            List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);

            // 5. 转换为PathResponseDto
            if (!segments.isEmpty()) {
                // 取第一段线段的起点和终点
                CableRoutingSolverService.Segment firstSegment = segments.get(0);
                CableRoutingSolverService.Segment lastSegment = segments.get(segments.size() - 1);

                pathResponseDto.setStartPoint(new PathResponseDto.Point(
                    firstSegment.start.x,
                    firstSegment.start.y,
                    firstSegment.start.z
                ));

                pathResponseDto.setEndPoint(new PathResponseDto.Point(
                    lastSegment.end.x,
                    lastSegment.end.y,
                    lastSegment.end.z
                ));
            } else {
                // 如果没有找到路径，直接使用中心坐标
                pathResponseDto.setStartPoint(new PathResponseDto.Point(
                    startCoords[0], startCoords[1], startCoords[2]
                ));
                pathResponseDto.setEndPoint(new PathResponseDto.Point(
                    endCoords[0], endCoords[1], endCoords[2]
                ));
            }

        } catch (Exception e) {
            log.error("Error parsing coordinates: " + e.getMessage());
            // 异常处理：返回基于中心坐标的简单路径
            try {
                double[] startCoords = parseCoordinates(startStruct.getCog());
                double[] endCoords = parseCoordinates(endStruct.getCog());

                pathResponseDto.setStartPoint(new PathResponseDto.Point(
                    startCoords[0], startCoords[1], startCoords[2]
                ));
                pathResponseDto.setEndPoint(new PathResponseDto.Point(
                    endCoords[0], endCoords[1], endCoords[2]
                ));
            } catch (Exception ex) {
                // 最后的异常处理：返回默认坐标
                log.error("Error parsing coordinates: " + ex.getMessage());
            }
        }

        return ResponseEntity.ok(pathResponseDto);
    }



//
//    @PostMapping("rest/getPoints")
//    public ResponseEntity<List<Map<String, Double>>> getPoints(@RequestBody Map<String, String> requestBody) {
//        String startEquipment = requestBody.get("startEquipment");
//        String endEquipment = requestBody.get("endEquipment");
//
//        // Fetch start and end equipment coordinates using EruptDao
//        Struct startStruct = eruptDao.queryEntity(Struct.class, "name = :equipmentName", new HashMap<String, Object>() {{
//            put("equipmentName", startEquipment);
//        }});
//        Struct endStruct = eruptDao.queryEntity(Struct.class, "name = :equipmentName", new HashMap<String, Object>() {{
//            put("equipmentName", endEquipment);
//        }});
//
//        // Fetch all channels using EruptDao
//        List<Struct> channels = eruptDao.queryEntityList(Struct.class, "eqType = :eqType", new HashMap<String, Object>() {{
//            put("eqType", "Channel");
//        }});
//
//        // Calculate shortest path (this is a placeholder, implement your own logic)
//        List<Struct> path = calculateShortestPath(startStruct, endStruct, channels);
//
//        // Organize points into a list
//        List<Map<String, Double>> points = new ArrayList<>();
//        for (Struct struct : path) {
//            Map<String, Double> point = new HashMap<>();
//            point.put("X", struct.getCoordinateX());
//            point.put("Y", struct.getCoordinateY());
//            points.add(point);
//        }
//
//        return ResponseEntity.ok(points);
//    }
//
//    private List<Struct> calculateShortestPath(Struct start, Struct end, List<Struct> channels) {
//        List<Struct> path = new ArrayList<>();
//        path.add(start);
//        // 调用AI，计算返回一个最短路径的通道
//        // Demo验收效果为：两个设备相连，中间有多个通道选择，通过AI计算出一个或多个最短路径的通道
//        // 提示词最好可以修改，这里可以将所有设备和通道数据通过list方式提供给调用端，由调用端放入到提示词中，再调用AI
//        // 分析处理中心（提供起始设备和结束设备，以及备选通道列表） ->
//        // python Agent（编写提示词，提供rest接口接收设备和通道列表，组装提示词后调用本地AI） ->
//        // 本地AI（返回结构化数据） ->
//        // python Agent （解析结构化数据，返回给分析处理中心）->
//        // 分析处理中心（组装结果返回给client端）
//        path.addAll(channels);
//        path.add(end);
//
////        // 组装完整点位信息
//        List<Map<String, Double>> points = new ArrayList<>();
//        for (Struct struct : path) {
//            Map<String, Double> point = new HashMap<>();
//            point.put("X", struct.getCoordinateX());
//            point.put("Y", struct.getCoordinateY());
//            points.add(point);
//        }
//
//        try {
//            // 由Rule中获取api地址
//            Rule rule = eruptDao.queryEntity(Rule.class, "rullName = :rullName", new HashMap<String, Object>() {{
//            put("rullName", "缺省规则");
//            }});
//
//            // 组装提示词后
//            String fullPromote = rule.getRullPromote() + " " + points.toString();
//            String apiAddress = rule.getApiAddress();
//            String modelName = rule.getModelName();
//
//
//
//
//            // Create a RestTemplate instance
//            RestTemplate restTemplate = new RestTemplate();
//
//            // Create request body
//            Map<String, Object> requestBody = new HashMap<>();
//            requestBody.put("model", modelName);
//            List<Map<String, String>> messages = new ArrayList<>();
//            Map<String, String> message = new HashMap<>();
//            message.put("role", "user");
//            message.put("content", fullPromote);
//            messages.add(message);
//            requestBody.put("messages", messages);
//            requestBody.put("stream", false);
//
//            // Create headers
//            HttpHeaders headers = new HttpHeaders();
//            headers.set("Authorization", "Bearer sk-jnmkzoffqlhiuwhowjnzfhcdlimsxurrkfruryoynfoasuzy");
//            headers.setContentType(MediaType.APPLICATION_JSON);
//
//            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
//
//            // Make the REST call
//            ResponseEntity<String> responseEntity = restTemplate.postForEntity(apiAddress, requestEntity, String.class);
//
//            // Extract the response body
//            String responseBody = responseEntity.getBody();
//            if (responseBody != null) {
//                responseBody = responseBody.replaceAll("\\\\n", "").replaceAll("\\\\", "").replaceAll("\\t", "").replaceAll("u003c", "").replaceAll("u003e", "").replaceAll("tt", "").replaceAll("t\\{","\\{").replaceAll("t\"X", "\"X").replaceAll("t\\}", "\\}");
//            }
//            // Extract the result from the response
//            String result = "";
//            if (responseBody != null) {
//            int startIndex = responseBody.indexOf("resultstartresult") + "resultstartresult".length();
//            int endIndex = responseBody.indexOf("resultendresult") - 1;
//            if (startIndex != -1 && endIndex != -1) {
//                result = responseBody.substring(startIndex, endIndex);
//            }
//            }
//            // Validate and parse the result into a list of Struct objects
//            List<Struct> aiPath = parseResultToPath(result);
//            if (aiPath != null && !aiPath.isEmpty()) {
//                path = aiPath;
//            }
//            // Process the result as needed
//            // For example, you can parse it into a list of Struct objects or any other required format
//
//        } catch (Exception e) {
//            // Log the exception if needed
//            e.printStackTrace();
//            // Return the original path in case of any failure
//            return path;
//        }
//
//        return path;
//    }
//
//    private List<Struct> parseResultToPath(String result) {
//        List<Struct> path = new ArrayList<>();
//        try {
//            // Assuming the result is a JSON array of objects with coordinates
//            ObjectMapper objectMapper = new ObjectMapper();
//            List<Map<String, Double>> points = objectMapper.readValue(result, new TypeReference<List<Map<String, Double>>>() {});
//
//            for (Map<String, Double> point : points) {
//                Struct struct = new Struct();
////                struct.setCoordinateX(point.get("X"));
////                struct.setCoordinateY(point.get("Y"));
//                path.add(struct);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            // Return an empty path in case of any failure
//            return new ArrayList<>();
//        }
//        return path;
//    }

    /**
     * 解析坐标字符串为double数组
     * @param cogString 坐标字符串，格式如 "x,y,z"
     * @return double数组 [x, y, z]
     */
    private double[] parseCoordinates(String cogString) {
        if (cogString == null || cogString.trim().isEmpty()) {
            return new double[]{0.0, 0.0, 0.0};
        }

        String[] parts = cogString.split(",");
        if (parts.length != 3) {
            return new double[]{0.0, 0.0, 0.0};
        }

        try {
            return new double[]{
                Double.parseDouble(parts[0].trim()),
                Double.parseDouble(parts[1].trim()),
                Double.parseDouble(parts[2].trim())
            };
        } catch (NumberFormatException e) {
            return new double[]{0.0, 0.0, 0.0};
        }
    }

    /**
     * 根据Component名称获取Component对象
     * @param componentName Component名称
     * @return Component对象
     */
    private com.example.tribon.domain.model.Component getComponentByName(String componentName) {
        if (componentName == null || componentName.trim().isEmpty()) {
            return null;
        }

        try {
            com.example.tribon.domain.model.Component component = eruptDao.queryEntity(
                com.example.tribon.domain.model.Component.class,
                "code = :code",
                new HashMap<String, Object>() {{
                    put("code", componentName);
                }}
            );

            if (component != null) {
                return component;
            } else {
                // 如果找不到Component，返回默认值
                return null;
            }
        } catch (Exception e) {
            // 异常情况下返回默认Component
            return null;
        }
    }
}
