package com.example.tenant;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.upms.model.base.HyperModel;

import javax.persistence.*;


/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */

//ProductionOrder


@Table()
@Entity
@Erupt(name = "租户管理员",
        power = @Power(importable = false, export = false)
)
public class TenantAdmin extends MetaUser {

    @EruptField(
            views = @View(title = "管理员账号"),
            edit = @Edit(title = "管理员账号", notNull = true, search = @Search)
    )
    private String adminAccount;



}
