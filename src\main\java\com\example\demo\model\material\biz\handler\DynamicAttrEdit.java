package com.example.demo.model.material.biz.handler;

import org.springframework.stereotype.Service;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.upms.service.EruptUserService;

import javax.annotation.Resource;

@Service
public class DynamicAttrEdit implements ExprBool.ExprHandler{

    @Resource
    private EruptUserService eruptUserService;

    @Override
    public boolean handler(boolean expr, String[] params) {

        return false;
    }
}

