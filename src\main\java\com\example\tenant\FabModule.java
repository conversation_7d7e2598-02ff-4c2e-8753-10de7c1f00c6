package com.example.tenant;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.upms.model.base.HyperModel;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * date 2020/12/15 12:06
 */
@Erupt(
        name = "模块"
)
@Table(name = "fd_module")
@Entity
public class FabModule extends HyperModel {

    @EruptField(
            views = @View(title = "模块编码"),
            edit = @Edit(title = "模块编码", notNull = true, search = @Search(vague = true), inputType = @InputType(fullSpan = true))
    )
    private String code;

    @EruptField(
            views = @View(title = "模块名称"),
            edit = @Edit(title = "模块名称", notNull = true, search = @Search(vague = true), inputType = @InputType(fullSpan = true))
    )
    private String name;



}
