<!DOCTYPE html>
<html lang="zh">
<head>
    <base href="${base}/">
    <meta charset="UTF-8" />
    <title>amis demo</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"/>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <link rel="stylesheet" href="amis/sdk.css" />
    <!-- 从 1.1.0 开始 sdk.css 将不支持 IE 11，如果要支持 IE11 请引用这个 css，并把前面那个删了 -->
    <!-- <link rel="stylesheet" href="amis/sdk-ie11.css" /> -->
    <!-- 不过 amis 开发团队几乎没测试过 IE 11 下的效果，所以可能有细节功能用不了，如果发现请报 issue -->

    <!-- 百度云舍主题 -->
    <!-- <link href="amis/cxd.css" rel="stylesheet" /> -->
    <!-- 暗黑主题 -->
    <!-- <link href="amis/dark.css" rel="stylesheet" /> -->
    <!-- 仿 antd 主题 -->
    <!-- <link href="amis/antd.css" rel="stylesheet" /> -->

    <style>
        html, body, .app-wrapper {
            position: relative;
            width: 100%;
            height: 600px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
<div id="root" class="app-wrapper"></div>
<script src="amis/sdk.js"></script>
<script type="text/javascript">
    (function () {
        let amis = amisRequire('amis/embed');
        // 通过替换下面这个配置来生成不同页面
        let amisJSON ={
            "type": "page",
            "title": "Hello world",
            "body": [
                {
                    "type": "tabs",
                    "id": "u:ba42d1d9a91e",
                    "tabs": [
                        {
                            "title": "工序列表",
                            "body": [
                                {
                                    "id": "u:3e65ed37d420",
                                    "type": "crud2",
                                    "mode": "table2",
                                    "dsType": "api",
                                    "syncLocation": true,
                                    "primaryField": "id",
                                    "loadType": "pagination",
                                    "api": {
                                        "url": "/op/list/${rows[0].id}",
                                        "method": "get"
                                    },
                                    "headerToolbar": [
                                        {
                                            "type": "flex",
                                            "direction": "row",
                                            "justify": "flex-start",
                                            "alignItems": "stretch",
                                            "style": {
                                                "position": "static"
                                            },
                                            "items": [
                                                {
                                                    "type": "container",
                                                    "align": "left",
                                                    "behavior": [
                                                        "Insert",
                                                        "BulkEdit",
                                                        "BulkDelete"
                                                    ],
                                                    "body": [],
                                                    "wrapperBody": false,
                                                    "style": {
                                                        "flexGrow": 1,
                                                        "flex": "1 1 auto",
                                                        "position": "static",
                                                        "display": "flex",
                                                        "flexBasis": "auto",
                                                        "flexDirection": "row",
                                                        "flexWrap": "nowrap",
                                                        "alignItems": "stretch",
                                                        "justifyContent": "flex-start"
                                                    },
                                                    "id": "u:c5b43b278e73"
                                                },
                                                {
                                                    "type": "container",
                                                    "align": "right",
                                                    "behavior": [
                                                        "FuzzyQuery"
                                                    ],
                                                    "body": [],
                                                    "wrapperBody": false,
                                                    "style": {
                                                        "flexGrow": 1,
                                                        "flex": "1 1 auto",
                                                        "position": "static",
                                                        "display": "flex",
                                                        "flexBasis": "auto",
                                                        "flexDirection": "row",
                                                        "flexWrap": "nowrap",
                                                        "alignItems": "stretch",
                                                        "justifyContent": "flex-end"
                                                    },
                                                    "id": "u:71571b702749"
                                                }
                                            ],
                                            "id": "u:b065c5b08c33"
                                        }
                                    ],
                                    "footerToolbar": [
                                        {
                                            "type": "flex",
                                            "direction": "row",
                                            "justify": "flex-start",
                                            "alignItems": "stretch",
                                            "style": {
                                                "position": "static"
                                            },
                                            "items": [
                                                {
                                                    "type": "container",
                                                    "align": "left",
                                                    "body": [],
                                                    "wrapperBody": false,
                                                    "style": {
                                                        "flexGrow": 1,
                                                        "flex": "1 1 auto",
                                                        "position": "static",
                                                        "display": "flex",
                                                        "flexBasis": "auto",
                                                        "flexDirection": "row",
                                                        "flexWrap": "nowrap",
                                                        "alignItems": "stretch",
                                                        "justifyContent": "flex-start"
                                                    },
                                                    "id": "u:d0f0b3b21f62"
                                                },
                                                {
                                                    "type": "container",
                                                    "align": "right",
                                                    "body": [
                                                        {
                                                            "type": "pagination",
                                                            "behavior": "Pagination",
                                                            "layout": [
                                                                "total",
                                                                "perPage",
                                                                "pager"
                                                            ],
                                                            "perPage": 10,
                                                            "perPageAvailable": [
                                                                10,
                                                                20,
                                                                50,
                                                                100
                                                            ],
                                                            "align": "right",
                                                            "id": "u:f0d56235f68e"
                                                        }
                                                    ],
                                                    "wrapperBody": false,
                                                    "style": {
                                                        "flexGrow": 1,
                                                        "flex": "1 1 auto",
                                                        "position": "static",
                                                        "display": "flex",
                                                        "flexBasis": "auto",
                                                        "flexDirection": "row",
                                                        "flexWrap": "nowrap",
                                                        "alignItems": "stretch",
                                                        "justifyContent": "flex-end"
                                                    },
                                                    "id": "u:78b8de37c667"
                                                }
                                            ],
                                            "id": "u:6489a920341b"
                                        }
                                    ],
                                    "columns": [
                                        {
                                            "type": "tpl",
                                            "title": "工序编码",
                                            "name": "operationCode",
                                            "id": "u:371cd79297dc",
                                            "placeholder": "-",
                                            "copyable": false,
                                            "sorter": false,
                                            "searchable": false
                                        },
                                        {
                                            "type": "tpl",
                                            "title": "工序名称",
                                            "name": "operationName",
                                            "id": "u:016978db5aab"
                                        },
                                        {
                                            "type": "tpl",
                                            "title": "工序类型",
                                            "name": "operationType",
                                            "id": "u:147f6197a15c"
                                        }
                                    ],
                                    "editorSetting": {
                                        "mock": {
                                            "enable": true,
                                            "maxDisplayRows": 5
                                        }
                                    },
                                    "visible": true,
                                    "onEvent": {
                                        "rowClick": {
                                            "weight": 0,
                                            "actions": []
                                        }
                                    }
                                }
                            ],
                            "id": "u:e8b2cb40b7ae"
                        },
                        {
                            "title": "物料列表",
                            "body": [
                                {
                                    "type": "tpl",
                                    "tpl": "内容2",
                                    "wrapperComponent": "",
                                    "inline": false,
                                    "id": "u:634dd561dcb2"
                                }
                            ],
                            "id": "u:987eadd3a378"
                        }
                    ]
                }
            ],
            "id": "u:0ac78bc79300",
            "asideResizor": false,
            "pullRefresh": {
                "disabled": true
            },
            "regions": [
                "body",
                "toolbar"
            ],
            "toolbar": [
                {
                    "type": "button",
                    "label": "确认",
                    "onEvent": {
                        "click": {
                            "actions": [
                                {
                                    "ignoreError": false,
                                    "outputVar": "responseResult",
                                    "actionType": "ajax",
                                    "options": {
                                        "silent": false
                                    },
                                    "api": {
                                        "url": "/po/confirm",
                                        "method": "post",
                                        "requestAdaptor": "",
                                        "adaptor": "",
                                        "messages": {
                                            "success": "展开成功",
                                            "failed": "展开失败"
                                        },
                                        "dataType": "json",
                                        "data": {
                                            "id": "1"
                                        },
                                        "silent": false
                                    }
                                }
                            ]
                        }
                    },
                    "id": "u:ac187fe424ff"
                },
                {
                    "type": "button",
                    "label": "返回",
                    "onEvent": {
                        "click": {
                            "actions": []
                        }
                    },
                    "id": "u:89988737a550"
                }
            ],
            "actions": [
                {
                    "type": "button",
                    "actionType": "cancel",
                    "label": "取消",
                    "id": "u:a9c2f03ffe8d",
                    "close": true
                },
                {
                    "type": "button",
                    "actionType": "confirm",
                    "label": "确定",
                    "primary": true,
                    "id": "u:fad29a7843f3"
                }
            ]
        };
        let amisScoped = amis.embed('#root', amisJSON);
    })();
</script>
</body>
</html>