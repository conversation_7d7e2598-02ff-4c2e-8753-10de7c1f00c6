package com.example.wom.order.domain;


import com.example.demo.handler.OperationHandlerImpl;
import com.example.modeler.processroute.ProcessRoute;
import com.example.wom.order.domain.handler.OrderExpendOperationHandlerImpl;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.upms.handler.ViaMenuValueCtrl;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */
@Table(name = "mf_process_operation")
@Entity
@Erupt(name = "工单工序")
public class ProcessOperationOrder extends BaseModel {

    @EruptField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码", notNull = true, search = @Search)
    )
    private String operationCode;

    @EruptField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称", notNull = true, search = @Search)
    )
    private String operationName;

    @EruptField(
            views = @View(title = "工序类型"),
            edit = @Edit(title = "工序类型", notNull = true, search = @Search, type=EditType.CHOICE,
            choiceType = @ChoiceType(vl = {
                    @VL(value = "01", label="装配"),
                    @VL(value = "02", label="调试")
            })
            )
    )
    private String operationType;

    public String getOperationType() {
        return operationType;
    }
    public String getOperationName() {
        return operationName;
    }
    public String getOperationCode() {
        return operationCode;
    }
}
