<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.10</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <packaging>war</packaging>

    <groupId>com.example</groupId>
    <artifactId>erupt-example</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <description>Erupt Example project for Spring Boot</description>

    <properties>
        <java.version>1.8</java.version>
<!--        <erupt.version>1.112.11</erupt.version>-->
<!--        <erupt.version>1.112.11</erupt.version>-->
        <erupt.version>1.12.20</erupt.version>
<!--        <dubbo.version>3.3.0-beta.2</dubbo.version>-->

    </properties>


    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.29</version>
        </dependency>


        <!--核心管理模块-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-admin</artifactId>
            <version>${erupt.version}</version>
        </dependency>
        <!--后台WEB界面-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-web</artifactId>
            <version>${erupt.version}</version>
        </dependency>

        <!-- ****** 以下模块不需要可以去掉 ****** -->
        <!--erupt-cloud 云节点分布式控制模块-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-cloud-server</artifactId>
            <version>${erupt.version}</version>
        </dependency>
        <!--任务管理模块-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-job</artifactId>
            <version>${erupt.version}</version>
        </dependency>
        <!--代码生成器模块-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-generator</artifactId>
            <version>${erupt.version}</version>
        </dependency>
        <!--服务监控模块-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-monitor</artifactId>
            <version>${erupt.version}</version>
        </dependency>

        <!--在线接口开发-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-magic-api</artifactId>
            <version>${erupt.version}</version>
        </dependency>
        <!--自定义页面模块-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-tpl</artifactId>
            <version>${erupt.version}</version>
        </dependency>
        <!--element-ui-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-tpl-ui.element-ui</artifactId>
            <version>${erupt.version}</version>
        </dependency>
        <!--velocity-->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.3</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.github.snice</groupId>-->
<!--            <artifactId>erupt-pf4j</artifactId>-->
<!--            <version>0.0.1</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>xyz.erupt</groupId>
            <artifactId>erupt-tpl-ui.amis</artifactId>
            <version>${erupt.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.6.6</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.4</version>
            <scope>provided</scope>
        </dependency>
         <!-- dubbo -->
<!--         <dependency>-->
<!--             <groupId>org.apache.dubbo</groupId>-->
<!--             <artifactId>dubbo-spring-boot-starter</artifactId>-->
<!--             <version>${dubbo.version}</version>-->
<!--         </dependency>-->
<!--         <dependency>-->
<!--             <groupId>org.apache.dubbo</groupId>-->
<!--             <artifactId>dubbo-nacos-spring-boot-starter</artifactId>-->
<!--             <version>${dubbo.version}</version>-->
<!--         </dependency>-->
         <dependency>
             <groupId>org.jboss.resteasy</groupId>
             <artifactId>resteasy-jaxrs</artifactId>
             <version>3.15.6.Final</version>
             <exclusions>
                 <exclusion>
                     <groupId>org.apache.httpcomponents</groupId>
                     <artifactId>httpclient</artifactId>
                 </exclusion>
             </exclusions>
         </dependency>
         <dependency>
             <groupId>com.sun.xml.bind</groupId>
             <artifactId>jaxb-impl</artifactId>
             <version>2.3.1</version>
         </dependency>
         <dependency>
             <groupId>org.jboss.resteasy</groupId>
             <artifactId>resteasy-jackson-provider</artifactId>
             <version>3.0.19.Final</version>
             <scope>provided</scope>
         </dependency>
         <dependency>
             <groupId>org.jboss.resteasy</groupId>
             <artifactId>resteasy-jaxb-provider</artifactId>
             <version>3.0.9.Final</version>
             <scope>provided</scope>
         </dependency>
         <dependency>
             <groupId>javax.ws.rs</groupId>
             <artifactId>javax.ws.rs-api</artifactId>
             <version>2.1.1</version>
         </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!--        <dependency>-->
<!--            <groupId>org.apache.dubbo</groupId>-->
<!--            <artifactId>dubbo-dependencies-zookeeper-curator5</artifactId>-->
<!--            <version>3.3.0</version>-->
<!--            <type>pom</type>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>slf4j-reload4j</artifactId>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>nexus-aliyun</id>
            <name>nexus-aliyun</name>
            <url>http://maven.aliyun.com/nexus/content/repositories/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>
