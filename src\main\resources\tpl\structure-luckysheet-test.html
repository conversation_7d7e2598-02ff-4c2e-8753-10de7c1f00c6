<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Structure Luckysheet 测试页面</title>
    
    <!-- Luckysheet CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/css/pluginsCss.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/plugins.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/css/luckysheet.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/assets/iconfont/iconfont.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .header {
            background: #f5f5f5;
            padding: 10px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .luckysheet-container {
            width: 100%;
            height: calc(100vh - 60px);
            margin: 0;
            padding: 0;
        }
        
        .btn {
            padding: 8px 16px;
            background: #409EFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #66b1ff;
        }
        
        .btn-success {
            background: #67C23A;
        }
        
        .btn-success:hover {
            background: #85ce61;
        }
        
        .status {
            margin-left: auto;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <button class="btn" onclick="loadTestData()">加载测试数据</button>
        <button class="btn btn-success" onclick="saveData()">保存数据</button>
        <button class="btn" onclick="exportData()">导出数据</button>
        <div class="status">
            <span id="status">就绪</span>
        </div>
    </div>
    
    <!-- Luckysheet 容器 -->
    <div id="luckysheet" class="luckysheet-container"></div>

    <!-- Luckysheet JS -->
    <script src="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/js/plugin.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/luckysheet.umd.js"></script>

    <script>
        let luckysheetInstance = null;
        
        // 初始化 Luckysheet
        function initLuckysheet() {
            const options = {
                container: 'luckysheet',
                title: 'Structure 数据测试',
                lang: 'zh',
                allowCopy: true,
                allowEdit: true,
                allowUpdate: true,
                showToolbar: true,
                showInfoBar: false,
                showSheetBar: false,
                showStatisticBar: false,
                enableAddRow: true,
                enableAddCol: false,
                sheetBottomConfig: false,
                data: [{
                    name: 'Structure数据',
                    index: '0',
                    order: 0,
                    status: 1,
                    row: 20,
                    column: 15,
                    celldata: [
                        // 表头
                        {r: 0, c: 0, v: {v: 'ID', m: 'ID'}},
                        {r: 0, c: 1, v: {v: '唯一编号', m: '唯一编号'}},
                        {r: 0, c: 2, v: {v: '安装件名称', m: '安装件名称'}},
                        {r: 0, c: 3, v: {v: 'Component名称', m: 'Component名称'}},
                        {r: 0, c: 4, v: {v: '房间编号', m: '房间编号'}},
                        {r: 0, c: 5, v: {v: 'Volume名称', m: 'Volume名称'}},
                        {r: 0, c: 6, v: {v: '安装件类型', m: '安装件类型'}},
                        {r: 0, c: 7, v: {v: 'POI坐标', m: 'POI坐标'}},
                        {r: 0, c: 8, v: {v: 'ROT坐标', m: 'ROT坐标'}},
                        {r: 0, c: 9, v: {v: 'ROU坐标', m: 'ROU坐标'}},
                        {r: 0, c: 10, v: {v: 'COG坐标', m: 'COG坐标'}},
                        // 示例数据
                        {r: 1, c: 0, v: {v: '1', m: '1'}},
                        {r: 1, c: 1, v: {v: 'STR001', m: 'STR001'}},
                        {r: 1, c: 2, v: {v: '测试安装件1', m: '测试安装件1'}},
                        {r: 1, c: 3, v: {v: 'Component1', m: 'Component1'}},
                        {r: 1, c: 4, v: {v: 'ROOM001', m: 'ROOM001'}},
                        {r: 1, c: 5, v: {v: 'Volume1', m: 'Volume1'}},
                        {r: 1, c: 6, v: {v: '设备', m: '设备'}},
                        {r: 1, c: 7, v: {v: '100,200,300', m: '100,200,300'}},
                        {r: 1, c: 8, v: {v: '0,0,0', m: '0,0,0'}},
                        {r: 1, c: 9, v: {v: '100,200,300', m: '100,200,300'}},
                        {r: 1, c: 10, v: {v: '100,200,300', m: '100,200,300'}}
                    ]
                }],
                hook: {
                    cellEditBefore: (range) => {
                        // 第一列（ID列）不允许编辑
                        if (range && range.length > 0 && range[0].column && range[0].column[0] === 0) {
                            return false;
                        }
                        return true;
                    }
                }
            };
            
            luckysheet.create(options);
            luckysheetInstance = luckysheet;
            updateStatus('Luckysheet 初始化完成');
        }
        
        // 加载测试数据
        async function loadTestData() {
            updateStatus('加载数据中...');
            try {
                const response = await fetch('/api/structure-luckysheet/data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateLuckysheetData(data);
                    updateStatus('数据加载成功');
                } else {
                    updateStatus('数据加载失败: ' + response.statusText);
                }
            } catch (error) {
                updateStatus('数据加载失败: ' + error.message);
                console.error('加载数据失败:', error);
            }
        }
        
        // 更新 Luckysheet 数据
        function updateLuckysheetData(data) {
            try {
                if (data.sheets && data.sheets.length > 0) {
                    // 销毁现有实例
                    if (luckysheetInstance) {
                        luckysheet.destroy();
                    }
                    
                    // 重新创建实例
                    const options = {
                        container: 'luckysheet',
                        title: 'Structure 数据管理',
                        lang: 'zh',
                        allowCopy: true,
                        allowEdit: true,
                        allowUpdate: true,
                        showToolbar: true,
                        showInfoBar: false,
                        showSheetBar: false,
                        showStatisticBar: false,
                        enableAddRow: true,
                        enableAddCol: false,
                        sheetBottomConfig: false,
                        data: data.sheets,
                        hook: {
                            cellEditBefore: (range) => {
                                if (range && range.length > 0 && range[0].column && range[0].column[0] === 0) {
                                    return false;
                                }
                                return true;
                            }
                        }
                    };
                    
                    luckysheet.create(options);
                    luckysheetInstance = luckysheet;
                }
            } catch (error) {
                console.error('更新 Luckysheet 数据失败:', error);
                updateStatus('更新表格数据失败');
            }
        }
        
        // 保存数据
        async function saveData() {
            updateStatus('保存数据中...');
            try {
                const currentSheetData = getCurrentSheetData();
                const saveData = {
                    sheets: [currentSheetData],
                    title: 'Structure数据管理'
                };
                
                const response = await fetch('/api/structure-luckysheet/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(saveData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    updateStatus('保存成功: ' + result.message);
                } else {
                    updateStatus('保存失败: ' + response.statusText);
                }
            } catch (error) {
                updateStatus('保存失败: ' + error.message);
                console.error('保存数据失败:', error);
            }
        }
        
        // 获取当前工作表数据
        function getCurrentSheetData() {
            try {
                const sheetData = luckysheet.getSheetData();
                const celldata = [];
                
                if (sheetData && Array.isArray(sheetData)) {
                    for (let r = 0; r < sheetData.length; r++) {
                        const row = sheetData[r];
                        if (row && Array.isArray(row)) {
                            for (let c = 0; c < row.length; c++) {
                                const cell = row[c];
                                if (cell !== null && cell !== undefined && cell !== '') {
                                    celldata.push({
                                        r: r,
                                        c: c,
                                        v: {
                                            v: cell,
                                            m: cell.toString()
                                        }
                                    });
                                }
                            }
                        }
                    }
                }
                
                return {
                    name: 'Structure数据',
                    index: '0',
                    order: 0,
                    status: 1,
                    row: sheetData ? sheetData.length : 20,
                    column: sheetData && sheetData[0] ? sheetData[0].length : 15,
                    celldata: celldata
                };
            } catch (error) {
                console.error('获取工作表数据失败:', error);
                return {
                    name: 'Structure数据',
                    index: '0',
                    order: 0,
                    status: 1,
                    row: 20,
                    column: 15,
                    celldata: []
                };
            }
        }
        
        // 导出数据
        function exportData() {
            try {
                luckysheet.exportLuckyToExcel('Structure数据导出');
                updateStatus('导出成功');
            } catch (error) {
                updateStatus('导出失败: ' + error.message);
                console.error('导出失败:', error);
            }
        }
        
        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('状态:', message);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            setTimeout(initLuckysheet, 100);
        });
    </script>
</body>
</html>
