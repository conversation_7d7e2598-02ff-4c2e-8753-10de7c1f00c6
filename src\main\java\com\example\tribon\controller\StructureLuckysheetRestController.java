package com.example.tribon.controller;

import com.example.tribon.domain.model.Structure;
import com.example.tribon.dto.StructureLuckysheetDto;
import com.example.tribon.service.StructureLuckysheetService;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.util.*;

/**
 * Structure Luckysheet REST 控制器
 */
@RestController
public class StructureLuckysheetRestController {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private StructureLuckysheetService structureLuckysheetService;

    @PostMapping("api/structure-luckysheet/data")
    public ResponseEntity<Map<String, Object>> getStructureData(@RequestBody Map<String, Object> requestBody) {
        try {
            StringBuilder whereClause = new StringBuilder("1 = 1");
            Map<String, Object> params = new HashMap<>();

            String code = (String) requestBody.get("code");
            String name = (String) requestBody.get("name");
            String structType = (String) requestBody.get("structType");
            String roomCode = (String) requestBody.get("roomCode");

            if (code != null && !code.trim().isEmpty()) {
                whereClause.append(" AND code LIKE :code");
                params.put("code", "%" + code.trim() + "%");
            }
            if (name != null && !name.trim().isEmpty()) {
                whereClause.append(" AND name LIKE :name");
                params.put("name", "%" + name.trim() + "%");
            }
            if (structType != null && !structType.trim().isEmpty()) {
                whereClause.append(" AND structType = :structType");
                params.put("structType", structType.trim());
            }
            if (roomCode != null && !roomCode.trim().isEmpty()) {
                whereClause.append(" AND roomCode = :roomCode");
                params.put("roomCode", roomCode.trim());
            }

            List<Structure> structures = eruptDao.queryEntityList(
                Structure.class, 
                whereClause.toString(), 
                params
            );

            Map<String, Object> luckysheetData = structureLuckysheetService.convertToLuckysheetFormat(structures);
            return ResponseEntity.ok(luckysheetData);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取数据失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @PostMapping("api/structure-luckysheet/save")
    public ResponseEntity<Map<String, Object>> saveStructureData(@RequestBody Map<String, Object> luckysheetData) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<StructureLuckysheetDto> dtos = structureLuckysheetService.convertFromLuckysheetFormat(luckysheetData);
            int updatedCount = structureLuckysheetService.saveStructures(dtos);
            
            response.put("success", true);
            response.put("message", "保存成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("api/structure-luckysheet/struct-types")
    public ResponseEntity<List<String>> getStructTypes() {
        try {
            List<Structure> structures = eruptDao.queryEntityList(
                Structure.class,
                "structType IS NOT NULL AND structType != ''",
                new HashMap<>()
            );
            
            Set<String> typeSet = new HashSet<>();
            for (Structure structure : structures) {
                if (structure.getStructType() != null && !structure.getStructType().trim().isEmpty()) {
                    typeSet.add(structure.getStructType());
                }
            }
            
            List<String> structTypes = new ArrayList<>(typeSet);
            Collections.sort(structTypes);
            return ResponseEntity.ok(structTypes);
        } catch (Exception e) {
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @PostMapping("api/structure-luckysheet/room-codes")
    public ResponseEntity<List<String>> getRoomCodes() {
        try {
            List<Structure> structures = eruptDao.queryEntityList(
                Structure.class,
                "roomCode IS NOT NULL AND roomCode != ''",
                new HashMap<>()
            );
            
            Set<String> roomSet = new HashSet<>();
            for (Structure structure : structures) {
                if (structure.getRoomCode() != null && !structure.getRoomCode().trim().isEmpty()) {
                    roomSet.add(structure.getRoomCode());
                }
            }
            
            List<String> roomCodes = new ArrayList<>(roomSet);
            Collections.sort(roomCodes);
            return ResponseEntity.ok(roomCodes);
        } catch (Exception e) {
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }
}
