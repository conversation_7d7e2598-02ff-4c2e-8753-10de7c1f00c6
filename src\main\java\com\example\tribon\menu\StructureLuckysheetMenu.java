package com.example.tribon.menu;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Structure Luckysheet 菜单配置
 * 用于在 Erupt 框架中显示自定义页面菜单
 */
@Erupt(
    name = "Structure表格编辑",
    desc = "基于Luckysheet的Structure数据管理",
    linkTree = @xyz.erupt.annotation.sub_erupt.LinkTree(field = "menuType"),
    orderBy = "sort asc"
)
@Table(name = "structure_luckysheet_menu")
@Entity
public class StructureLuckysheetMenu extends BaseModel {

    @EruptField(
        views = @View(title = "菜单名称"),
        edit = @Edit(title = "菜单名称", notNull = true)
    )
    private String menuName = "Structure表格编辑";

    @EruptField(
        views = @View(title = "菜单类型"),
        edit = @Edit(title = "菜单类型")
    )
    private String menuType = "CUSTOM";

    @EruptField(
        views = @View(title = "排序"),
        edit = @Edit(title = "排序")
    )
    private Integer sort = 100;

    @EruptField(
        views = @View(title = "页面路径"),
        edit = @Edit(title = "页面路径")
    )
    private String pagePath = "/tpl/structure-luckysheet.html";

    @EruptField(
        views = @View(title = "图标"),
        edit = @Edit(title = "图标")
    )
    private String icon = "fa fa-table";

    @EruptField(
        views = @View(title = "描述"),
        edit = @Edit(title = "描述")
    )
    private String description = "基于Luckysheet的Structure数据管理，支持表格编辑、查询过滤、批量操作等功能";

    // Getters and Setters
    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getPagePath() {
        return pagePath;
    }

    public void setPagePath(String pagePath) {
        this.pagePath = pagePath;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
