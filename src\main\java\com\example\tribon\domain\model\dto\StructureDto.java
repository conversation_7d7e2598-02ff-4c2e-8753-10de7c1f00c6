package com.example.tribon.domain.model.dto;

import com.example.tribon.domain.model.Structure;
import lombok.AllArgsConstructor;
import lombok.Value;

import java.io.Serializable;

/**
 * DTO for {@link Structure}
 */
@Value
@AllArgsConstructor
public class StructureDto implements Serializable {
//    Long cogXMarkHandle1;
//    Long cogXMarkHandle2;
    String cogXMark1StructCode;
    String cogXMark2StructCode;

}