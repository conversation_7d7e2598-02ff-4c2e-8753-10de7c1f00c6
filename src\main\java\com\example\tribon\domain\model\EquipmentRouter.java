package com.example.tribon.domain.model;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(
        name = "设备路径"
)
@Table(name = "tribon_equipment_router"
)
@Entity
@Getter
@Setter
public class EquipmentRouter extends BaseModel {



    @EruptField(
            views = @View(title = "前一结构件"),
            edit = @Edit(title = "前一结构件", search = @Search(vague = true))
    )
    private String name;

    @EruptField(
            views = @View(title = "partId"),
            edit = @Edit(title = "partId", notNull = true, search = @Search(vague = true))
    )
    private String partId;

    @EruptField(
            views = @View(title = "type"),
            edit = @Edit(title = "type", notNull = true, search = @Search(vague = true))
    )
    private String type;

    @EruptField(
            views = @View(title = "partType"),
            edit = @Edit(title = "partType", notNull = true, search = @Search(vague = true))
    )
    private String partType;

}
