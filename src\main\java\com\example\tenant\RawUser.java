package com.example.tenant;

import lombok.Data;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.InputType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.upms.model.base.HyperModel;

import javax.persistence.*;


/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */

//ProductionOrder

@Table(name = "fd_raw_user")
@Entity
@Inheritance(strategy = InheritanceType.JOINED)
@Data
public class RawUser extends HyperModel {

    @EruptSmartSkipSerialize
    public String account;

    @EruptSmartSkipSerialize
    public String name;

    @EruptSmartSkipSerialize
    public String password;

    @EruptSmartSkipSerialize
    public Boolean tenantAdminFlag;

    public Boolean checkIfTenantAdmin() {

        return tenantAdminFlag;
    }


}

