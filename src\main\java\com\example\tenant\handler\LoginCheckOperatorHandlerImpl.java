package com.example.tenant.handler;


import com.example.tenant.RawUser;
//import com.example.tenant.RawUserAdmin;
import com.example.tenant.RawUserOperator;
import com.example.wom.order.domain.ProductionOrder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.view.EruptApiModel;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-10.
 */
@Component
@Service
@RequiredArgsConstructor
public class LoginCheckOperatorHandlerImpl implements OperationHandler<Long, LoginCheckOperator> {

    @Resource
    private HttpServletRequest request; //展示自动注入功能

    @Resource
    private EruptDao eruptDao;

    @Override
    public String exec(List<Long> data, LoginCheckOperator loginCheckOperator, String[] param) {
        RawUser rawUser = eruptDao.findById(RawUser.class, loginCheckOperator.getUserId());
        boolean checkResult = rawUser.checkIfTenantAdmin();
        return "xxx";
    }
}
