package com.example.productionPlan_api.model;

import com.example.demo.handler.OperationHandlerImpl;
import com.example.modeler.processroute.ProcessRoute;
import com.example.wom.order.domain.handler.OrderExpendOperationHandlerImpl;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.upms.handler.ViaMenuValueCtrl;

import javax.persistence.Entity;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.util.Date;


@Table(name = "bm_wom_plan_year")
@Entity
@Erupt(name = "年生产计划",
        power = @Power(importable = false, export = true)
)
public class ProductionPlanYear extends BaseModel {

    @EruptField(
            views = @View(title = "生产计划号"),
            edit = @Edit(title = "生产计划号", notNull = true, search = @Search)
    )
    private String productionPlanNumber;

    @EruptField(
            views = @View(title = "产品类别"),
            edit = @Edit(title = "产品类别", notNull = true, search = @Search)
    )
    private String productionCategory;

    @EruptField(
            views = @View(title = "计划生产数量", sortable = true),
            edit = @Edit(title = "计划生产数量", search = @Search)
    )
    private Float productionRequestQuantity;

    @EruptField(
            views = @View(title = "年"),
            edit = @Edit(title = "年")
    )
    private String year;

    @EruptField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", notNull = true, search = @Search, type=EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "EDIT", label="新建"),
                            @VL(value = "NEW", label="展开"),
                            @VL(value = "ALLSET", label="齐套"),
                            @VL(value = "ACTIVE", label="下达"),
                            @VL(value = "COMPLETE", label="完工"),
                    })
            )
    )
    private String planState;

    private void setplanState(String planState) {
        this.planState = planState;
    }


}
