package com.example.demo.model.material.biz.domain.model;

import com.example.demo.model.material.biz.domain.proxy.MaterialCategoryDataProxy;
import com.example.demo.model.namingrule.biz.domain.namingRule.proxy.NamingRuleParameterDataProxy;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_erupt.Tree;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.annotation.sub_field.sub_edit.ShowBy;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;

@Entity
@Table(name = "material_category",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
@Erupt(
        name = "物料类别", dataProxy = MaterialCategoryDataProxy.class,
        orderBy = "MaterialCategory.sort",
        tree = @Tree(pid = "parentCategory.id", expandLevel = 4)
)
@Getter
@Setter
public class MaterialCategory extends BaseModel {

    @EruptField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", notNull = true)
    )
    private String code;

    @EruptField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true)
    )
    private String name;

    @EruptField(
            views = @View(title = "显示顺序"),
            edit = @Edit(title = "显示顺序")
    )
    private Integer sort;

    @ManyToOne
    @EruptField(
            edit = @Edit(
                    title = "上级物料类别",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id", expandLevel = 4)
            )
    )
    private MaterialCategory parentCategory;

    @EruptField(
            edit = @Edit(title = "级别", show = false),
            views = @View(title = "级别", show = true)
    )
    @Comment("逻辑字段，生成的当前类别的级别")
    private Integer categoryLevel;

    @EruptField(
            views = @View(title = "保管员"),
            edit = @Edit(title = "保管员", notNull = false)
    )
    private String storeKeeper;

    @EruptField(
            edit = @Edit(title = "描述", notNull = false,
                    type = EditType.TEXTAREA)
    )
    private String desciption;

    @OneToOne
    @EruptField(
            views = @View(title = "物料属性模板"),
            edit = @Edit(title = "物料属性模板", notNull = false, showBy = @ShowBy(dependField = "categoryLevel", expr = "value == 2")
            )
    )
    private MaterialAttributeTemplate materialAttributeTemplate;


}
