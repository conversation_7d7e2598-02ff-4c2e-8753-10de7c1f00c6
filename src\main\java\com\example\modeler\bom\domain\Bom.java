package com.example.modeler.bom.domain;

import com.example.modeler.processroute.ProcessRoute;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Tree;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTableType;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;
import java.util.Set;

@Table(name = "ms_bom")
@Entity
@Erupt(name="BOM",
        orderBy = "Bom.sort",
        tree = @Tree(pid = "parent.id")
)
public class Bom extends BaseModel {
    @EruptField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", notNull = true)
    )
    private String name;

    @OneToOne
    @EruptField(
            views = @View(title = "工艺路线", column = "routeCode"),
            edit = @Edit(title = "工艺路线", notNull = true, search = @Search,
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(label = "routeCode")
            )
    )
    private ProcessRoute processRoute;

    @EruptField(
            views = @View(title = "Bom版本"),
            edit = @Edit(title = "Bom版本", notNull = true)
    )
    private String bomVersion;

    @EruptField(
            views = @View(title = "生产目的"),
            edit = @Edit(title = "生产目的", notNull = false)
    )
    private String productionPurpose;

    @EruptField(
            views = @View(title = "显示顺序"),
            edit = @Edit(title = "显示顺序")
    )
    private Integer sort;

    @ManyToOne
    @EruptField(
            edit = @Edit(
                    title = "上级树节点",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id")
            )
    )
    private Bom parent;

    @OneToMany
    @EruptField(
            edit = @Edit(title = "BOM子项", notNull = true, search = @Search, type = EditType.TAB_TABLE_REFER
            )
    )
    private Set<BomList> bomList;
}
