package com.example.modeler.processroute;


import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameterSrValue;
import com.example.modeler.material.domain.Material1;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTableType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.ShowBy;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */
@Table(name = "ms_process_route")
@Entity
@Erupt(name = "工艺路线", power = @Power(importable = false, export = false))
public class ProcessRoute extends BaseModel {

    @EruptField(
            views = @View(title = "工艺路线编码"),
            edit = @Edit(title = "工艺路线编码", notNull = true, search = @Search)
    )
    private String routeCode;

//    @OneToOne
    @ManyToMany
    @EruptField(
            views = @View(title = "物料编码", column = "materialCode")
//            edit = @Edit(title = "物料编码", notNull = true, search = @Search, type=EditType.TAB_TABLE_REFER
//            referenceTableType = @ReferenceTableType(label = "materialCode"))

    )
    private Set<Material1> materialCode;

    @ManyToMany
    @EruptField(
            edit = @Edit(title = "工序编码", notNull = true, search = @Search, type = EditType.TAB_TABLE_REFER
            )
    )
    private Set<ProcessOperation> processOperations;


    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "process_routeoperationrelation_id")
    @EruptField(
            views = @View(title = "关联工序", column = "processRouteOperationRelations"),
            edit = @Edit(title = "关联工序",
                    type = EditType.TAB_TABLE_ADD)
    )
    @Comment("工艺下关联工序附带其他信息")
    private Set<ProcessRouteOperationRelation> processRouteOperationRelations;

    public Set<ProcessOperation> getProcessOperations(){
        return this.processOperations;
    }
}
