package com.example.tribon.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * Structure Luckysheet DTO
 * 用于 Luckysheet 与 Structure 实体之间的数据传输
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StructureLuckysheetDto {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 唯一编号
     */
    private String code;
    
    /**
     * 安装件名称
     */
    private String name;
    
    /**
     * Component名称
     */
    private String componentName;
    
    /**
     * 所属房间编号
     */
    private String roomCode;
    
    /**
     * Volume Name
     */
    private String volumeName;
    
    /**
     * 安装件类型
     */
    private String structType;
    
    /**
     * 三维空间坐标 POI
     */
    private String poi;
    
    /**
     * 旋转坐标 ROT
     */
    private String rot;
    
    /**
     * 路由坐标 ROU
     */
    private String rou;
    
    /**
     * 重心坐标 COG
     */
    private String cog;
    
    /**
     * 重心X标记1结构编码
     */
    private String cogXMark1StructCode;
    
    /**
     * 重心X标记2结构编码
     */
    private String cogXMark2StructCode;
    
    /**
     * 行号（用于 Luckysheet 定位）
     */
    private Integer rowIndex;
    
    /**
     * 是否为新增记录
     */
    private Boolean isNew = false;
    
    /**
     * 是否已删除
     */
    private Boolean isDeleted = false;
    
    /**
     * 是否已修改
     */
    private Boolean isModified = false;
    
    /**
     * 创建时间字符串
     */
    private String createTime;
    
    /**
     * 更新时间字符串
     */
    private String updateTime;
}
