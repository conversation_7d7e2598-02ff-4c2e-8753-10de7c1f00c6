# 连接优先级算法修复

## 🎯 问题分析

### ❌ 原始问题
用户发现将连接数量限制的判断放在 `findConnectionCandidates` 方法中是不正确的，因为：

1. **时机错误**：在候选查找阶段就限制连接数，但后续的去重和选择策略可能会移除高优先级的连接
2. **逻辑错误**：应该按优先级逐个处理，每种类型达到限制后再处理下一优先级
3. **结果不符合预期**：无法保证按优先级正确获得希望的连接结果

### 🔍 核心需求
- **按优先级处理**：优先级1的连接类型先处理，达到限制后再处理优先级2
- **类型限制**：每种连接类型都有自身的连接数限制
- **总数限制**：同时要遵守总的连接数限制
- **距离约束**：每种连接类型都有最大距离限制

## ✅ 解决方案

### 1. 算法重构
将原来的单一候选查找+选择模式改为按优先级逐步处理模式：

```java
// 原来的方式（有问题）
List<ConnectionCandidate> candidates = findConnectionCandidates(...);
List<ConnectionCandidate> selected = selectOptimalConnections(candidates, ...);

// 新的方式（正确）
processConnectionsByPriority(src, connMap, kdByRoom, ruleLibrary, 
    kPerPriority, processedConnections, result);
```

### 2. 核心算法逻辑

#### 步骤1：按优先级分组
```java
// 按优先级分组连接规则
TreeMap<Integer, Set<String>> byPriority = new TreeMap<>();
connMap.forEach((type, priority) ->
    byPriority.computeIfAbsent(priority, p -> new HashSet<>()).add(type));
```

#### 步骤2：逐优先级处理
```java
// 记录每种连接类型的已连接数量
Map<String, Integer> connectionCounts = new HashMap<>();
int totalConnections = 0;

// 按优先级逐个处理
for (Map.Entry<Integer, Set<String>> entry : byPriority.entrySet()) {
    int priority = entry.getKey();
    Set<String> typesAtThisPriority = entry.getValue();
    
    // 处理当前优先级的所有类型...
}
```

#### 步骤3：类型内连接分配
```java
// 为每种类型分配连接，直到达到该类型的限制
for (String targetType : typesAtThisPriority) {
    Integer connectionLimit = ruleLibrary.getConnectionLimitForConnection(src.getType(), targetType);
    int currentCount = connectionCounts.getOrDefault(targetType, 0);
    
    // 为当前类型选择连接，直到达到限制
    for (ConnectionCandidate candidate : priorityCandidates) {
        if (!candidate.getTarget().getType().equals(targetType)) continue;
        if (currentCount >= connectionLimit) break;  // 类型限制
        if (totalConnections >= kPerPriority) break; // 总数限制
        
        // 创建连接并更新计数
        // ...
    }
}
```

### 3. 完整的处理流程

```mermaid
graph TD
    A[开始处理节点] --> B[获取连接规则]
    B --> C[按优先级分组]
    C --> D[获取最近邻候选]
    D --> E[处理优先级1]
    E --> F{类型1达到限制?}
    F -->|否| G[添加类型1连接]
    F -->|是| H[处理类型2]
    G --> I{总连接数达到限制?}
    I -->|否| F
    I -->|是| K[结束]
    H --> J{所有类型处理完?}
    J -->|否| H
    J -->|是| L[处理优先级2]
    L --> M{总连接数达到限制?}
    M -->|否| L
    M -->|是| K
```

## 🔧 具体实现

### 1. 新增方法：processConnectionsByPriority

```java
private static void processConnectionsByPriority(
        Node src, Map<String, Integer> connMap, Map<String, KdTree> kdByRoom,
        RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int kPerPriority,
        Set<String> processedConnections, List<GenerateRouteMapResponseDto.RoutePathDto> result) {
    
    // 1. 按优先级分组
    TreeMap<Integer, Set<String>> byPriority = new TreeMap<>();
    
    // 2. 获取候选节点
    List<Node> nearestNodes = tree.findKNearest(src, searchCount);
    
    // 3. 按优先级逐个处理
    for (Map.Entry<Integer, Set<String>> entry : byPriority.entrySet()) {
        // 4. 为每种类型分配连接
        for (String targetType : typesAtThisPriority) {
            // 5. 检查类型限制和总数限制
            // 6. 创建连接并更新计数
        }
    }
}
```

### 2. 辅助方法：isConnectionProcessed 重载

```java
// 原有方法
private static boolean isConnectionProcessed(ConnectionCandidate candidate, Set<String> processedConnections)

// 新增重载方法
private static boolean isConnectionProcessed(Node source, Node target, Set<String> processedConnections)
```

## 📊 算法特性

### 1. 优先级保证
- ✅ 严格按优先级顺序处理
- ✅ 高优先级类型优先获得连接机会
- ✅ 低优先级类型在高优先级未满足时不会被选择

### 2. 数量限制
- ✅ 每种连接类型独立的数量限制
- ✅ 总连接数的全局限制
- ✅ 达到限制后自动停止该类型的连接

### 3. 距离约束
- ✅ 每种连接类型独立的最大距离限制
- ✅ 超出距离的候选自动过滤
- ✅ 在距离范围内按距离排序选择

### 4. 去重机制
- ✅ 避免重复连接（A→B 和 B→A）
- ✅ 全局连接状态跟踪
- ✅ 双向连接记录

## 🧪 测试验证

### 1. 优先级测试
```java
@Test
void testConnectionLimitsByPriority() {
    // 设置：设备 → 扁条(优先级1,限制1) → 托架(优先级2,限制1) → 支架(优先级3,限制1)
    // 验证：应该优先连接扁条，然后托架，最后支架
}
```

### 2. 数量限制测试
```java
// 验证每种类型的连接数不超过配置的限制
assertTrue(connectionTypeCount.getOrDefault("扁条", 0) <= 1);
assertTrue(connectionTypeCount.getOrDefault("托架", 0) <= 1);
```

### 3. 距离约束测试
```java
// 验证所有连接都在最大距离限制内
assertTrue(distance <= maxDistanceForConnection);
```

## 🎯 效果对比

### 修复前
```
问题：可能出现低优先级连接占用了连接名额，高优先级连接反而被排除
结果：设备可能连接到支架(优先级3)，但没有连接到扁条(优先级1)
```

### 修复后
```
保证：严格按优先级分配连接，高优先级类型优先满足
结果：设备首先连接扁条(优先级1)，达到限制后再考虑托架(优先级2)
```

## 📋 配置示例

```java
// 规则配置
AdvancedRuleLibrary rules = RuleLibraryBuilder.create()
    .addConnectionRule("设备", "扁条", 1)    // 优先级1，最高优先级
    .addConnectionRule("设备", "托架", 2)    // 优先级2
    .addConnectionRule("设备", "支架", 3)    // 优先级3，最低优先级
    .setConnectionLimitForConnection("设备", "扁条", 2)  // 扁条最多连2个
    .setConnectionLimitForConnection("设备", "托架", 1)  // 托架最多连1个
    .setConnectionLimitForConnection("设备", "支架", 1)  // 支架最多连1个
    .setMaxDistanceForConnection("设备", "扁条", 500.0)  // 扁条最大距离500
    .build();

// 处理结果
// 1. 首先尝试连接2个最近的扁条（优先级1）
// 2. 如果总连接数未达到限制，再连接1个最近的托架（优先级2）
// 3. 如果总连接数仍未达到限制，再连接1个最近的支架（优先级3）
```

## 🚀 总结

这次修复解决了连接优先级算法的核心问题：

1. **✅ 正确的处理时机**：在最终选择阶段而不是候选查找阶段应用连接限制
2. **✅ 严格的优先级顺序**：确保高优先级类型优先获得连接机会
3. **✅ 精确的数量控制**：每种类型独立限制，同时遵守总数限制
4. **✅ 可预测的结果**：算法行为符合用户的直观预期

现在的算法能够确保按照用户配置的优先级和数量限制，生成最符合业务需求的连接结果。
