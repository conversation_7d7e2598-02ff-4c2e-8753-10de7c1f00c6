package com.example.tribon.dto;

import lombok.Data;

import java.util.List;

@Data
/**
 * 定义接收来自交互端创建关系的对象结构
 */
public class CreateRouteMapDto {
    private List<RoutePathDto> routePathList;

    @Data
    public static class RoutePathDto {
        private RouteNodeDto startRouteNode;
        private RouteNodeDto endRouteNode;
        private String relationEdgeFlagStructCode;
        private List<Integer> relationHandles;
    }

    @Data
    public static class RouteNodeDto {
        private String baseOnStructCode;
        private String flagX1StructCode;
        private List<Integer> flagX1Handles;
        private String flagX2StructCode;
        private List<Integer> flagX2Handles;
    }


}
