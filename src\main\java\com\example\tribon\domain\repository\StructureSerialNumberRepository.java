package com.example.tribon.domain.repository;

import com.example.tribon.domain.model.StructureSerialNumber;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.LockModeType;

public interface StructureSerialNumberRepository extends JpaRepository<StructureSerialNumber, Long> {
    // 写一个hql的查询方法，根据项目名称和结构类型获取当前序号
    @Query("SELECT max(s.currentSerialNumber) FROM StructureSerialNumber s WHERE s.projectName = ?1 AND s.structureType = ?2")
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Long findByProjectNameAndStructureType(String projectName, String structureType);
}