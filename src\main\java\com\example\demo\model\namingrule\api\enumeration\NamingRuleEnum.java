package com.example.demo.model.namingrule.api.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import xyz.erupt.annotation.fun.ChoiceFetchHandler;
import xyz.erupt.annotation.fun.VLModel;

import java.util.ArrayList;
import java.util.List;

public class NamingRuleEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getTitle()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        CS("常量"),
        VA("变量"),
        SR("顺序号"),
        D2("2位日"),
        M2("2位月"),
        Y2("2位年"),
        Y4("4位年");

        private final String title;

    }
}
