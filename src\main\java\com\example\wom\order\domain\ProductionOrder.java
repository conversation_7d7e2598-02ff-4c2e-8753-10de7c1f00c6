package com.example.wom.order.domain;

import com.example.demo.handler.OperationHandlerImpl;
import com.example.demo.model.complex.ComplexOperator;
import com.example.modeler.processroute.ProcessRoute;
import com.example.wom.order.domain.handler.OrderExpendOperationHandlerImpl;
import com.example.wom.order.domain.handler.OrderRelease1OperationHandlerImpl;
import com.example.wom.order.domain.handler.OrderRelease2OperationHandlerImpl;
import org.hibernate.criterion.Order;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.LinkTree;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.upms.handler.ViaMenuValueCtrl;
import com.example.wom.order.domain.handler.OrderExpendOperator;

import javax.persistence.Entity;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.util.Date;



/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */

//ProductionOrder


@Table(name = "bm_wom_ordr")
@Entity
@Erupt(name = "生产工单",
        power = @Power(importable = false, export = true),
        rowOperation = {
                @RowOperation(
                        type = RowOperation.Type.TPL,
                        tpl = @Tpl(path = "/tpl/amis.html", width = "80%"),
                        operationHandler = OrderExpendOperationHandlerImpl.class,
                        mode = RowOperation.Mode.MULTI,
                        show = @ExprBool(
                                exprHandler = ViaMenuValueCtrl.class, //根据菜单类型值控制是否显示的实现类
                                params = "orderCMDExpend"  //权限标识，菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        callHint = "确定要执行吗？",
                        title = "展开"),
                @RowOperation(
//                        eruptClass = OrderExpendOperator.class,
                        eruptClass = ProcessRoute.class,
                        operationHandler = OrderExpendOperationHandlerImpl.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "确定要执行吗？",
                        title = "拆分"),
                @RowOperation(
                        operationHandler = OperationHandlerImpl.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "确定要执行吗？",
                        title = "齐套"),
                @RowOperation(
                        operationHandler = OperationHandlerImpl.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "确定要执行吗？",
                        title = "齐套查看"),
                @RowOperation(
                        operationHandler = OrderRelease1OperationHandlerImpl.class,
                        mode = RowOperation.Mode.BUTTON,
                        callHint = "确定要执行吗？",
                        show = @ExprBool(
                        exprHandler = ViaMenuValueCtrl.class, //根据菜单类型值控制是否显示的实现类
                        params = "orderCMD1"  //权限标识，菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        eruptClass = ProductionOrderP1.class,
                        title = "工单下达-类型1"),
                @RowOperation(
                        operationHandler = OrderRelease2OperationHandlerImpl.class,
                        mode = RowOperation.Mode.BUTTON,
                        callHint = "确定要执行吗？",
                        show = @ExprBool(
                                exprHandler = ViaMenuValueCtrl.class, //根据菜单类型值控制是否显示的实现类
                                params = "orderCMD2"  //权限标识，菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        eruptClass = ProductionOrderP2.class,
                        title = "工单下达-类型2"),
        }
)
public class ProductionOrder extends BaseModel {

    @EruptField(
            views = @View(title = "生产工单号"),
            edit = @Edit(title = "生产工单号", notNull = true, search = @Search)
    )
    private String productionOrderNumber;

    @OneToOne
    @EruptField(
            views = @View(title = "工艺路线", column = "routeCode"),
            edit = @Edit(title = "工艺路线", notNull = true, search = @Search,
            type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(label = "routeCode")
            )
    )
    private ProcessRoute processRoute;

    @EruptField(
            views = @View(title = "计划生产数量", sortable = true),
            edit = @Edit(title = "计划生产数量", search = @Search(vague = true))
    )
    private Float productionRequestQuantity;

    @EruptField(
            views = @View(title = "是否完工"),
            edit = @Edit(title = "是否完工")
    )
    private Boolean bool;

    @EruptField(
            views = @View(title = "计划开始时间"),
            edit = @Edit(title = "计划时间", search = @Search(vague = true))
    )
    private Date startDate;


    @EruptField(
            views = @View(title = "计划完成时间"),
            edit = @Edit(title = "计划完成时间", search = @Search(vague = true))
    )
    private Date endDate;

    @EruptField(
            views = @View(title = "优先级"),
            edit = @Edit(title = "优先级", type = EditType.SLIDER, search = @Search,
                    sliderType = @SliderType(max = 10, markPoints = {3, 6, 9}))
    )
    private Integer priority;

    @EruptField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", notNull = true, search = @Search, type=EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "EDIT", label="新建"),
                            @VL(value = "NEW", label="展开"),
                            @VL(value = "ALLSET", label="齐套"),
                            @VL(value = "ACTIVE", label="下达"),
                            @VL(value = "COMPLETE", label="完工"),
                    })
            )
    )
    private String orderState;

    private void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public ProcessRoute getProcessRoute() {
        return this.processRoute;
    }

    public void confirm(){
        this.setOrderState("NEW");
    }

    public void setProductionOrderNumber(String productionOrderNumber) {
        this.productionOrderNumber = productionOrderNumber;
    }
}
