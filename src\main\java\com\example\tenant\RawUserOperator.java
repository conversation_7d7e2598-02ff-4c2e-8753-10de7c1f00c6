package com.example.tenant;

import com.example.demo.handler.OperationHandlerImpl;
import com.example.modeler.processroute.ProcessRoute;
import com.example.tenant.handler.LoginCheckOperator;
import com.example.tenant.handler.LoginCheckOperatorHandlerImpl;
import com.example.wom.order.domain.handler.OrderExpendOperationHandlerImpl;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.core.constant.MenuStatus;
import xyz.erupt.core.module.MetaMenu;
import xyz.erupt.upms.handler.ViaMenuValueCtrl;
import xyz.erupt.upms.model.EruptMenu;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Table()
@Entity
@Erupt(name = "操作用户",
        power = @Power(importable = false, export = false),
        rowOperation = {
                @RowOperation(
                        eruptClass = LoginCheckOperator.class,
                        operationHandler = LoginCheckOperatorHandlerImpl.class,
                        mode = RowOperation.Mode.BUTTON,
                        show = @ExprBool(
                                params = "adminCheckCMD"  //权限标识，菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        callHint = "执行？",
                        title = "检查是否允许登录"),
        }
)
public class RawUserOperator extends RawUser {

    @EruptField(
            views = @View(title = "操作账号"),
            edit = @Edit(title = "操作账号", notNull = true, search = @Search)
    )
    private String adminAccount;

    @EruptField(
            views = @View(title = "账号"),
            edit = @Edit(title = "账号", notNull = true, search = @Search)
    )
    private String account;

    @EruptField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", notNull = true, search = @Search)
    )
    private String name;


    public RawUserOperator(String account, String name, Boolean tenantAdminFlag) {
        this.account = account;
        this.name = name;
        this.tenantAdminFlag = tenantAdminFlag;
    }

    public RawUserOperator(){}

    public static RawUserOperator fromRawUser(RawUser rawUser) {
        if (null == rawUser) return null;
        RawUserOperator rawUserOperator = new RawUserOperator(rawUser.account,
                rawUser.name, rawUser.tenantAdminFlag);
        return rawUserOperator;
    }





}