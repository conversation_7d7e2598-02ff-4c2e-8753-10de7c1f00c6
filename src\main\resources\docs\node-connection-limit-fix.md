# 节点连接数限制修复

## 🎯 问题描述

### ❌ 发现的问题
在连接优先级规则中，如果定义了"设备→托架"的连接数量限制是1，那么：

1. **处理设备→托架时**：创建1个连接（符合限制）
2. **处理托架→设备时**：又创建1个连接（也符合各自的限制）
3. **结果**：设备实际有2个连接，超出了预期的连接数量限制

### 🔍 根本原因
- 当前逻辑只检查**连接类型**的数量限制（如"设备→托架"限制1个）
- 没有检查**节点本身**的总连接数限制
- 双向连接规则会导致同一对节点被连接多次

### 📊 问题示例
```
配置：
- 设备→托架：限制1个
- 托架→设备：限制1个  
- 节点最大连接数：2个

处理过程：
1. 处理设备DEV001：连接到托架BRACKET001 ✓
2. 处理托架BRACKET001：连接到设备DEV001 ✓（重复连接！）

结果：
- DEV001实际连接数：2个（超出预期）
- BRACKET001实际连接数：2个（超出预期）
```

## ✅ 解决方案

### 1. 核心思路
在创建连接之前，检查**源节点**和**目标节点**的当前连接数是否已达到各自的最大连接数限制。

### 2. 实现逻辑
```java
// 检查源节点和目标节点的连接数是否已达到限制
String sourceId = candidate.getSource().getId();
String targetId = candidate.getTarget().getId();

int sourceConnections = getNodeConnectionCount(sourceId, result, processedConnections);
int targetConnections = getNodeConnectionCount(targetId, result, processedConnections);

// 获取节点的最大连接数限制
int sourceMaxConnections = getNodeMaxConnections(candidate.getSource(), ruleLibrary);
int targetMaxConnections = getNodeMaxConnections(candidate.getTarget(), ruleLibrary);

// 如果任一节点的连接数已达到限制，跳过这个连接
if (sourceConnections >= sourceMaxConnections || targetConnections >= targetMaxConnections) {
    continue;
}
```

### 3. 新增辅助方法

#### getNodeConnectionCount()
```java
// 获取节点的当前连接数（包括作为源和目标的连接）
private static int getNodeConnectionCount(String nodeId, 
    List<GenerateRouteMapResponseDto.RoutePathDto> existingConnections, 
    Set<String> processedConnections) {
    
    int count = 0;
    // 统计已创建的连接中涉及该节点的数量
    for (GenerateRouteMapResponseDto.RoutePathDto path : existingConnections) {
        if (path.getStartStructCode().equals(nodeId) || 
            path.getEndStructCode().equals(nodeId)) {
            count++;
        }
    }
    return count;
}
```

#### getNodeMaxConnections()
```java
// 获取节点的最大连接数限制
private static int getNodeMaxConnections(Node node, 
    RouteMapRuleService.AdvancedRuleLibrary ruleLibrary) {
    
    // 首先尝试从规则库的全局最大连接数获取
    Integer globalMaxConnections = ruleLibrary.getMaxConnections();
    if (globalMaxConnections != null && globalMaxConnections > 0) {
        return globalMaxConnections;
    }
    
    // 如果没有全局配置，根据节点类型设置不同的默认值
    switch (node.getType()) {
        case "设备": return 5;
        case "扁条": return 10;
        case "托架": return 8;
        case "支架": return 6;
        case "贯穿件": return 20;
        default: return 5;
    }
}
```

## 🔧 修复效果

### 修复前
```
处理顺序：
1. 设备DEV001 → 托架BRACKET001 ✓ (设备连接数: 1)
2. 托架BRACKET001 → 设备DEV001 ✓ (设备连接数: 2, 超限!)

结果：设备DEV001有2个连接，超出限制
```

### 修复后
```
处理顺序：
1. 设备DEV001 → 托架BRACKET001 ✓ (设备连接数: 1)
2. 托架BRACKET001 → 设备DEV001 ✗ (设备连接数已达限制，跳过)

结果：设备DEV001有1个连接，符合预期
```

## 🧪 测试验证

### 1. 新增测试用例
```java
@Test
void testNodeConnectionLimits() {
    // 配置双向连接规则
    AdvancedRuleLibrary bidirectionalRules = RuleLibraryBuilder.create()
        .addConnectionRule("设备", "托架", 1)
        .addConnectionRule("托架", "设备", 1)
        .setMaxConnections(2) // 每个节点最多2个连接
        .build();
    
    // 验证每个节点的连接数不超过限制
    // 验证不会出现重复连接
}
```

### 2. 验证点
- ✅ 每个节点的连接数不超过配置的最大值
- ✅ 不会出现重复连接（A→B 和 B→A 同时存在）
- ✅ 优先级规则仍然正确工作
- ✅ 连接类型限制仍然有效

## 📊 算法流程

```mermaid
graph TD
    A[开始处理连接] --> B[获取候选连接]
    B --> C[检查连接类型限制]
    C --> D{类型限制OK?}
    D -->|否| E[跳过此连接]
    D -->|是| F[检查源节点连接数]
    F --> G{源节点连接数OK?}
    G -->|否| E
    G -->|是| H[检查目标节点连接数]
    H --> I{目标节点连接数OK?}
    I -->|否| E
    I -->|是| J[创建连接]
    J --> K[更新连接计数]
    K --> L[记录已处理连接]
    L --> M[继续下一个候选]
    E --> M
    M --> N{还有候选?}
    N -->|是| C
    N -->|否| O[结束]
```

## 🎯 配置建议

### 1. 合理设置节点连接数限制
```java
// 根据节点类型设置合理的连接数限制
.setMaxConnections(5)  // 全局限制，适用于所有节点

// 或者在 getNodeMaxConnections 中为不同类型设置不同限制
设备: 5个连接    // 设备通常连接多种类型
扁条: 10个连接   // 扁条可能连接更多节点
托架: 8个连接    // 托架中等连接数
支架: 6个连接    // 支架较少连接
贯穿件: 20个连接 // 贯穿件连接最多
```

### 2. 避免过度限制
```java
// 确保连接类型限制 + 节点连接数限制的合理性
// 例如：如果设备最多5个连接，那么各类型限制之和应该 ≤ 5
设备→扁条: 限制2个
设备→托架: 限制2个  
设备→支架: 限制1个
总计: 5个 ✓
```

### 3. 双向连接规则设计
```java
// 如果需要双向连接，建议：
// 方案1：只配置一个方向，让算法自动处理
.addConnectionRule("设备", "托架", 1)  // 只配置设备→托架

// 方案2：如果必须双向，确保连接数限制合理
.addConnectionRule("设备", "托架", 1)
.addConnectionRule("托架", "设备", 2)  // 不同的限制
```

## 🚀 总结

这次修复解决了一个重要的算法缺陷：

1. **✅ 防止节点连接数超限**：确保每个节点的总连接数不超过配置的最大值
2. **✅ 避免重复连接**：防止双向规则导致的重复连接问题
3. **✅ 保持优先级逻辑**：修复不影响原有的优先级处理逻辑
4. **✅ 提供灵活配置**：支持全局和类型特定的连接数限制

现在的算法能够在遵守连接类型限制的同时，确保每个节点的总连接数也在合理范围内，避免了双向连接规则导致的连接数超限问题。
