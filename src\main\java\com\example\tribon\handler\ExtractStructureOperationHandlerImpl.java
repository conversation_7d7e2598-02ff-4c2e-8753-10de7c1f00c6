package com.example.tribon.handler;

import com.example.tribon.domain.model.Structure;
import com.example.tribon.domain.model.subModel.StructureExtractForm;
import com.example.tribon.dto.StructureDataRetriveDto;
import com.example.tribon.dto.SockeGetAllStructureDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.annotation.fun.OperationHandler;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.dao.EruptJpaDao;

import javax.annotation.Resource;

@Component
@Transactional
@Slf4j
public class ExtractStructureOperationHandlerImpl implements OperationHandler<Structure, StructureExtractForm> {

    @Resource
    private EruptJpaDao eruptJpaDao;

    @Resource
    private EruptDao eruptDao;

    private static final String SERVER_HOST = "***************";
    private static final int SERVER_PORT = 8000;
    private static final String END_MARKER = "%EOR%";

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String exec(List<Structure> data, StructureExtractForm form, String[] param) {
        SockeGetAllStructureDto dto = new SockeGetAllStructureDto();
        dto.setRequestData(new SockeGetAllStructureDto.RequestData(form.getModule(), form.getStructure()));

        try (Socket socket = new Socket(SERVER_HOST, SERVER_PORT);
             BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream(), StandardCharsets.UTF_8));
             BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream(), StandardCharsets.UTF_8))) {

            // 发送 JSON 请求
            String requestJson = objectMapper.writeValueAsString(dto);
            writer.write(requestJson);
            writer.newLine(); // 确保服务器能接收到换行作为消息分割
            writer.flush();

            // 接收响应
            // 1. 读取固定长度的10位头部信息
            char[] header = new char[10];
            reader.read(header, 0, 10);
            int totalLength = Integer.parseInt(new String(header).trim());

            // 2. 读取数据体（totalLength 字节）
            StringBuilder buffer = new StringBuilder(totalLength);
            int read = 0;
            char[] tempBuffer = new char[totalLength];
            while (read < totalLength) {
                int r = reader.read(tempBuffer, read, totalLength - read);
                if (r == -1) break;
                buffer.append(tempBuffer, read, r);
                read += r;
            }

            // 3. 继续读直到%EOR%
            StringBuilder endMarker = new StringBuilder();
            while (true) {
                int ch = reader.read();
                if (ch == -1) break;
                endMarker.append((char) ch);
                if (endMarker.toString().endsWith("%EOR%")) break;
            }


//            StringBuilder responseBuilder = new StringBuilder();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                responseBuilder.append(line).append(System.lineSeparator());
//                if (line.trim().endsWith(END_MARKER)) {
//                    break;
//                }
//            }

            String body = buffer.toString().trim();
            processResponse(body);
            return body;

        } catch (IOException e) {
            log.error("Socket通信异常: {}", e.getMessage(), e);  // 推荐用日志系统替代 System.out
            return null;
        }
    }

    /*
    * 处理数据体
    * */
    private void processResponse(String response) {
//        if (response.length() < 10) {
//            throw new IllegalArgumentException("Response length is less than 10 characters.");
//        }
//        int length = Integer.parseInt(response.substring(0, 10).trim());
        String jsonString = response.trim();
        Gson gson = new Gson();
        List<StructureDataRetriveDto> dataList = gson.fromJson(
                jsonString,
                new TypeToken<List<StructureDataRetriveDto>>(){}.getType()
        );
        // 将StructureDataRetriveDto转为Structure，保存到数据库中
        for (StructureDataRetriveDto dto : dataList) {
            Structure structure = new Structure();
            structure.setCode(dto.getStructureName());
            structure.setComponentName(dto.getComponentName());
            structure.setStructType(getStructTypeByComponentName(dto.getComponentName()));
            structure.setVolumeName(dto.getVolumeName());
            structure.setRoomCode("DEFAULT"); // 默认房间编号
            structure.setPoi(Arrays.stream(dto.getPoi()).mapToObj(Double::toString).collect(Collectors.joining(",")));
            structure.setRot(Arrays.stream(dto.getRot()).mapToObj(Double::toString).collect(Collectors.joining(",")));
            structure.setRou(Arrays.stream(dto.getRou()).mapToObj(Double::toString).collect(Collectors.joining(",")));
            structure.setCog(Arrays.stream(dto.getCog()).mapToObj(Double::toString).collect(Collectors.joining(",")));
            // 将strucutre保存或更新到数据库中， code相同则更新，否则新增
            Structure existStructure = eruptDao.queryEntity(Structure.class, "code = :code", new HashMap<String, Object>() {{
                put("code", structure.getCode());
            }});
            if (existStructure != null){
                // 更新现有结构
                existStructure.setComponentName(structure.getComponentName());
                existStructure.setVolumeName(structure.getVolumeName());
                existStructure.setStructType(getStructTypeByComponentName(dto.getComponentName()));
                if (existStructure.getRoomCode() == null) {
                    existStructure.setRoomCode("DEFAULT"); // 默认房间编号
                }
                existStructure.setPoi(structure.getPoi());
                existStructure.setRot(structure.getRot());
                existStructure.setRou(structure.getRou());
                existStructure.setCog(structure.getCog());
                eruptDao.persistAndFlush(existStructure);
            } else {
                // 新增结构
                eruptDao.persist(structure);
            }



            // 其他字段的赋值
            // 保存到数据库的逻辑
            // repository.save(structure); // 假设有一个repository来保存数据
        }



    }

    // 根据component name，返回Struct Type
    private String getStructTypeByComponentName(String componentName) {
        // 这里可以根据实际情况实现逻辑
        // 例如，可以使用一个映射表来获取对应的结构类型
        switch (componentName.trim()) {
            case "E1-LD-5-A":
                return "托架";
            case "E1-FB35-100":
                return "扁钢";
            case "E3-LJD":
                return "设备";
            default:
                return "DEFAULT";
        }
    }

    @Override
    public StructureExtractForm eruptFormValue(List<Structure> data, StructureExtractForm structureExtractForm, String[] param) {
        return structureExtractForm;
    }
}
