package com.example.tribon.handler;

import com.example.tribon.domain.model.Structure;
import com.example.tribon.domain.model.subModel.StructureExtractForm;
import com.example.tribon.dto.StructureDataRetriveDto;
import com.example.tribon.dto.SockeGetAllStructureDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.annotation.fun.OperationHandler;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.dao.EruptJpaDao;

import javax.annotation.Resource;

@Component
@Transactional
public class ExtractStructureOperationHandlerImpl implements OperationHandler<Structure, StructureExtractForm> {

    @Resource
    private EruptJpaDao eruptJpaDao;

    @Resource
    private EruptDao eruptDao;

    @Override
    public String exec(List<Structure> data, StructureExtractForm structureExtractForm, String[] param) {
        try (Socket socket = new Socket("192.168.116.142", 8000)) {
            PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
            BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            // 构造请求参数
            SockeGetAllStructureDto dto = new SockeGetAllStructureDto();
            dto.setRequestData(new SockeGetAllStructureDto.RequestData(structureExtractForm.getModule(), structureExtractForm.getStructure()));
            ObjectMapper objectMapper = new ObjectMapper();
            out.println(objectMapper.writeValueAsString(dto)); // 发送消息
            StringBuilder responseBuilder = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                responseBuilder.append(line).append(System.lineSeparator());
                if (line.endsWith("%EOR%")) {
                    break;
                }
            }
            processResponse(responseBuilder.toString());
            System.out.println("服务器回复: " + responseBuilder.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private void processResponse(String response) {
        int length = Integer.parseInt (response.toString().substring(0, response.toString().indexOf("\r\n")).trim());
        String jsonString = response.substring(response.toString().indexOf("\r\n"), length + response.toString().indexOf("\r\n") + 2).trim();
        Gson gson = new Gson();
        List<StructureDataRetriveDto> dataList = gson.fromJson(
                jsonString,
                new TypeToken<List<StructureDataRetriveDto>>(){}.getType()
        );
        // 将StructureDataRetriveDto转为Structure，保存到数据库中
        for (StructureDataRetriveDto dto : dataList) {
            Structure structure = new Structure();
            structure.setCode(dto.getStructureName());
            structure.setComponentName(dto.getComponentName());
            structure.setVolumeName(dto.getVolumeName());
            structure.setPoi(Arrays.stream(dto.getPoi()).mapToObj(Double::toString).collect(Collectors.joining(",")));
            structure.setRot(Arrays.stream(dto.getRot()).mapToObj(Double::toString).collect(Collectors.joining(",")));
            structure.setRou(Arrays.stream(dto.getRou()).mapToObj(Double::toString).collect(Collectors.joining(",")));
            structure.setCog(Arrays.stream(dto.getCog()).mapToObj(Double::toString).collect(Collectors.joining(",")));
            // 将strucutre保存或更新到数据库中， code相同则更新，否则新增
            Structure existStructure = eruptDao.queryEntity(Structure.class, "code = :code", new HashMap<String, Object>() {{
                put("code", structure.getCode());
            }});
            if (existStructure != null){
                // 更新现有结构
                existStructure.setComponentName(structure.getComponentName());
                existStructure.setVolumeName(structure.getVolumeName());
                existStructure.setPoi(structure.getPoi());
                existStructure.setRot(structure.getRot());
                existStructure.setRou(structure.getRou());
                existStructure.setCog(structure.getCog());
                eruptDao.persistAndFlush(existStructure);
            } else {
                // 新增结构
                eruptDao.persist(structure);
            }



            // 其他字段的赋值
            // 保存到数据库的逻辑
            // repository.save(structure); // 假设有一个repository来保存数据
        }



    }

    @Override
    public StructureExtractForm eruptFormValue(List<Structure> data, StructureExtractForm structureExtractForm, String[] param) {
        return structureExtractForm;
    }
}
