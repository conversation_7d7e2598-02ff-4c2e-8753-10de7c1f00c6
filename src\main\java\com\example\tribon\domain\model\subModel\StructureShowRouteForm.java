package com.example.tribon.domain.model.subModel;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.jpa.model.BaseModel;

@Erupt(
        name = "安装件路线图展示表单"
)
@Getter
@Setter
public class StructureShowRouteForm extends BaseModel {

    @EruptField(
            views = @View(title = "Module"),
            edit = @Edit(title = "Module")
    )
    private String module;

    @EruptField(
            views = @View(title = "Structure"),
            edit = @Edit(title = "Structure")
    )
    private String structure;
}
