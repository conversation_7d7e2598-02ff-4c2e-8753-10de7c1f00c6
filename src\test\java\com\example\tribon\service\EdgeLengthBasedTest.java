package com.example.tribon.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基于边缘长度的窄边中心点连接测试
 */
@SpringBootTest
public class EdgeLengthBasedTest {

    @Test
    void testLengthBasedEdgeClassification() {
        System.out.println("=== 基于长度的边缘分类测试 ===");
        
        // 测试不同尺寸的托架
        testTrayEdges("长方形托架", 120, 60, 20);
        testTrayEdges("正方形托架", 80, 80, 20);
        testTrayEdges("窄长托架", 30, 100, 20);
        testTrayEdges("极窄托架", 10, 200, 20);
    }
    
    private void testTrayEdges(String description, double length, double width, double height) {
        System.out.println("\n" + description + " (" + length + "×" + width + "×" + height + "):");
        
        CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(
            0, 0, 0, 0, length, width, height
        );
        
        CableRoutingSolverService.Edge[] edges = tray.getTopEdges();
        
        int longEdgeCount = 0;
        int shortEdgeCount = 0;
        
        for (int i = 0; i < edges.length; i++) {
            CableRoutingSolverService.Edge edge = edges[i];
            boolean isPoint = (edge.start.x == edge.end.x && 
                              edge.start.y == edge.end.y && 
                              edge.start.z == edge.end.z);
            
            double edgeLength = 0;
            if (!isPoint) {
                edgeLength = Math.sqrt(
                    Math.pow(edge.end.x - edge.start.x, 2) +
                    Math.pow(edge.end.y - edge.start.y, 2) +
                    Math.pow(edge.end.z - edge.start.z, 2)
                );
                longEdgeCount++;
            } else {
                shortEdgeCount++;
            }
            
            String edgeType = getEdgeName(edge.side);
            System.out.println("  " + edgeType + ": " + 
                              (isPoint ? "[点]" : "[线段，长度=" + String.format("%.1f", edgeLength) + "]"));
        }
        
        System.out.println("  统计: 长边=" + longEdgeCount + ", 窄边(点)=" + shortEdgeCount);
        
        // 验证逻辑
        if (length == width) {
            // 正方形：所有边都应该是线段
            assertEquals(4, longEdgeCount, "正方形托架所有边都应该是线段");
            assertEquals(0, shortEdgeCount, "正方形托架不应该有点边");
        } else {
            // 长方形：应该有2条长边和2条窄边
            assertEquals(2, longEdgeCount, "长方形托架应该有2条长边");
            assertEquals(2, shortEdgeCount, "长方形托架应该有2条窄边(点)");
        }
    }
    
    private String getEdgeName(int side) {
        switch (side) {
            case 0: return "下边";
            case 1: return "上边";
            case 2: return "左边";
            case 3: return "右边";
            default: return "未知边";
        }
    }

    @Test
    void testRotatedTrayEdgeClassification() {
        System.out.println("\n=== 旋转托架边缘分类测试 ===");
        
        // 测试旋转45度的长方形托架
        CableRoutingSolverService.Tray rotatedTray = new CableRoutingSolverService.Tray(
            0, 0, 0, 0, 150, 75, 20, 0, 0, Math.toRadians(45)
        );
        
        CableRoutingSolverService.Edge[] edges = rotatedTray.getTopEdges();
        
        System.out.println("旋转45度的长方形托架 (150×75×20):");
        
        int longEdgeCount = 0;
        int shortEdgeCount = 0;
        
        for (int i = 0; i < edges.length; i++) {
            CableRoutingSolverService.Edge edge = edges[i];
            boolean isPoint = (edge.start.x == edge.end.x && 
                              edge.start.y == edge.end.y && 
                              edge.start.z == edge.end.z);
            
            double edgeLength = 0;
            if (!isPoint) {
                edgeLength = Math.sqrt(
                    Math.pow(edge.end.x - edge.start.x, 2) +
                    Math.pow(edge.end.y - edge.start.y, 2) +
                    Math.pow(edge.end.z - edge.start.z, 2)
                );
                longEdgeCount++;
            } else {
                shortEdgeCount++;
            }
            
            String edgeType = getEdgeName(edge.side);
            System.out.println("  " + edgeType + ": " + 
                              String.format("(%.1f,%.1f,%.1f)", edge.start.x, edge.start.y, edge.start.z) + " → " +
                              String.format("(%.1f,%.1f,%.1f)", edge.end.x, edge.end.y, edge.end.z) + " " +
                              (isPoint ? "[点]" : "[线段，长度=" + String.format("%.1f", edgeLength) + "]"));
        }
        
        System.out.println("  统计: 长边=" + longEdgeCount + ", 窄边(点)=" + shortEdgeCount);
        
        // 即使旋转后，仍应该有2条长边和2条窄边
        assertEquals(2, longEdgeCount, "旋转后仍应该有2条长边");
        assertEquals(2, shortEdgeCount, "旋转后仍应该有2条窄边(点)");
        
        System.out.println("✅ 旋转托架边缘分类正确");
    }

    @Test
    void testCrossRoomConnectionWithLengthBasedEdges() {
        System.out.println("\n=== 基于长度的跨房间连接测试 ===");
        
        // 创建两个不同方向的托架
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 200, 50, 20),     // 水平长托架
            new CableRoutingSolverService.Tray(1, 0, 150, 0, 50, 200, 20)    // 垂直长托架
        );
        
        System.out.println("测试场景:");
        System.out.println("  托架0: 水平长托架 (200×50×20)，中心(0,0,0)");
        System.out.println("  托架1: 垂直长托架 (50×200×20)，中心(0,150,0)");
        
        // 分析两个托架的边缘
        for (int i = 0; i < trays.size(); i++) {
            CableRoutingSolverService.Edge[] edges = trays.get(i).getTopEdges();
            System.out.println("  托架" + i + "的边缘:");
            
            for (CableRoutingSolverService.Edge edge : edges) {
                boolean isPoint = (edge.start.x == edge.end.x && 
                                  edge.start.y == edge.end.y && 
                                  edge.start.z == edge.end.z);
                
                String edgeType = getEdgeName(edge.side);
                System.out.println("    " + edgeType + ": " + (isPoint ? "[点]" : "[线段]"));
            }
        }
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        assertFalse(segments.isEmpty(), "应该能找到连接路径");
        
        if (!segments.isEmpty()) {
            System.out.println("连接结果:");
            for (int i = 0; i < segments.size(); i++) {
                CableRoutingSolverService.Segment seg = segments.get(i);
                System.out.println("  段" + i + ": " + 
                                  String.format("(%.1f,%.1f,%.1f)", seg.start.x, seg.start.y, seg.start.z) + " → " +
                                  String.format("(%.1f,%.1f,%.1f)", seg.end.x, seg.end.y, seg.end.z));
            }
            
            System.out.println("✅ 基于长度的跨房间连接测试通过");
        }
    }

    @Test
    void testCornerPointAdjustment() {
        System.out.println("\n=== 角点调整到窄边中心测试 ===");

        // 创建两个托架，使它们的最短连接可能在角点
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 200, 60, 20),      // 长方形托架1
            new CableRoutingSolverService.Tray(1, 250, 80, 0, 200, 60, 20)    // 长方形托架2，稍微偏移
        );

        System.out.println("测试场景:");
        System.out.println("  托架0: 长方形托架 (200×60×20)，中心(0,0,0)");
        System.out.println("  托架1: 长方形托架 (200×60×20)，中心(250,80,0)");
        System.out.println("  预期：如果连接点在角上，应调整到窄边中心");

        // 分析托架的窄边中心点
        for (int i = 0; i < trays.size(); i++) {
            CableRoutingSolverService.Tray tray = trays.get(i);
            List<CableRoutingSolverService.Point3D> narrowCenters = tray.getNarrowEdgeCenters();

            System.out.println("  托架" + i + "的窄边中心点:");
            for (int j = 0; j < narrowCenters.size(); j++) {
                CableRoutingSolverService.Point3D center = narrowCenters.get(j);
                System.out.println("    窄边中心" + j + ": " +
                                  String.format("(%.1f,%.1f,%.1f)", center.x, center.y, center.z));
            }
        }

        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);

        assertFalse(segments.isEmpty(), "应该能找到连接路径");

        if (!segments.isEmpty()) {
            System.out.println("连接结果:");
            for (int i = 0; i < segments.size(); i++) {
                CableRoutingSolverService.Segment seg = segments.get(i);
                System.out.println("  段" + i + ": " +
                                  String.format("(%.1f,%.1f,%.1f)", seg.start.x, seg.start.y, seg.start.z) + " → " +
                                  String.format("(%.1f,%.1f,%.1f)", seg.end.x, seg.end.y, seg.end.z));
            }

            // 验证连接点是否合理
            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);

            System.out.println("连接点分析:");
            System.out.println("  起点: " + String.format("(%.1f,%.1f,%.1f)", firstSeg.start.x, firstSeg.start.y, firstSeg.start.z));
            System.out.println("  终点: " + String.format("(%.1f,%.1f,%.1f)", lastSeg.end.x, lastSeg.end.y, lastSeg.end.z));

            // 检查起点是否在托架0的窄边中心附近
            List<CableRoutingSolverService.Point3D> tray0NarrowCenters = trays.get(0).getNarrowEdgeCenters();
            boolean startNearNarrowCenter = false;
            for (CableRoutingSolverService.Point3D center : tray0NarrowCenters) {
                double dist = Math.sqrt(
                    Math.pow(firstSeg.start.x - center.x, 2) +
                    Math.pow(firstSeg.start.y - center.y, 2) +
                    Math.pow(firstSeg.start.z - center.z, 2)
                );
                if (dist < 5.0) { // 5单位的容差
                    startNearNarrowCenter = true;
                    System.out.println("  ✅ 起点接近托架0的窄边中心 (距离=" + String.format("%.1f", dist) + ")");
                    break;
                }
            }

            // 检查终点是否在托架1的窄边中心附近
            List<CableRoutingSolverService.Point3D> tray1NarrowCenters = trays.get(1).getNarrowEdgeCenters();
            boolean endNearNarrowCenter = false;
            for (CableRoutingSolverService.Point3D center : tray1NarrowCenters) {
                double dist = Math.sqrt(
                    Math.pow(lastSeg.end.x - center.x, 2) +
                    Math.pow(lastSeg.end.y - center.y, 2) +
                    Math.pow(lastSeg.end.z - center.z, 2)
                );
                if (dist < 5.0) { // 5单位的容差
                    endNearNarrowCenter = true;
                    System.out.println("  ✅ 终点接近托架1的窄边中心 (距离=" + String.format("%.1f", dist) + ")");
                    break;
                }
            }

            if (startNearNarrowCenter || endNearNarrowCenter) {
                System.out.println("✅ 角点调整到窄边中心测试通过");
            } else {
                System.out.println("ℹ️ 连接点可能不在角上，未触发调整机制");
            }
        }
    }

    @Test
    void testCornerDetection() {
        System.out.println("\n=== 角点检测测试 ===");

        // 创建一个托架并测试角点检测
        CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20);

        // 获取托架的顶点
        CableRoutingSolverService.Point3D[] vertices = tray.getTopVertices();

        System.out.println("托架顶点:");
        for (int i = 0; i < vertices.length; i++) {
            System.out.println("  顶点" + i + ": " +
                              String.format("(%.1f,%.1f,%.1f)", vertices[i].x, vertices[i].y, vertices[i].z));
        }

        // 测试角点检测
        double tolerance = 5.0; // 5单位容差

        System.out.println("角点检测测试 (容差=" + tolerance + "):");

        // 测试顶点本身
        for (int i = 0; i < vertices.length; i++) {
            boolean isCorner = tray.isPointOnCorner(vertices[i], tolerance);
            System.out.println("  顶点" + i + " 是否为角点: " + isCorner + " ✅");
            assertTrue(isCorner, "顶点应该被识别为角点");
        }

        // 测试顶点附近的点
        CableRoutingSolverService.Point3D nearCorner = new CableRoutingSolverService.Point3D(
            vertices[0].x + 2, vertices[0].y + 2, vertices[0].z
        );
        boolean isNearCorner = tray.isPointOnCorner(nearCorner, tolerance);
        System.out.println("  顶点附近的点 是否为角点: " + isNearCorner + " ✅");
        assertTrue(isNearCorner, "顶点附近的点应该被识别为角点");

        // 测试边中心的点（不应该是角点）
        CableRoutingSolverService.Point3D edgeCenter = new CableRoutingSolverService.Point3D(
            (vertices[0].x + vertices[1].x) / 2,
            (vertices[0].y + vertices[1].y) / 2,
            vertices[0].z
        );
        boolean isEdgeCenter = tray.isPointOnCorner(edgeCenter, tolerance);
        System.out.println("  边中心的点 是否为角点: " + isEdgeCenter + " (应该为false)");
        assertFalse(isEdgeCenter, "边中心的点不应该被识别为角点");

        System.out.println("✅ 角点检测测试通过");
    }
}
