package com.example.tribon.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基于边缘长度的窄边中心点连接测试
 */
@SpringBootTest
public class EdgeLengthBasedTest {

    @Test
    void testLengthBasedEdgeClassification() {
        System.out.println("=== 基于长度的边缘分类测试 ===");
        
        // 测试不同尺寸的托架
        testTrayEdges("长方形托架", 120, 60, 20);
        testTrayEdges("正方形托架", 80, 80, 20);
        testTrayEdges("窄长托架", 30, 100, 20);
        testTrayEdges("极窄托架", 10, 200, 20);
    }
    
    private void testTrayEdges(String description, double length, double width, double height) {
        System.out.println("\n" + description + " (" + length + "×" + width + "×" + height + "):");
        
        CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(
            0, 0, 0, 0, length, width, height
        );
        
        CableRoutingSolverService.Edge[] edges = tray.getTopEdges();
        
        int longEdgeCount = 0;
        int shortEdgeCount = 0;
        
        for (int i = 0; i < edges.length; i++) {
            CableRoutingSolverService.Edge edge = edges[i];
            boolean isPoint = (edge.start.x == edge.end.x && 
                              edge.start.y == edge.end.y && 
                              edge.start.z == edge.end.z);
            
            double edgeLength = 0;
            if (!isPoint) {
                edgeLength = Math.sqrt(
                    Math.pow(edge.end.x - edge.start.x, 2) +
                    Math.pow(edge.end.y - edge.start.y, 2) +
                    Math.pow(edge.end.z - edge.start.z, 2)
                );
                longEdgeCount++;
            } else {
                shortEdgeCount++;
            }
            
            String edgeType = getEdgeName(edge.side);
            System.out.println("  " + edgeType + ": " + 
                              (isPoint ? "[点]" : "[线段，长度=" + String.format("%.1f", edgeLength) + "]"));
        }
        
        System.out.println("  统计: 长边=" + longEdgeCount + ", 窄边(点)=" + shortEdgeCount);
        
        // 验证逻辑
        if (length == width) {
            // 正方形：所有边都应该是线段
            assertEquals(4, longEdgeCount, "正方形托架所有边都应该是线段");
            assertEquals(0, shortEdgeCount, "正方形托架不应该有点边");
        } else {
            // 长方形：应该有2条长边和2条窄边
            assertEquals(2, longEdgeCount, "长方形托架应该有2条长边");
            assertEquals(2, shortEdgeCount, "长方形托架应该有2条窄边(点)");
        }
    }
    
    private String getEdgeName(int side) {
        switch (side) {
            case 0: return "下边";
            case 1: return "上边";
            case 2: return "左边";
            case 3: return "右边";
            default: return "未知边";
        }
    }

    @Test
    void testRotatedTrayEdgeClassification() {
        System.out.println("\n=== 旋转托架边缘分类测试 ===");
        
        // 测试旋转45度的长方形托架
        CableRoutingSolverService.Tray rotatedTray = new CableRoutingSolverService.Tray(
            0, 0, 0, 0, 150, 75, 20, 0, 0, Math.toRadians(45)
        );
        
        CableRoutingSolverService.Edge[] edges = rotatedTray.getTopEdges();
        
        System.out.println("旋转45度的长方形托架 (150×75×20):");
        
        int longEdgeCount = 0;
        int shortEdgeCount = 0;
        
        for (int i = 0; i < edges.length; i++) {
            CableRoutingSolverService.Edge edge = edges[i];
            boolean isPoint = (edge.start.x == edge.end.x && 
                              edge.start.y == edge.end.y && 
                              edge.start.z == edge.end.z);
            
            double edgeLength = 0;
            if (!isPoint) {
                edgeLength = Math.sqrt(
                    Math.pow(edge.end.x - edge.start.x, 2) +
                    Math.pow(edge.end.y - edge.start.y, 2) +
                    Math.pow(edge.end.z - edge.start.z, 2)
                );
                longEdgeCount++;
            } else {
                shortEdgeCount++;
            }
            
            String edgeType = getEdgeName(edge.side);
            System.out.println("  " + edgeType + ": " + 
                              String.format("(%.1f,%.1f,%.1f)", edge.start.x, edge.start.y, edge.start.z) + " → " +
                              String.format("(%.1f,%.1f,%.1f)", edge.end.x, edge.end.y, edge.end.z) + " " +
                              (isPoint ? "[点]" : "[线段，长度=" + String.format("%.1f", edgeLength) + "]"));
        }
        
        System.out.println("  统计: 长边=" + longEdgeCount + ", 窄边(点)=" + shortEdgeCount);
        
        // 即使旋转后，仍应该有2条长边和2条窄边
        assertEquals(2, longEdgeCount, "旋转后仍应该有2条长边");
        assertEquals(2, shortEdgeCount, "旋转后仍应该有2条窄边(点)");
        
        System.out.println("✅ 旋转托架边缘分类正确");
    }

    @Test
    void testCrossRoomConnectionWithLengthBasedEdges() {
        System.out.println("\n=== 基于长度的跨房间连接测试 ===");
        
        // 创建两个不同方向的托架
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 200, 50, 20),     // 水平长托架
            new CableRoutingSolverService.Tray(1, 0, 150, 0, 50, 200, 20)    // 垂直长托架
        );
        
        System.out.println("测试场景:");
        System.out.println("  托架0: 水平长托架 (200×50×20)，中心(0,0,0)");
        System.out.println("  托架1: 垂直长托架 (50×200×20)，中心(0,150,0)");
        
        // 分析两个托架的边缘
        for (int i = 0; i < trays.size(); i++) {
            CableRoutingSolverService.Edge[] edges = trays.get(i).getTopEdges();
            System.out.println("  托架" + i + "的边缘:");
            
            for (CableRoutingSolverService.Edge edge : edges) {
                boolean isPoint = (edge.start.x == edge.end.x && 
                                  edge.start.y == edge.end.y && 
                                  edge.start.z == edge.end.z);
                
                String edgeType = getEdgeName(edge.side);
                System.out.println("    " + edgeType + ": " + (isPoint ? "[点]" : "[线段]"));
            }
        }
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        assertFalse(segments.isEmpty(), "应该能找到连接路径");
        
        if (!segments.isEmpty()) {
            System.out.println("连接结果:");
            for (int i = 0; i < segments.size(); i++) {
                CableRoutingSolverService.Segment seg = segments.get(i);
                System.out.println("  段" + i + ": " + 
                                  String.format("(%.1f,%.1f,%.1f)", seg.start.x, seg.start.y, seg.start.z) + " → " +
                                  String.format("(%.1f,%.1f,%.1f)", seg.end.x, seg.end.y, seg.end.z));
            }
            
            System.out.println("✅ 基于长度的跨房间连接测试通过");
        }
    }
}
