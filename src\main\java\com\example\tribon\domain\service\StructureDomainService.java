package com.example.tribon.domain.service;

import com.example.tribon.domain.enumeration.StructTypeEnum;
import com.example.tribon.domain.model.StructureRelationship;
import com.example.tribon.domain.model.StructureSerialNumber;
import com.example.tribon.domain.repository.StructureSerialNumberRepository;
import com.example.tribon.dto.CreateRouteMapDto;
import com.example.tribon.dto.GenerateRouteMapResponseDto;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
public class StructureDomainService {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private StructureSerialNumberRepository structureSerialNumberRepository;

    /**
     * 生成结构代码
     *
     * @param structType    结构类型
     * @param projectName   项目名称
     * @param requestAmount 请求数量
     * @return 生成的结构代码
     */
    @Transactional
    public List<String> generateStructureCode(String structType, String projectName, Long requestAmount) {
        // 查询获取当前类型的最大编号
        Long maxSerialNumber = structureSerialNumberRepository.findByProjectNameAndStructureType(
                projectName,
                structType
        );
        if (maxSerialNumber == null) {
            maxSerialNumber = 0L; // 如果没有记录，则从0开始
            // 插入一条新的记录到编号池
            StructureSerialNumber structureSerialNumber = new StructureSerialNumber();
            structureSerialNumber.setStructureType(structType);
            structureSerialNumber.setProjectName(projectName);
            structureSerialNumber.setCurrentSerialNumber(maxSerialNumber + requestAmount);
            eruptDao.persist(structureSerialNumber);
        }
        // 更新编号池中的当前最大编号
        structureSerialNumberRepository.updateMaxSerialNumber(
                projectName,
                structType,
                maxSerialNumber + requestAmount
        );
        // 根据requestAmount，返回一组maxSerialNumber递增的编号
        List<String> structureCodes = new ArrayList<>();
        for (int i = 0; i < requestAmount; i++) {
            maxSerialNumber++;
            if ( structType != null && !structType.equals("") ) {
                structureCodes.add(structType + "-" + String.format("%010d", maxSerialNumber)) ;
            } else {
                structureCodes.add("DUMMY-" + String.format("%010d", maxSerialNumber)) ;
            }
        }
        return structureCodes;
    }

    public List<StructureRelationship> parseRouteMapToRelationships(CreateRouteMapDto createRouteMapDto){
        List<StructureRelationship> relationships = new ArrayList<>();
        for (CreateRouteMapDto.RoutePathDto routePath : createRouteMapDto.getRoutePathList()) {
            StructureRelationship relationship = new StructureRelationship();
            relationship.setStructCodeA(routePath.getStartRouteNode().getBaseOnStructCode());
            relationship.setStructCodeB(routePath.getEndRouteNode().getBaseOnStructCode());
            relationship.setEdgeStructCode(routePath.getRelationEdgeFlagStructCode());
            relationship.setEdgeStructHandle(routePath.getRelationHandles());
            relationship.setCogXMark1StructCodeA(routePath.getStartRouteNode().getFlagX1StructCode());
            relationship.setCogXMark2StructCodeA(routePath.getStartRouteNode().getFlagX2StructCode());
            relationship.setCogXMark1StructCodeB(routePath.getEndRouteNode().getFlagX1StructCode());
            relationship.setCogXMark2StructCodeB(routePath.getEndRouteNode().getFlagX2StructCode());
            relationships.add(relationship);
        }
        return relationships;
    }

}
