erupt-app:
  # 登录失败几次，需要验证码
  verifyCodeCount: 2
  # 是否开启水印
  water-mark: false
erupt:
  # 是否开启csrf防御
  csrfInspect: true
  # 是否开启redis方式存储session，默认false，开启后需在配置文件中添加redis配置（同 spring boot）
  redisSession: false
  # 附件上传存储路径, 默认路径为：/opt/erupt-attachment
  uploadPath: D:/erupt/attachment
  # 是否保留上传文件原始名称
  keepUploadFileName: false
  # 登录session时长（redisSession为true时有效）
  upms.expireTimeByLogin: 60
  # 是否记录操作日志，默认true，该功能开启后可在【系统管理 → 操作日志】中查看操作日志
  security.recordOperateLog: true
  cloud-server:
    # cloud key 命名空间(可选配置)
    cloud-name-space: 'erupt-cloud:'
    # node节点持久化时长，单位：ms (可选配置)
    node-expire-time: 60000
    # node节点存活检查周期，单位：ms (可选配置)
    node-survive-check-time: 120000
    # 是否校验 node 节点 access-token，默认值 true，1.11.4及以上版本支持
    validate-access-token: true
  hot-build: true


magic-api:
  web: /magic/web
  # 接口配置文件存放路径
  resource.location: D:/erupt/magic-script
  show-url: false
  response: |- #配置JSON格式，格式为magic-script中的表达式
    {
      code: code,
      message: message,
      data,
      timestamp,
      requestTime,
      executeTime,
    }



spring:
  datasource:
    url: *******************************************************************************************************
    username: tribon_mstr
    password: tribon_mstr
  jpa:
    show-sql: true
    generate-ddl: true
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    database: mysql
  profiles:
    active: dev
  mail:
    username: <EMAIL>
    password: xxxxxxx
    host: smtp.qq.com
    properties:
      mail.smtp.ssl.auth: true
      mail.smtp.ssl.enable: true
      mail.smtp.ssl.required: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

server:
  # 启用 gzip 压缩
  compression:
    mime-types: application/javascript,text/css,application/json,application/xml,text/html,text/xml,text/plain
    enabled: true
  error:
    includeException: true
    includeStacktrace: ALWAYS
    includeMessage: ALWAYS
  port: 9999
#  ip: ************

logging:
  level:
    root: info

management:
  endpoints:
    web:
      exposure:
        include: health   # 仅暴露 health 端点[2](@ref)[6](@ref)
  endpoint:
    health:
      enabled: true       # 启用 health 端点（默认已启用，可省略）[2](@ref)[7](@ref)
      show-details: always  # 显示详细健康信息（可选增强）[2](@ref)

#
#dubbo:
#  application:
#    name: dubbo-springboot-triple-rest-jaxrs
#    qos-port: 22223
#    # Serializable 接口检查模式，Dubbo 中默认配置为 `true` 开启检查
#    check-serializable: false
#    # 检查模式分为三个级别：STRICT 严格检查，WARN 告警，DISABLE 禁用
#    serialize-check-status: DISABLE
#  registry:
#    address: zookeeper://127.0.0.1:2181?registry-type=service
#  protocol:
#    name: tri
#    port: 50053