<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Structure 数据管理 - Luckysheet</title>
    
    <!-- Luckysheet CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/css/pluginsCss.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/plugins.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/css/luckysheet.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/assets/iconfont/iconfont.css" />
    
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .header-toolbar {
            background: #f5f5f5;
            padding: 10px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .toolbar-label {
            font-weight: bold;
            color: #333;
        }
        
        .luckysheet-container {
            width: 100%;
            height: calc(100vh - 80px);
            margin: 0;
            padding: 0;
        }
        
        .status-bar {
            background: #f0f0f0;
            padding: 5px 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        
        .el-input {
            width: 150px;
        }
        
        .el-select {
            width: 120px;
        }
        
        .el-button {
            margin-left: 5px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 工具栏 -->
        <div class="header-toolbar">
            <!-- 第一行：查询条件 -->
            <div class="toolbar-group" style="width: 100%; margin-bottom: 10px;">
                <span class="toolbar-label">查询条件:</span>
                <el-input
                    v-model="searchForm.code"
                    placeholder="唯一编号"
                    clearable
                    size="small"
                    @keyup.enter="loadData">
                </el-input>
                <el-input
                    v-model="searchForm.name"
                    placeholder="安装件名称"
                    clearable
                    size="small"
                    @keyup.enter="loadData">
                </el-input>
                <el-input
                    v-model="searchForm.componentName"
                    placeholder="Component名称"
                    clearable
                    size="small"
                    @keyup.enter="loadData">
                </el-input>
                <el-select
                    v-model="searchForm.structType"
                    placeholder="安装件类型"
                    clearable
                    size="small"
                    filterable>
                    <el-option
                        v-for="type in structTypes"
                        :key="type"
                        :label="type"
                        :value="type">
                    </el-option>
                </el-select>
                <el-select
                    v-model="searchForm.roomCode"
                    placeholder="房间编号"
                    clearable
                    size="small"
                    filterable>
                    <el-option
                        v-for="room in roomCodes"
                        :key="room"
                        :label="room"
                        :value="room">
                    </el-option>
                </el-select>
                <el-input
                    v-model="searchForm.volumeName"
                    placeholder="Volume名称"
                    clearable
                    size="small"
                    @keyup.enter="loadData">
                </el-input>

                <!-- 高级查询按钮 -->
                <el-button
                    type="text"
                    size="small"
                    @click="showAdvancedSearch = !showAdvancedSearch">
                    <i :class="showAdvancedSearch ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                    高级查询
                </el-button>
            </div>

            <!-- 高级查询条件（可折叠） -->
            <div v-show="showAdvancedSearch" class="toolbar-group" style="width: 100%; margin-bottom: 10px; padding: 10px; background: #fafafa; border-radius: 4px;">
                <el-input
                    v-model="searchForm.poi"
                    placeholder="POI坐标"
                    clearable
                    size="small"
                    @keyup.enter="loadData">
                </el-input>
                <el-input
                    v-model="searchForm.cog"
                    placeholder="COG坐标"
                    clearable
                    size="small"
                    @keyup.enter="loadData">
                </el-input>
                <el-input
                    v-model="searchForm.cogXMark1StructCode"
                    placeholder="重心X标记1"
                    clearable
                    size="small"
                    @keyup.enter="loadData">
                </el-input>
                <el-input
                    v-model="searchForm.cogXMark2StructCode"
                    placeholder="重心X标记2"
                    clearable
                    size="small"
                    @keyup.enter="loadData">
                </el-input>

                <!-- 日期范围查询 -->
                <el-date-picker
                    v-model="searchForm.dateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss">
                </el-date-picker>

                <el-button type="warning" size="small" @click="resetSearch">
                    <i class="el-icon-refresh"></i> 重置
                </el-button>
            </div>
            
            <div class="toolbar-group">
                <el-button type="primary" size="small" @click="loadData" :loading="loading">
                    <i class="el-icon-search"></i> 查询
                </el-button>
                <el-button type="success" size="small" @click="saveData" :loading="saving">
                    <i class="el-icon-check"></i> 保存
                </el-button>
                <el-button type="info" size="small" @click="exportData">
                    <i class="el-icon-download"></i> 导出
                </el-button>
                <el-button type="warning" size="small" @click="addNewRow">
                    <i class="el-icon-plus"></i> 新增行
                </el-button>
                <el-button type="danger" size="small" @click="deleteSelectedRows">
                    <i class="el-icon-delete"></i> 删除选中
                </el-button>
            </div>
            
            <div class="toolbar-group" style="margin-left: auto;">
                <span class="toolbar-label">总计: {{totalCount}} 条记录</span>
            </div>
        </div>
        
        <!-- Luckysheet 容器 -->
        <div id="luckysheet" class="luckysheet-container"></div>
        
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>就绪 | 双击单元格编辑 | Ctrl+S 保存 | 选中行后点击删除按钮可删除记录</span>
        </div>
        
        <!-- 加载遮罩 -->
        <div class="loading-overlay" :class="{hidden: !loading}">
            <el-loading-spinner></el-loading-spinner>
            <span style="margin-left: 10px;">加载中...</span>
        </div>
    </div>

    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- Axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <!-- Luckysheet JS -->
    <script src="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/js/plugin.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/luckysheet.umd.js"></script>

    <script>
        new Vue({
            el: '#app',
            data: {
                loading: false,
                saving: false,
                showAdvancedSearch: false,
                searchForm: {
                    code: '',
                    name: '',
                    componentName: '',
                    structType: '',
                    roomCode: '',
                    volumeName: '',
                    poi: '',
                    cog: '',
                    cogXMark1StructCode: '',
                    cogXMark2StructCode: '',
                    dateRange: null
                },
                structTypes: [],
                roomCodes: [],
                totalCount: 0,
                luckysheetInstance: null,
                originalData: null,
                selectedRows: []
            },
            
            mounted() {
                this.initLuckysheet();
                this.loadMetadata();
                this.loadData();
                this.bindKeyboardShortcuts();
            },
            
            methods: {
                // 初始化 Luckysheet
                initLuckysheet() {
                    const options = {
                        container: 'luckysheet',
                        title: 'Structure 数据管理',
                        lang: 'zh',
                        allowCopy: true,
                        allowEdit: true,
                        allowUpdate: true,
                        showToolbar: true,
                        showInfoBar: false,
                        showSheetBar: false,
                        showStatisticBar: false,
                        enableAddRow: true,
                        enableAddCol: false,
                        sheetBottomConfig: false,
                        allowEdit: true,
                        hook: {
                            cellEditBefore: (range) => {
                                // 第一列（ID列）不允许编辑
                                if (range && range.length > 0 && range[0].column && range[0].column[0] === 0) {
                                    return false;
                                }
                                return true;
                            }
                        }
                    };
                    
                    luckysheet.create(options);
                    this.luckysheetInstance = luckysheet;
                },
                
                // 加载元数据
                async loadMetadata() {
                    try {
                        const [typesRes, roomsRes] = await Promise.all([
                            axios.post('/api/structure-luckysheet/struct-types', {}),
                            axios.post('/api/structure-luckysheet/room-codes', {})
                        ]);

                        this.structTypes = typesRes.data;
                        this.roomCodes = roomsRes.data;
                    } catch (error) {
                        this.$message.error('加载元数据失败: ' + error.message);
                    }
                },
                
                // 加载数据
                async loadData() {
                    this.loading = true;
                    try {
                        const requestData = {
                            ...this.searchForm,
                            page: 0,
                            size: 1000
                        };

                        const response = await axios.post('/api/structure-luckysheet/data', requestData);
                        const data = response.data;
                        
                        if (data.sheets && data.sheets.length > 0) {
                            this.totalCount = data.sheets[0].row - 1; // 减去表头行
                            this.originalData = JSON.parse(JSON.stringify(data));
                            
                            // 更新 Luckysheet 数据
                            luckysheet.loadUrl(null, data);
                        }
                        
                        this.$message.success('数据加载成功');
                    } catch (error) {
                        this.$message.error('加载数据失败: ' + error.message);
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 保存数据
                async saveData() {
                    this.saving = true;
                    try {
                        const currentData = luckysheet.getAllSheets();
                        const saveData = {
                            sheets: currentData,
                            title: 'Structure数据管理'
                        };
                        
                        const response = await axios.post('/api/structure-luckysheet/save', saveData);
                        
                        if (response.data.success) {
                            this.$message.success(response.data.message);
                            // 重新加载数据以获取最新状态
                            await this.loadData();
                        } else {
                            this.$message.error(response.data.message);
                        }
                    } catch (error) {
                        this.$message.error('保存失败: ' + error.message);
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 导出数据
                async exportData() {
                    try {
                        // 使用 Luckysheet 的导出功能
                        luckysheet.exportLuckyToExcel('Structure数据导出');
                        this.$message.success('导出成功');
                    } catch (error) {
                        this.$message.error('导出失败: ' + error.message);
                    }
                },
                
                // 新增行
                addNewRow() {
                    const sheet = luckysheet.getSheet();
                    const rowCount = sheet.row;
                    
                    // 在最后添加新行
                    luckysheet.insertRow(rowCount);
                    
                    // 设置新行的默认值
                    luckysheet.setCellValue(rowCount, 0, 'NEW'); // ID 列标记为新增
                    
                    this.$message.success('已添加新行，请填写数据后保存');
                },
                
                // 删除选中行
                deleteSelectedRows() {
                    const selection = luckysheet.getRange();
                    if (!selection || selection.length === 0) {
                        this.$message.warning('请先选中要删除的行');
                        return;
                    }
                    
                    this.$confirm('确定要删除选中的行吗？', '确认删除', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 获取选中的行号
                        const rows = selection.map(range => range.row).filter((row, index, arr) => arr.indexOf(row) === index);
                        
                        // 从后往前删除，避免索引变化
                        rows.sort((a, b) => b - a).forEach(row => {
                            if (row > 0) { // 不删除表头行
                                luckysheet.deleteRow(row);
                            }
                        });
                        
                        this.$message.success('已删除选中行，请保存以确认删除');
                    }).catch(() => {
                        // 用户取消删除
                    });
                },
                
                // 重置搜索条件
                resetSearch() {
                    this.searchForm = {
                        code: '',
                        name: '',
                        componentName: '',
                        structType: '',
                        roomCode: '',
                        volumeName: '',
                        poi: '',
                        cog: '',
                        cogXMark1StructCode: '',
                        cogXMark2StructCode: '',
                        dateRange: null
                    };
                    this.loadData();
                    this.$message.success('搜索条件已重置');
                },

                // 绑定键盘快捷键
                bindKeyboardShortcuts() {
                    document.addEventListener('keydown', (e) => {
                        if (e.ctrlKey && e.key === 's') {
                            e.preventDefault();
                            this.saveData();
                        }
                        if (e.ctrlKey && e.key === 'f') {
                            e.preventDefault();
                            this.showAdvancedSearch = !this.showAdvancedSearch;
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>
