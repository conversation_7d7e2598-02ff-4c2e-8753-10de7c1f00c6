package com.example.demo.model.material.api.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import xyz.erupt.annotation.fun.ChoiceFetchHandler;
import xyz.erupt.annotation.fun.VLModel;

import java.util.ArrayList;
import java.util.List;

public class MaterialEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getTitle()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        ACTIVE("可用"),
        FROZEN("冻结");

        private final String title;

    }
}
