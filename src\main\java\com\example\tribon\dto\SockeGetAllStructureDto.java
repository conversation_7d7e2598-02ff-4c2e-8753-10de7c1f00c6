package com.example.tribon.dto;

import lombok.Data;

import java.util.Map;

@Data
public class SockeGetAllStructureDto {
    private String requestCode = "001";
    private RequestData requestData;


    @Data
    public static class RequestData {
        private String module;
        private String structure;

        public RequestData(String module, String structure) {
            this.module = module;
            this.structure = structure;
        }
    }
}
