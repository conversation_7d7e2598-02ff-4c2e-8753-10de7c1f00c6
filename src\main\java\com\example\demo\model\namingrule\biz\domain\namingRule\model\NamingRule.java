package com.example.demo.model.namingrule.biz.domain.namingRule.model;


import com.example.demo.model.namingrule.biz.handler.NamingRuleOperationHandler;
import com.example.demo.model.namingrule.biz.domain.namingRule.proxy.NamingRuleDataProxy;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AutoCompleteType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;
import java.util.List;

@Erupt(
        name = "编码规则",
        dataProxy = NamingRuleDataProxy.class,
        rowOperation = {
                @RowOperation(
                        operationHandler = NamingRuleOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "确定获取下一编码值？",
                        title = "获取下一编码值"),
        }
)
@Table(name = "naming_rule",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"namingRuleCode"})
        }
)
@Entity
@Getter
@Setter
public class NamingRule extends BaseModel {
    @EruptField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", readonly = @Readonly(add = false, edit = true),notNull = true, search = @Search(vague = true))
    )
    private String namingRuleCode;

    @EruptField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true, search = @Search(vague = true))
    )
    private String displayName;

    @EruptField(
            edit = @Edit(title = "说明", notNull = false,
                    type = EditType.TEXTAREA)
    )
    private String desciption;



    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true) //一对多，且开启级联
    @JoinColumn(name = "naming_rule_id")//this表示当前的表名，如：order_id子表会自动创建该列来标识与主表的关系
    @EruptField(
            views = @View(title = "规则参数", column = "namingRuleParametersList"),
            edit = @Edit(title = "规则参数", notNull = true, search = @Search,
                    type = EditType.TAB_TABLE_ADD
            )
    )
    private List<NamingRuleParameter> namingRuleParameterList;

}
