//package com.example.wom.order_provider;
//
//import com.alibaba.fastjson2.JSON;
//import com.alibaba.fastjson2.JSONObject;
//import com.example.wom.order.domain.ProductionOrder;
//import com.example.wom.order_api.OrderService;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.springframework.stereotype.Service;
//import xyz.erupt.core.view.EruptModel;
//import xyz.erupt.jpa.dao.EruptDao;
//import xyz.erupt.jpa.service.EruptDataServiceDbImpl;
//
//import javax.annotation.Resource;
//import javax.ws.rs.core.MultivaluedMap;
//
//@Service
//@DubboService
//public class OrderServiceImpl implements OrderService {
//
//    @Override
//    public String getOrderNumberById(String OrderNumber){
//        return "getOrderNumberById from ProductionOrder:" + OrderNumber;
//    }
//    @Override
//    public String updateOrder(String jsonObject){
//        JSONObject order =  JSON.parseObject(jsonObject);
////        ProductionOrder po = eruptDao.findById(ProductionOrder.class, order.get("id"));
////        po.setProductionOrderNumber(order.get("productionOrderNumber").toString());
////        eruptDao.persistAndFlush(po);
//        return "updateOrder";
//    }
//}
