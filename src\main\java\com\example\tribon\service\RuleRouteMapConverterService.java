package com.example.tribon.service;

import com.example.tribon.domain.model.RuleRouteMap;
import com.example.tribon.domain.model.RuleRouteMapConnectionPriority;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 路线图规则转换服务
 * 负责将数据库中的规则模型转换为算法使用的规则对象
 */
@Service
public class RuleRouteMapConverterService {

    @Resource
    private EruptDao eruptDao;

    /**
     * 将数据库规则转换为算法规则库
     */
    public RouteMapRuleService.AdvancedRuleLibrary convertToAdvancedRuleLibrary(Long ruleId) {
        // 查询规则主表
        RuleRouteMap ruleRouteMap = eruptDao.queryEntity(RuleRouteMap.class, ruleId.toString());
        if (ruleRouteMap == null) {
            throw new IllegalArgumentException("规则不存在，ID: " + ruleId);
        }

        return convertToAdvancedRuleLibrary(ruleRouteMap);
    }

    /**
     * 将 RuleRouteMap 实体转换为 AdvancedRuleLibrary
     */
    public RouteMapRuleService.AdvancedRuleLibrary convertToAdvancedRuleLibrary(RuleRouteMap ruleRouteMap) {
        RouteMapRuleService.AdvancedRuleLibrary ruleLibrary = new RouteMapRuleService.AdvancedRuleLibrary();

        // 基本配置
        ruleLibrary.setConnectionStrategy(ruleRouteMap.getConnectionStrategy().name());
        ruleLibrary.setEnableRoomRule(ruleRouteMap.getEnableRoomRule());
        ruleLibrary.setEnableThroughPieceRule(ruleRouteMap.getEnableThroughPieceRule());
        ruleLibrary.setMaxConnections(ruleRouteMap.getMaxConnections());

        // 转换连接优先级规则
        Map<String, Map<String, Integer>> connectionPriorityRules = new HashMap<>();
        Map<String, Map<String, RouteMapRuleService.ConnectionDirection>> connectionDirections = new HashMap<>();
        Map<String, Map<String, Double>> maxDistanceRules = new HashMap<>();
        Map<String, Map<String, Integer>> connectionLimits = new HashMap<>();

        if (ruleRouteMap.getRuleRouteMapConnectionPrioritys() != null) {
            for (RuleRouteMapConnectionPriority priority : ruleRouteMap.getRuleRouteMapConnectionPrioritys()) {
                if (!priority.getEnabled()) {
                    continue; // 跳过未启用的规则
                }

                String sourceType = priority.getSourceStructTypeCode();
                String targetType = priority.getTargetStructTypeCode();

                // 连接优先级规则
                connectionPriorityRules
                        .computeIfAbsent(sourceType, k -> new HashMap<>())
                        .put(targetType, priority.getPriority());

                // 连接方向规则
                connectionDirections
                        .computeIfAbsent(sourceType, k -> new HashMap<>())
                        .put(targetType, convertConnectionDirection(priority.getConnectionDirection()));

                // 最大距离规则
                if (priority.getMaxDistance() != null) {
                    maxDistanceRules
                            .computeIfAbsent(sourceType, k -> new HashMap<>())
                            .put(targetType, priority.getMaxDistance());
                }

                // 连接数量限制
                if (priority.getConnectionLimit() != null) {
                    connectionLimits
                            .computeIfAbsent(sourceType, k -> new HashMap<>())
                            .put(targetType, priority.getConnectionLimit());
                }
            }
        }

        ruleLibrary.setConnectionPriorityRules(connectionPriorityRules);
        ruleLibrary.setConnectionDirections(connectionDirections);
        ruleLibrary.setMaxDistanceRules(maxDistanceRules);
        ruleLibrary.setConnectionLimits(connectionLimits);

        return ruleLibrary;
    }

    /**
     * 转换连接方向枚举
     */
    private RouteMapRuleService.ConnectionDirection convertConnectionDirection(RuleRouteMap.ConnectionDirection direction) {
        if (direction == null) {
            return RouteMapRuleService.ConnectionDirection.BIDIRECTIONAL;
        }

        switch (direction) {
            case BIDIRECTIONAL:
                return RouteMapRuleService.ConnectionDirection.BIDIRECTIONAL;
            case UNIDIRECTIONAL:
                return RouteMapRuleService.ConnectionDirection.UNIDIRECTIONAL;
            case REVERSE_ONLY:
                return RouteMapRuleService.ConnectionDirection.REVERSE_ONLY;
            default:
                return RouteMapRuleService.ConnectionDirection.BIDIRECTIONAL;
        }
    }

    /**
     * 转换连接策略枚举
     */
    private RouteMapRuleService.ConnectionStrategy convertConnectionStrategy(RuleRouteMap.ConnectionStrategy strategy) {
        if (strategy == null) {
            return RouteMapRuleService.ConnectionStrategy.BALANCED;
        }

        switch (strategy) {
            case NEAREST_FIRST:
                return RouteMapRuleService.ConnectionStrategy.NEAREST_FIRST;
            case PRIORITY_FIRST:
                return RouteMapRuleService.ConnectionStrategy.PRIORITY_FIRST;
            case BALANCED:
                return RouteMapRuleService.ConnectionStrategy.BALANCED;
            case MINIMUM_SPANNING:
                return RouteMapRuleService.ConnectionStrategy.MINIMUM_SPANNING;
            default:
                return RouteMapRuleService.ConnectionStrategy.BALANCED;
        }
    }

    /**
     * 获取默认规则
     */
    public RouteMapRuleService.AdvancedRuleLibrary getDefaultRuleLibrary() {
        // 查询默认规则或创建默认规则
        List<RuleRouteMap> defaultRules = eruptDao.queryEntityList(
                RuleRouteMap.class,
                "ruleName = :ruleName",
                Collections.singletonMap("ruleName", "默认规则")
        );

        if (!defaultRules.isEmpty()) {
            return convertToAdvancedRuleLibrary(defaultRules.get(0));
        } else {
            return createAndSaveDefaultRule();
        }
    }

    /**
     * 创建并保存默认规则
     */
    private RouteMapRuleService.AdvancedRuleLibrary createAndSaveDefaultRule() {
        RuleRouteMap defaultRule = new RuleRouteMap();
        defaultRule.setRuleName("默认规则");
        defaultRule.setDescription("系统默认的路线图连接规则");
        defaultRule.setConnectionStrategy(RuleRouteMap.ConnectionStrategy.BALANCED);
        defaultRule.setEnableRoomRule(true);
        defaultRule.setEnableThroughPieceRule(true);
        defaultRule.setMaxConnections(5);

        // 创建默认连接优先级规则
        Set<RuleRouteMapConnectionPriority> priorities = new HashSet<>();

        // 设备连接规则
        priorities.add(createConnectionPriority("设备", "扁条", 1, 500.0, 2));
        priorities.add(createConnectionPriority("设备", "托架", 2, 800.0, 2));
        priorities.add(createConnectionPriority("设备", "支架", 3, 1000.0, 1));

        // 扁条连接规则
        priorities.add(createConnectionPriority("扁条", "设备", 1, 500.0, 3));
        priorities.add(createConnectionPriority("扁条", "托架", 2, 600.0, 2));
        priorities.add(createConnectionPriority("扁条", "贯穿件", 3, 1200.0, 1));

        // 托架连接规则
        priorities.add(createConnectionPriority("托架", "设备", 1, 800.0, 2));
        priorities.add(createConnectionPriority("托架", "扁条", 2, 600.0, 3));
        priorities.add(createConnectionPriority("托架", "支架", 3, 1000.0, 1));

        // 支架连接规则
        priorities.add(createConnectionPriority("支架", "设备", 1, 1000.0, 2));
        priorities.add(createConnectionPriority("支架", "托架", 2, 1000.0, 2));
        priorities.add(createConnectionPriority("支架", "贯穿件", 3, 1500.0, 1));

        defaultRule.setRuleRouteMapConnectionPrioritys(priorities);

        // 保存到数据库
        try {
            eruptDao.persist(defaultRule);
        } catch (Exception e) {
            // 如果保存失败，返回内存中的规则
            System.err.println("保存默认规则失败: " + e.getMessage());
        }

        return convertToAdvancedRuleLibrary(defaultRule);
    }

    /**
     * 创建连接优先级规则
     */
    private RuleRouteMapConnectionPriority createConnectionPriority(
            String sourceType, String targetType, int priority, double maxDistance, int connectionLimit) {
        RuleRouteMapConnectionPriority connectionPriority = new RuleRouteMapConnectionPriority();
        connectionPriority.setSourceStructTypeCode(sourceType);
        connectionPriority.setTargetStructTypeCode(targetType);
        connectionPriority.setPriority(priority);
        connectionPriority.setMaxDistance(maxDistance);
        connectionPriority.setConnectionLimit(connectionLimit);
        connectionPriority.setConnectionDirection(RuleRouteMap.ConnectionDirection.BIDIRECTIONAL);
        connectionPriority.setEnabled(true);
        connectionPriority.setRuleDescription(
                String.format("%s连接到%s，优先级%d，最大距离%.1f，连接限制%d",
                        sourceType, targetType, priority, maxDistance, connectionLimit));
        return connectionPriority;
    }

    /**
     * 验证规则的有效性
     */
    public List<String> validateRule(RuleRouteMap ruleRouteMap) {
        List<String> errors = new ArrayList<>();

        if (ruleRouteMap.getRuleName() == null || ruleRouteMap.getRuleName().trim().isEmpty()) {
            errors.add("规则名称不能为空");
        }

        if (ruleRouteMap.getMaxConnections() == null || ruleRouteMap.getMaxConnections() <= 0) {
            errors.add("最大连接数必须大于0");
        }

        if (ruleRouteMap.getRuleRouteMapConnectionPrioritys() == null || 
            ruleRouteMap.getRuleRouteMapConnectionPrioritys().isEmpty()) {
            errors.add("至少需要配置一条连接优先级规则");
        } else {
            // 验证连接优先级规则
            for (RuleRouteMapConnectionPriority priority : ruleRouteMap.getRuleRouteMapConnectionPrioritys()) {
                if (priority.getSourceStructTypeCode() == null || priority.getSourceStructTypeCode().trim().isEmpty()) {
                    errors.add("源结构类型代码不能为空");
                }
                if (priority.getTargetStructTypeCode() == null || priority.getTargetStructTypeCode().trim().isEmpty()) {
                    errors.add("目标结构类型代码不能为空");
                }
                if (priority.getPriority() == null || priority.getPriority() <= 0) {
                    errors.add("连接优先级必须大于0");
                }
                if (priority.getMaxDistance() != null && priority.getMaxDistance() <= 0) {
                    errors.add("最大距离必须大于0");
                }
                if (priority.getConnectionLimit() != null && priority.getConnectionLimit() <= 0) {
                    errors.add("连接数量限制必须大于0");
                }
            }
        }

        return errors;
    }

    /**
     * 获取所有可用的规则列表
     */
    public List<RuleRouteMap> getAllRules() {
        return eruptDao.queryEntityList(RuleRouteMap.class, "1 = 1", Collections.emptyMap());
    }

    /**
     * 根据名称查找规则
     */
    public RuleRouteMap findRuleByName(String ruleName) {
        List<RuleRouteMap> rules = eruptDao.queryEntityList(
                RuleRouteMap.class,
                "ruleName = :ruleName",
                Collections.singletonMap("ruleName", ruleName)
        );
        return rules.isEmpty() ? null : rules.get(0);
    }
}
