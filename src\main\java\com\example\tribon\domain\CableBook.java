//package com.example.demo.model.tribon.domain;
//
//import lombok.Getter;
//import lombok.Setter;
//import xyz.erupt.annotation.Erupt;
//import xyz.erupt.annotation.EruptField;
//import xyz.erupt.annotation.sub_field.Readonly;
//import xyz.erupt.annotation.sub_field.View;
//import xyz.erupt.annotation.sub_field.Edit;
//import xyz.erupt.annotation.sub_field.sub_edit.Search;
//import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTable;
//import xyz.erupt.jpa.model.BaseModel;
//
//import javax.persistence.Entity;
//import javax.persistence.Table;
//import javax.persistence.ManyToMany;
//import java.util.List;
//
//@Erupt(
//        name = "���²����"
//)
//@Table(name = "tribon_cable_book")
//@Entity
//@Getter
//@Setter
//public class CableBook extends BaseModel {
//    @EruptField(
//            views = @View(title = "��ʼ�豸"),
//            edit = @Edit(title = "��ʼ�豸", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
//    )
//    private String startEquipment;
//
//    @EruptField(
//            views = @View(title = "�����豸"),
//            edit = @Edit(title = "�����豸", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
//    )
//    private String endEquipment;
//
//    @EruptField(
//            views = @View(title = "ͨ���б�"),
//            edit = @Edit(title = "ͨ���б�", type = Edit.Type.REFERENCE_TABLE, referenceTableType = @ReferenceTable(id = "id", label = "name"))
//    )
//    @ManyToMany
//    private List<Channel> channels;
//}
