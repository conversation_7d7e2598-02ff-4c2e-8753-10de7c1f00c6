package com.example.tribon.service;

import com.example.tribon.dto.GenerateRouteMapResponseDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.example.tribon.service.RouteMapService.findConnectionsAdvanced;

public class RouteMapRulePerformanceService {
    // --- 规则管理和验证 ---

    // --- 使用示例和工具方法 ---

    // 使用示例

    // 创建测试节点
    private static List<RouteMapService.Node> createTestNodes() {
        List<RouteMapService.Node> nodes = new ArrayList<>();

        // 创建不同类型的测试节点
        nodes.add(createNode("DEV001", "设备", "ROOM001", 0, 0, 0));
        nodes.add(createNode("FLAT001", "扁条", "ROOM001", 100, 0, 0));
        nodes.add(createNode("BRACKET001", "托架", "ROOM001", 200, 0, 0));
        nodes.add(createNode("SUPPORT001", "支架", "ROOM001", 300, 0, 0));

        nodes.add(createNode("DEV002", "设备", "ROOM002", 0, 100, 0));
        nodes.add(createNode("FLAT002", "扁条", "ROOM002", 100, 100, 0));
        nodes.add(createNode("BRACKET002", "托架", "ROOM002", 200, 100, 0));

        return nodes;
    }

    // 创建节点的辅助方法
    private static RouteMapService.Node createNode(String id, String type, String roomCode, double x, double y, double z) {
        RouteMapService.Node node = new RouteMapService.Node();
        node.setId(id);
        node.setType(type);
        node.setRoomCode(roomCode);
        node.setX(x);
        node.setY(y);
        node.setZ(z);
        node.setThroughPiece(false);
        return node;
    }

    // 性能分析工具
    public static class PerformanceAnalyzer {

        public static PerformanceReport analyzePerformance(
                List<RouteMapService.Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, RouteMapRuleService.ConnectionStrategy strategy) {

            long startTime = System.currentTimeMillis();
            long startMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();

            GenerateRouteMapResponseDto result = findConnectionsAdvanced(nodes, ruleLibrary, strategy, 3);

            long endTime = System.currentTimeMillis();
            long endMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();

            PerformanceReport report = new PerformanceReport();
            report.setExecutionTime(endTime - startTime);
            report.setMemoryUsed(endMemory - startMemory);
            report.setNodeCount(nodes.size());
            report.setConnectionCount(result.getRoutePathList().size());
            report.setStrategy(strategy);

            return report;
        }
    }

    // 性能报告
    @Data
    public static class PerformanceReport {
        private long executionTime;      // 执行时间（毫秒）
        private long memoryUsed;         // 内存使用（字节）
        private int nodeCount;           // 节点数量
        private int connectionCount;     // 连接数量
        private RouteMapRuleService.ConnectionStrategy strategy; // 使用的策略

        @Override
        public String toString() {
            return String.format(
                    "性能报告 [策略=%s, 节点=%d, 连接=%d, 时间=%dms, 内存=%dKB]",
                    strategy, nodeCount, connectionCount, executionTime, memoryUsed / 1024);
        }
    }

}
