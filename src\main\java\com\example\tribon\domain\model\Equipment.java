package com.example.tribon.domain.model;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(
        name = "设备"
)
@Table(name = "tribon_equipment"
)
@Entity
@Getter
@Setter
public class Equipment extends BaseModel {
    @EruptField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", readonly = @Readonly(add = false, edit = true),notNull = true, search = @Search(vague = true))
    )
    private String name;



}
