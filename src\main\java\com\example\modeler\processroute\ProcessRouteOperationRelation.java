package com.example.modeler.processroute;


import com.example.modeler.handler.ProcessOperationDataProxy;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTableType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */
@Table(name = "mf_process_operation")
@Entity
@Erupt(name = "工序",
        power = @Power(importable = false, export = true)
)
public class ProcessRouteOperationRelation extends BaseModel {

    @OneToOne
    @EruptField(
            edit = @Edit(title = "工序编码", notNull = true, search = @Search, type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(label = "operationName")
            )
    )
    private ProcessOperation processOperation;

    @OneToOne
    @EruptField(
            edit = @Edit(title = "工艺路线", notNull = true, search = @Search, type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(label = "routeCode")
            )
    )
    private ProcessRoute processRoute;


    @EruptField(
            edit = @Edit(title = "工艺路线", notNull = true, search = @Search, type = EditType.INPUT)
    )
    private String extText;

}
