package com.example.demo.model.material.biz.domain.model;

import com.example.demo.model.material.api.enumeration.MaterialEnum;
import com.example.demo.model.namingrule.api.enumeration.NamingRuleEnum;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_erupt.LinkTree;
import xyz.erupt.annotation.sub_erupt.Tree;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.AttachmentType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.persistence.criteria.CriteriaBuilder;

@Erupt(
        name = "物料主数据",
        linkTree = @LinkTree(field = "materialCategory")
)
@Table(name = "material",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"materialCode"})
        }
)
@Entity
@Getter
@Setter
public class Material extends BaseModel {
        @EruptField(
                views = @View(title = "编码"),
                edit = @Edit(title = "编码",readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
        )
        private String materialCode;

        @ManyToOne
        @EruptField(
                views = @View(title = "物料分类", column = "code"),
                edit = @Edit(title = "物料分类",readonly = @Readonly(add = false, edit = true),
                        notNull = true, type = EditType.REFERENCE_TREE, referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id"), search = @Search())
        )
        private MaterialCategory materialCategory;

        @EruptField(
                views = @View(title = "物料分类名称"),
                edit = @Edit(title = "物料分类名称",readonly = @Readonly(add = true, edit = true))
        )
        private String materialCategoryName;

        @EruptField(
                views = @View(title = "物料名称"),
                edit = @Edit(title = "物料名称", notNull = true)
        )
        private String materialName;

        @EruptField(
                views = @View(title = "物料简称"),
                edit = @Edit(title = "物料简称", notNull = false)
        )
        private String shortName;

        @EruptField(
                views = @View(title = "物料别名"),
                edit = @Edit(title = "物料别名", notNull = false)
        )
        @Comment("物料英文名称或别名")
        private String aliasName;

        @EruptField(
                views = @View(title = "牌号"),
                edit = @Edit(title = "牌号", notNull = false)
        )
        private String brandCode;

        @EruptField(
                views = @View(title = "规格"),
                edit = @Edit(title = "规格", notNull = false)
        )
        private String materialSpecification;

        @EruptField(
                views = @View(title = "记账单位"),
                edit = @Edit(title = "记账单位", notNull = false)
        )
        private String accountUnit;

        @EruptField(
                views = @View(title = "长度"),
                edit = @Edit(title = "长度", notNull = false)
        )
        private String materialLength;

        @EruptField(
                views = @View(title = "最大库存量"),
                edit = @Edit(title = "最大库存量", notNull = false)
        )
        private Double maxInventoryQuantity;

        @EruptField(
                views = @View(title = "最大复验次数"),
                edit = @Edit(title = "最大复验次数", notNull = false)
        )
        private Double maxReinspectTimes;

        @EruptField(
                views = @View(title = "保质期天数"),
                edit = @Edit(title = "保质期天数", notNull = false)
        )
        private Integer guaranteePeriodDays;

        @EruptField(
                views = @View(title = "当前状态"),
                edit = @Edit(title = "当前状态", notNull = true,type = EditType.CHOICE,search = @Search(vague = true),
                        choiceType = @ChoiceType(
                                fetchHandler = MaterialEnum.class
                        )
                )
        )
        private String currentStatus;

        @EruptField(
                views = @View(title = "供应商"),
                edit = @Edit(title = "供应商", notNull = false)
        )
        private String materialSupplier;

        @EruptField(
                views = @View(title = "附件"),
                edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                        attachmentType = @AttachmentType(maxLimit = 5))
        )
        private String materialDocument;


        @EruptField(
                views = @View(title = "技术标准号"),
                edit = @Edit(title = "技术标准号", notNull = false)
        )
        private String wl09;

        @EruptField(
                views = @View(title = "规格标准号"),
                edit = @Edit(title = "规格标准号", notNull = false)
        )
        private String wl10;

        @EruptField(
                views = @View(title = "加工工艺"),
                edit = @Edit(title = "加工工艺", notNull = false)
        )
        private String wl11;

        @EruptField(
                views = @View(title = "热处理工艺"),
                edit = @Edit(title = "热处理工艺", notNull = false)
        )
        private String wl12;

        @EruptField(
                views = @View(title = "力学性能"),
                edit = @Edit(title = "力学性能", notNull = false)
        )
        private String wl13;

        @EruptField(
                views = @View(title = "特殊要求"),
                edit = @Edit(title = "特殊要求", notNull = false)
        )
        private String wl14;

        @EruptField(
                views = @View(title = "型号"),
                edit = @Edit(title = "型号", notNull = false)
        )
        private String wl16;

        @EruptField(
                views = @View(title = "产品标准号/国外品牌"),
                edit = @Edit(title = "产品标准号/国外品牌", notNull = false)
        )
        private String wl17;

        @EruptField(
                views = @View(title = "颜色"),
                edit = @Edit(title = "颜色", notNull = false)
        )
        private String wl19;

        @EruptField(
                views = @View(title = "材质"),
                edit = @Edit(title = "材质", notNull = false)
        )
        private String wl20;

        @EruptField(
                views = @View(title = "品牌"),
                edit = @Edit(title = "品牌", notNull = false)
        )
        private String wl22;

        @EruptField(
                views = @View(title = "系列"),
                edit = @Edit(title = "系列", notNull = false)
        )
        private String wl23;

        @EruptField(
                views = @View(title = "型号(货号/品种代号)"),
                edit = @Edit(title = "型号(货号/品种代号)", notNull = false)
        )
        private String wl24;

        @EruptField(
                views = @View(title = "强度等级"),
                edit = @Edit(title = "强度等级", notNull = false)
        )
        private String wl25;

        @EruptField(
                views = @View(title = "质地"),
                edit = @Edit(title = "质地", notNull = false)
        )
        private String wl26;

        @EruptField(
                views = @View(title = "制造商"),
                edit = @Edit(title = "制造商", notNull = false)
        )
        private String wl27;

        @EruptField(
                views = @View(title = "性能等级"),
                edit = @Edit(title = "性能等级", notNull = false)
        )
        private String wl28;

        @EruptField(
                views = @View(title = "表面处理"),
                edit = @Edit(title = "表面处理", notNull = false)
        )
        private String wl29;

        @EruptField(
                views = @View(title = "产品等级"),
                edit = @Edit(title = "产品等级", notNull = false)
        )
        private String wl30;

        @EruptField(
                views = @View(title = "前置代号"),
                edit = @Edit(title = "前置代号", notNull = false)
        )
        private String wl31;

        @EruptField(
                views = @View(title = "基本代号"),
                edit = @Edit(title = "基本代号", notNull = false)
        )
        private String wl32;

        @EruptField(
                views = @View(title = "后置代号"),
                edit = @Edit(title = "后置代号", notNull = false)
        )
        private String wl33;

        @EruptField(
                views = @View(title = "封装形式"),
                edit = @Edit(title = "封装形式", notNull = false)
        )
        private String wl35;

        @EruptField(
                views = @View(title = "导线"),
                edit = @Edit(title = "封装形式", notNull = false)
        )
        private String wl38;

        @EruptField(
                views = @View(title = "精度"),
                edit = @Edit(title = "精度", notNull = false)
        )
        private String wl39;

        @EruptField(
                views = @View(title = "证书编号"),
                edit = @Edit(title = "证书编号", notNull = false)
        )
        private String wl46;

        @EruptField(
                views = @View(title = "出厂编号"),
                edit = @Edit(title = "出厂编号", notNull = false)
        )
        private String wl47;









}
