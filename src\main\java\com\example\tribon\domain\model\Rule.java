package com.example.tribon.domain.model;

import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(
       name = "AI规则"
)
@Table(name = "tribon_rule")
@Entity
@Getter
@Setter
public class Rule extends BaseModel {
   @EruptField(
           views = @View(title = "规则名"),
           edit = @Edit(title = "规则名", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
   )
   private String rullName;

   @EruptField(
    views = @View(title = "模型"),
    edit = @Edit(title = "模型", notNull = true, search = @Search(vague = true))
)
private String modelName;

   @EruptField(
    views = @View(title = "规则提示词"),
    edit = @Edit(title = "规则提示词", 
    notNull = true, search = @Search(vague = true),
    type = EditType.TEXTAREA
    )
    )
   @Column(length = 4000)
   private String rullPromote;

    @EruptField(
        views = @View(title = "api地址"),
        edit = @Edit(title = "api地址", 
        notNull = true, search = @Search(vague = true)
        )
        )
        private String apiAddress;


}
