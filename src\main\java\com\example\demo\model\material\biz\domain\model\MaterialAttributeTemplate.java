package com.example.demo.model.material.biz.domain.model;

import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.CheckboxType;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;
import java.util.List;

@Erupt(
        name = "物料属性模板"
)
@Table(name = "material_attribute_template",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
@Entity
@Getter
@Setter
public class MaterialAttributeTemplate extends BaseModel {
    @EruptField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", notNull = true)
    )
    private String code;

    @EruptField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true)
    )
    private String name;

    @ManyToMany  //多对多
    @JoinTable(
            name = "e_materialattrtemplate_materialfield", //中间表表名，如下为中间表的定义
            joinColumns = @JoinColumn(name = "template_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "field_id", referencedColumnName = "id"))
    @EruptField(
            edit = @Edit(
                    title = "属性字段",
                    type = EditType.TAB_TABLE_REFER
            )
    )
    private List<MaterialModelField> fields;

}
