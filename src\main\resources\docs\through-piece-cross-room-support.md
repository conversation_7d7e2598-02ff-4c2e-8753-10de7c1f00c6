# 贯穿件跨房间连接支持

## 🎯 功能概述

贯穿件（Through Piece）是一种特殊的结构件，可以跨越多个房间，因此能够与不同房间内的结构件建立连接。这个功能对于船舶设计中的管道、电缆等跨房间设施的路线规划至关重要。

## 🔧 模型设计

### 1. Structure 模型增强

#### 新增字段
```java
/**
 * 贯穿件房间编码列表（贯穿件可以跨多个房间）
 */
@ElementCollection
@CollectionTable(name = "structure_through_piece_rooms", joinColumns = @JoinColumn(name = "structure_id"))
@EruptField(
    views = @View(title = "贯穿件房间列表"),
    edit = @Edit(title = "贯穿件房间列表", type = EditType.TAGS)
)
private List<String> throughPieceRoomCodes;
```

#### 便捷方法
```java
/**
 * 获取结构件的所有房间编码
 * 对于普通结构件，返回单个房间编码
 * 对于贯穿件，返回所有关联的房间编码
 */
public List<String> getAllRoomCodes() {
    if (Boolean.TRUE.equals(throughPiece) && throughPieceRoomCodes != null && !throughPieceRoomCodes.isEmpty()) {
        return throughPieceRoomCodes;
    } else if (roomCode != null) {
        return Arrays.asList(roomCode);
    } else {
        return Collections.emptyList();
    }
}

/**
 * 检查结构件是否属于指定房间
 */
public boolean belongsToRoom(String targetRoomCode) {
    if (Boolean.TRUE.equals(throughPiece) && throughPieceRoomCodes != null) {
        return throughPieceRoomCodes.contains(targetRoomCode);
    } else {
        return targetRoomCode.equals(roomCode);
    }
}
```

### 2. Node 类增强

#### 新增字段和方法
```java
@Data
public static class Node {
    private String roomCode; // 主房间编码（普通结构件使用）
    private List<String> throughPieceRoomCodes; // 贯穿件房间编码列表
    private boolean throughPiece;

    /**
     * 获取节点的所有房间编码
     */
    public List<String> getAllRoomCodes() {
        if (throughPiece && throughPieceRoomCodes != null && !throughPieceRoomCodes.isEmpty()) {
            return throughPieceRoomCodes;
        } else if (roomCode != null) {
            return Arrays.asList(roomCode);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 检查节点是否属于指定房间
     */
    public boolean belongsToRoom(String targetRoomCode) {
        if (throughPiece && throughPieceRoomCodes != null) {
            return throughPieceRoomCodes.contains(targetRoomCode);
        } else {
            return targetRoomCode.equals(roomCode);
        }
    }
}
```

## 🚀 算法实现

### 1. KD-Tree 构建优化

#### 原来的逻辑（有问题）
```java
// 只处理普通结构件，贯穿件被排除
Map<String, List<Node>> nodesByRoom = nodes.stream()
    .filter(n -> !n.isThroughPiece() && n.getRoomCode() != null)
    .collect(Collectors.groupingBy(Node::getRoomCode));
```

#### 新的逻辑（支持贯穿件）
```java
// 处理所有节点，包括贯穿件
for (Node node : nodes) {
    List<String> roomCodes = node.getAllRoomCodes();
    
    // 将节点添加到它所属的所有房间中
    for (String roomCode : roomCodes) {
        if (roomCode != null) {
            nodesByRoom.computeIfAbsent(roomCode, k -> new ArrayList<>()).add(node);
        }
    }
}
```

**效果**：
- 普通结构件：只出现在一个房间的 KD-Tree 中
- 贯穿件：出现在多个房间的 KD-Tree 中，可以被不同房间的节点找到

### 2. 候选节点获取

#### getCandidateNodes 方法
```java
private static List<Node> getCandidateNodes(Node src, Map<String, KdTree> kdByRoom, 
        RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int kPerPriority) {
    
    Set<Node> candidateSet = new HashSet<>();
    
    // 获取源节点的所有房间编码
    List<String> srcRoomCodes = src.getAllRoomCodes();
    
    for (String roomCode : srcRoomCodes) {
        KdTree tree = kdByRoom.get(roomCode);
        if (tree != null) {
            List<Node> roomCandidates = tree.findKNearest(src, searchCount);
            
            for (Node candidate : roomCandidates) {
                if (canConnect(src, candidate, ruleLibrary)) {
                    candidateSet.add(candidate);
                }
            }
        }
    }
    
    return new ArrayList<>(candidateSet);
}
```

### 3. 连接规则检查

#### canConnect 方法
```java
private static boolean canConnect(Node src, Node target, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary) {
    // 如果禁用了房间规则，任何节点都可以连接
    if (!ruleLibrary.getEnableRoomRule()) {
        return true;
    }
    
    // 获取源节点和目标节点的房间编码
    List<String> srcRooms = src.getAllRoomCodes();
    List<String> targetRooms = target.getAllRoomCodes();
    
    // 检查是否有共同房间
    for (String srcRoom : srcRooms) {
        if (targetRooms.contains(srcRoom)) {
            return true; // 同房间可以连接
        }
    }
    
    // 如果启用了贯穿件规则，检查跨房间连接
    if (ruleLibrary.getEnableThroughPieceRule()) {
        // 如果源节点或目标节点是贯穿件，可以跨房间连接
        if (src.isThroughPiece() || target.isThroughPiece()) {
            return true;
        }
    }
    
    return false; // 不同房间且没有贯穿件，不能连接
}
```

## 📊 连接规则矩阵

| 源节点类型 | 目标节点类型 | 房间关系 | 房间规则 | 贯穿件规则 | 连接结果 |
|------------|--------------|----------|----------|------------|----------|
| 普通结构件 | 普通结构件 | 同房间 | 启用 | - | ✅ 可连接 |
| 普通结构件 | 普通结构件 | 不同房间 | 启用 | - | ❌ 不可连接 |
| 普通结构件 | 贯穿件 | 同房间 | 启用 | 启用 | ✅ 可连接 |
| 普通结构件 | 贯穿件 | 不同房间 | 启用 | 启用 | ✅ 可连接（贯穿件跨房间）|
| 贯穿件 | 普通结构件 | 同房间 | 启用 | 启用 | ✅ 可连接 |
| 贯穿件 | 普通结构件 | 不同房间 | 启用 | 启用 | ✅ 可连接（贯穿件跨房间）|
| 贯穿件 | 贯穿件 | 任意房间 | 启用 | 启用 | ✅ 可连接 |
| 任意 | 任意 | 任意 | 禁用 | - | ✅ 可连接 |

## 🧪 测试验证

### 测试场景
```java
@Test
void testThroughPieceCrossRoomConnection() {
    // 创建测试节点：不同房间的设备和跨房间的贯穿件
    Node dev1 = createNode("DEV001", "设备", "ROOM001", 0, 0, 0);      // 房间1设备
    Node dev2 = createNode("DEV002", "设备", "ROOM002", 500, 0, 0);    // 房间2设备
    Node throughPiece = createThroughPieceNode("THROUGH001", "贯穿件", 
        Arrays.asList("ROOM001", "ROOM002"), 250, 0, 0);              // 跨房间贯穿件
    
    // 验证贯穿件能够连接不同房间的设备
    assertTrue(dev1ToThroughPiece || dev2ToThroughPiece, 
        "贯穿件应该能够连接到不同房间的设备");
}
```

### 验证点
- ✅ 贯穿件出现在多个房间的 KD-Tree 中
- ✅ 贯穿件可以与不同房间的普通结构件连接
- ✅ 普通结构件可以通过贯穿件实现跨房间连接
- ✅ 房间规则和贯穿件规则正确协作

## 🎯 使用场景

### 1. 船舶管道系统
```
贯穿件：主管道（跨越机舱、货舱、甲板）
连接：各房间的设备通过主管道连接
```

### 2. 电缆系统
```
贯穿件：主电缆（跨越多个舱室）
连接：各房间的电气设备通过主电缆连接
```

### 3. 通风系统
```
贯穿件：主风管（跨越多个房间）
连接：各房间的通风设备通过主风管连接
```

## 📋 配置建议

### 1. 前端配置
```
结构件类型：贯穿件
是否贯穿件：是
贯穿件房间列表：["ROOM001", "ROOM002", "ROOM003"]
```

### 2. 规则配置
```java
AdvancedRuleLibrary rules = RuleLibraryBuilder.create()
    .setEnableRoomRule(true)           // 启用房间规则
    .setEnableThroughPieceRule(true)   // 启用贯穿件规则
    .addConnectionRule("设备", "贯穿件", 1)
    .addConnectionRule("贯穿件", "设备", 1)
    .build();
```

## 🚀 总结

通过这次增强，系统现在完全支持贯穿件的跨房间连接：

1. **✅ 模型支持**：Structure 和 Node 都支持多房间编码
2. **✅ 算法优化**：KD-Tree 构建和候选查找支持贯穿件
3. **✅ 规则完善**：连接规则正确处理跨房间场景
4. **✅ 测试验证**：完整的测试覆盖各种连接场景

这个功能让路线图计算能够更准确地反映船舶设计中的实际连接关系，特别是对于管道、电缆等跨房间设施的路线规划。
