package com.example.tribon.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基础的电缆走线测试，用于验证算法的基本正确性
 */
@SpringBootTest
public class CableRoutingBasicTest {

    @Test
    void testBasicTwoTrayConnection() {
        System.out.println("=== 基础两托架连接测试 ===");
        
        // 创建两个简单的托架，水平相邻
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20),     // 起点：中心(0,0,0)
            new CableRoutingSolverService.Tray(1, 200, 0, 0, 100, 50, 20)    // 终点：中心(200,0,0)
        );
        
        System.out.println("托架配置:");
        System.out.println("  起点托架: 中心(0,0,0), 尺寸100×50×20, zTop=" + trays.get(0).zTop);
        System.out.println("  终点托架: 中心(200,0,0), 尺寸100×50×20, zTop=" + trays.get(1).zTop);
        
        // 手动计算预期的边缘
        System.out.println("预期边缘:");
        System.out.println("  起点托架右边缘: x=50, y∈[-25,25], z=10");
        System.out.println("  终点托架左边缘: x=150, y∈[-25,25], z=10");
        System.out.println("  最短连接应该是: (50,0,10) → (150,0,10)");
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        assertFalse(segments.isEmpty(), "应该能找到路径");
        
        if (!segments.isEmpty()) {
            System.out.println("实际计算结果:");
            for (int i = 0; i < segments.size(); i++) {
                CableRoutingSolverService.Segment seg = segments.get(i);
                System.out.println("  段" + i + ": " + seg.start + " → " + seg.end);
            }
            
            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);
            
            System.out.println("路径端点:");
            System.out.println("  起点: " + firstSeg.start);
            System.out.println("  终点: " + lastSeg.end);
            
            // 验证起点在起点托架范围内
            assertTrue(firstSeg.start.x >= -50 && firstSeg.start.x <= 50, 
                      "起点X应该在起点托架范围内[-50,50]: " + firstSeg.start.x);
            assertTrue(firstSeg.start.y >= -25 && firstSeg.start.y <= 25, 
                      "起点Y应该在起点托架范围内[-25,25]: " + firstSeg.start.y);
            assertEquals(10.0, firstSeg.start.z, 0.001, "起点Z应该在托架顶面");
            
            // 验证终点在终点托架范围内
            assertTrue(lastSeg.end.x >= 150 && lastSeg.end.x <= 250, 
                      "终点X应该在终点托架范围内[150,250]: " + lastSeg.end.x);
            assertTrue(lastSeg.end.y >= -25 && lastSeg.end.y <= 25, 
                      "终点Y应该在终点托架范围内[-25,25]: " + lastSeg.end.y);
            assertEquals(10.0, lastSeg.end.z, 0.001, "终点Z应该在托架顶面");
            
            System.out.println("✅ 基础连接测试通过");
        }
    }

    @Test
    void testRealWorldData() {
        System.out.println("\n=== 实际数据测试 ===");
        
        // 使用实际的问题数据
        double[] startCoords = {-938.*************, -209.**************, 1612.***********};
        double[] endCoords = {1391.************, -989.*************, 1601.*********};
        double length = 950, width = 500, height = 240;
        
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, startCoords[0], startCoords[1], startCoords[2], 
                                             length, width, height),
            new CableRoutingSolverService.Tray(1, endCoords[0], endCoords[1], endCoords[2], 
                                             length, width, height)
        );
        
        System.out.println("实际数据:");
        System.out.println("  起点中心: (" + startCoords[0] + ", " + startCoords[1] + ", " + startCoords[2] + ")");
        System.out.println("  终点中心: (" + endCoords[0] + ", " + endCoords[1] + ", " + endCoords[2] + ")");
        System.out.println("  托架尺寸: " + length + "×" + width + "×" + height);
        
        // 计算预期范围
        double startHalfL = length / 2, startHalfW = width / 2;
        double endHalfL = length / 2, endHalfW = width / 2;
        
        System.out.println("预期范围:");
        System.out.println("  起点托架: X[" + (startCoords[0] - startHalfL) + ", " + (startCoords[0] + startHalfL) + 
                          "], Y[" + (startCoords[1] - startHalfW) + ", " + (startCoords[1] + startHalfW) + "]");
        System.out.println("  终点托架: X[" + (endCoords[0] - endHalfL) + ", " + (endCoords[0] + endHalfL) + 
                          "], Y[" + (endCoords[1] - endHalfW) + ", " + (endCoords[1] + endHalfW) + "]");
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        if (!segments.isEmpty()) {
            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);
            
            System.out.println("计算结果:");
            System.out.println("  起点端点: " + firstSeg.start);
            System.out.println("  终点端点: " + lastSeg.end);
            
            // 检查起点是否在合理范围内
            boolean startXOk = firstSeg.start.x >= (startCoords[0] - startHalfL - 1) && 
                              firstSeg.start.x <= (startCoords[0] + startHalfL + 1);
            boolean startYOk = firstSeg.start.y >= (startCoords[1] - startHalfW - 1) && 
                              firstSeg.start.y <= (startCoords[1] + startHalfW + 1);
            
            // 检查终点是否在合理范围内
            boolean endXOk = lastSeg.end.x >= (endCoords[0] - endHalfL - 1) && 
                            lastSeg.end.x <= (endCoords[0] + endHalfL + 1);
            boolean endYOk = lastSeg.end.y >= (endCoords[1] - endHalfW - 1) && 
                            lastSeg.end.y <= (endCoords[1] + endHalfW + 1);
            
            System.out.println("验证结果:");
            System.out.println("  起点X范围正确: " + startXOk + " (实际: " + firstSeg.start.x + 
                              ", 预期: [" + (startCoords[0] - startHalfL) + ", " + (startCoords[0] + startHalfL) + "])");
            System.out.println("  起点Y范围正确: " + startYOk + " (实际: " + firstSeg.start.y + 
                              ", 预期: [" + (startCoords[1] - startHalfW) + ", " + (startCoords[1] + startHalfW) + "])");
            System.out.println("  终点X范围正确: " + endXOk + " (实际: " + lastSeg.end.x + 
                              ", 预期: [" + (endCoords[0] - endHalfL) + ", " + (endCoords[0] + endHalfL) + "])");
            System.out.println("  终点Y范围正确: " + endYOk + " (实际: " + lastSeg.end.y + 
                              ", 预期: [" + (endCoords[1] - endHalfW) + ", " + (endCoords[1] + endHalfW) + "])");
            
            if (startXOk && startYOk && endXOk && endYOk) {
                System.out.println("✅ 实际数据测试通过");
            } else {
                System.out.println("❌ 实际数据测试失败 - 坐标超出预期范围");
                
                // 输出详细的错误信息
                if (!startXOk || !startYOk) {
                    System.out.println("起点坐标异常:");
                    System.out.println("  实际起点: " + firstSeg.start);
                    System.out.println("  起点托架中心: (" + startCoords[0] + ", " + startCoords[1] + ")");
                    System.out.println("  起点托架尺寸: " + length + "×" + width);
                }
                
                if (!endXOk || !endYOk) {
                    System.out.println("终点坐标异常:");
                    System.out.println("  实际终点: " + lastSeg.end);
                    System.out.println("  终点托架中心: (" + endCoords[0] + ", " + endCoords[1] + ")");
                    System.out.println("  终点托架尺寸: " + length + "×" + width);
                }
            }
        } else {
            fail("未找到路径");
        }
    }

    @Test
    void testRotatedTrayConnection() {
        System.out.println("\n=== 旋转托架连接测试 ===");

        // 创建两个托架，其中一个有旋转
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20), // 起点：无旋转
            new CableRoutingSolverService.Tray(1, 200, 0, 0, 100, 50, 20,
                                             Math.toRadians(45), 0, 0)    // 终点：X轴旋转45度
        );

        System.out.println("托架配置:");
        System.out.println("  起点托架: 中心(0,0,0), 尺寸100×50×20, 无旋转");
        System.out.println("  终点托架: 中心(200,0,0), 尺寸100×50×20, X轴旋转45度");

        // 验证旋转后的顶点计算
        CableRoutingSolverService.Point3D[] vertices = trays.get(1).getTopVertices();
        System.out.println("终点托架旋转后的顶点:");
        for (int i = 0; i < vertices.length; i++) {
            System.out.println("  顶点" + i + ": " + vertices[i]);
        }

        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);

        assertFalse(segments.isEmpty(), "应该能找到路径");

        if (!segments.isEmpty()) {
            System.out.println("计算结果:");
            for (int i = 0; i < segments.size(); i++) {
                CableRoutingSolverService.Segment seg = segments.get(i);
                System.out.println("  段" + i + ": " + seg.start + " → " + seg.end);
            }

            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);

            System.out.println("路径端点:");
            System.out.println("  起点: " + firstSeg.start);
            System.out.println("  终点: " + lastSeg.end);

            // 验证起点仍在起点托架范围内（无旋转）
            assertTrue(firstSeg.start.x >= -50 && firstSeg.start.x <= 50,
                      "起点X应该在起点托架范围内");
            assertTrue(firstSeg.start.y >= -25 && firstSeg.start.y <= 25,
                      "起点Y应该在起点托架范围内");

            System.out.println("✅ 旋转托架连接测试通过");
        }
    }

    @Test
    void testRotationParsing() {
        System.out.println("\n=== 旋转角度解析测试 ===");

        // 测试旋转变换函数
        CableRoutingSolverService.Point3D originalPoint = new CableRoutingSolverService.Point3D(1, 0, 0);

        // 测试Z轴旋转90度
        CableRoutingSolverService.Point3D rotatedPoint =
            CableRoutingSolverService.applyRotation(originalPoint, 0, 0, Math.toRadians(90));

        System.out.println("原始点: " + originalPoint);
        System.out.println("Z轴旋转90度后: " + rotatedPoint);

        // 验证旋转结果（90度旋转后，(1,0,0) 应该变成 (0,1,0)）
        assertEquals(0.0, rotatedPoint.x, 0.001, "X坐标应该接近0");
        assertEquals(1.0, rotatedPoint.y, 0.001, "Y坐标应该接近1");
        assertEquals(0.0, rotatedPoint.z, 0.001, "Z坐标应该保持0");

        System.out.println("✅ 旋转角度解析测试通过");
    }

    @Test
    void testNarrowEdgeCenterConnection() {
        System.out.println("\n=== 窄边中心点连接测试 ===");

        // 创建两个托架，测试窄边连接
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 200, 50, 20),     // 起点：长200，宽50
            new CableRoutingSolverService.Tray(1, 0, 100, 0, 200, 50, 20)    // 终点：Y方向偏移100
        );

        System.out.println("托架配置:");
        System.out.println("  起点托架: 中心(0,0,0), 尺寸200×50×20");
        System.out.println("  终点托架: 中心(0,100,0), 尺寸200×50×20");
        System.out.println("  预期：应该通过窄边（上下边）的中心点连接");

        // 验证边缘生成
        CableRoutingSolverService.Edge[] startEdges = trays.get(0).getTopEdges();
        CableRoutingSolverService.Edge[] endEdges = trays.get(1).getTopEdges();

        System.out.println("起点托架边缘:");
        for (int i = 0; i < startEdges.length; i++) {
            CableRoutingSolverService.Edge edge = startEdges[i];
            System.out.println("  边" + i + " (side " + edge.side + "): " + edge.start + " → " + edge.end);

            // 验证窄边（左右边，side 2和3）是否为点
            if (edge.side == 2 || edge.side == 3) {
                assertEquals(edge.start.x, edge.end.x, 0.001, "窄边应该是点（X坐标相同）");
                assertEquals(edge.start.y, edge.end.y, 0.001, "窄边应该是点（Y坐标相同）");
                assertEquals(edge.start.z, edge.end.z, 0.001, "窄边应该是点（Z坐标相同）");
                System.out.println("    ✅ 窄边" + edge.side + "正确设置为中心点");
            }
        }

        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);

        assertFalse(segments.isEmpty(), "应该能找到路径");

        if (!segments.isEmpty()) {
            System.out.println("计算结果:");
            for (int i = 0; i < segments.size(); i++) {
                CableRoutingSolverService.Segment seg = segments.get(i);
                System.out.println("  段" + i + ": " + seg.start + " → " + seg.end);
            }

            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);

            System.out.println("路径端点:");
            System.out.println("  起点: " + firstSeg.start);
            System.out.println("  终点: " + lastSeg.end);

            // 验证连接点的合理性
            // 由于托架在Y方向相邻，应该通过窄边（上下边）连接
            // 起点应该在起点托架的上边（Y=25）附近
            // 终点应该在终点托架的下边（Y=75）附近

            assertTrue(Math.abs(firstSeg.start.y - 25) < 30,
                      "起点Y坐标应该接近起点托架的上边: " + firstSeg.start.y);
            assertTrue(Math.abs(lastSeg.end.y - 75) < 30,
                      "终点Y坐标应该接近终点托架的下边: " + lastSeg.end.y);

            System.out.println("✅ 窄边中心点连接测试通过");
        }
    }

    @Test
    void testEdgeTypeIdentification() {
        System.out.println("\n=== 边缘类型识别测试 ===");

        // 创建一个托架并检查其边缘类型
        CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20);
        CableRoutingSolverService.Edge[] edges = tray.getTopEdges();

        System.out.println("托架尺寸: 100×50×20");
        System.out.println("边缘分析:");

        for (int i = 0; i < edges.length; i++) {
            CableRoutingSolverService.Edge edge = edges[i];
            boolean isPoint = (edge.start.x == edge.end.x &&
                              edge.start.y == edge.end.y &&
                              edge.start.z == edge.end.z);

            String edgeType = "";
            switch (edge.side) {
                case 0: edgeType = "下边（长边）"; break;
                case 1: edgeType = "上边（长边）"; break;
                case 2: edgeType = "左边（窄边）"; break;
                case 3: edgeType = "右边（窄边）"; break;
            }

            System.out.println("  边" + i + " " + edgeType + ": " + edge.start + " → " + edge.end +
                              (isPoint ? " [点]" : " [线段]"));

            // 验证：长边应该是线段，窄边应该是点
            if (edge.side == 0 || edge.side == 1) {
                assertFalse(isPoint, "长边应该是线段，不是点");
            } else {
                assertTrue(isPoint, "窄边应该是点，不是线段");
            }
        }

        System.out.println("✅ 边缘类型识别测试通过");
    }
}
