package com.example.tribon.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基础的电缆走线测试，用于验证算法的基本正确性
 */
@SpringBootTest
public class CableRoutingBasicTest {

    @Test
    void testBasicTwoTrayConnection() {
        System.out.println("=== 基础两托架连接测试 ===");
        
        // 创建两个简单的托架，水平相邻
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20),     // 起点：中心(0,0,0)
            new CableRoutingSolverService.Tray(1, 200, 0, 0, 100, 50, 20)    // 终点：中心(200,0,0)
        );
        
        System.out.println("托架配置:");
        System.out.println("  起点托架: 中心(0,0,0), 尺寸100×50×20, zTop=" + trays.get(0).zTop);
        System.out.println("  终点托架: 中心(200,0,0), 尺寸100×50×20, zTop=" + trays.get(1).zTop);
        
        // 手动计算预期的边缘
        System.out.println("预期边缘:");
        System.out.println("  起点托架右边缘: x=50, y∈[-25,25], z=10");
        System.out.println("  终点托架左边缘: x=150, y∈[-25,25], z=10");
        System.out.println("  最短连接应该是: (50,0,10) → (150,0,10)");
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        assertFalse(segments.isEmpty(), "应该能找到路径");
        
        if (!segments.isEmpty()) {
            System.out.println("实际计算结果:");
            for (int i = 0; i < segments.size(); i++) {
                CableRoutingSolverService.Segment seg = segments.get(i);
                System.out.println("  段" + i + ": " + seg.start + " → " + seg.end);
            }
            
            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);
            
            System.out.println("路径端点:");
            System.out.println("  起点: " + firstSeg.start);
            System.out.println("  终点: " + lastSeg.end);
            
            // 验证起点在起点托架范围内
            assertTrue(firstSeg.start.x >= -50 && firstSeg.start.x <= 50, 
                      "起点X应该在起点托架范围内[-50,50]: " + firstSeg.start.x);
            assertTrue(firstSeg.start.y >= -25 && firstSeg.start.y <= 25, 
                      "起点Y应该在起点托架范围内[-25,25]: " + firstSeg.start.y);
            assertEquals(10.0, firstSeg.start.z, 0.001, "起点Z应该在托架顶面");
            
            // 验证终点在终点托架范围内
            assertTrue(lastSeg.end.x >= 150 && lastSeg.end.x <= 250, 
                      "终点X应该在终点托架范围内[150,250]: " + lastSeg.end.x);
            assertTrue(lastSeg.end.y >= -25 && lastSeg.end.y <= 25, 
                      "终点Y应该在终点托架范围内[-25,25]: " + lastSeg.end.y);
            assertEquals(10.0, lastSeg.end.z, 0.001, "终点Z应该在托架顶面");
            
            System.out.println("✅ 基础连接测试通过");
        }
    }

    @Test
    void testRealWorldData() {
        System.out.println("\n=== 实际数据测试 ===");
        
        // 使用实际的问题数据
        double[] startCoords = {-938.*************, -209.**************, 1612.***********};
        double[] endCoords = {1391.************, -989.*************, 1601.*********};
        double length = 950, width = 500, height = 240;
        
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, startCoords[0], startCoords[1], startCoords[2], 
                                             length, width, height),
            new CableRoutingSolverService.Tray(1, endCoords[0], endCoords[1], endCoords[2], 
                                             length, width, height)
        );
        
        System.out.println("实际数据:");
        System.out.println("  起点中心: (" + startCoords[0] + ", " + startCoords[1] + ", " + startCoords[2] + ")");
        System.out.println("  终点中心: (" + endCoords[0] + ", " + endCoords[1] + ", " + endCoords[2] + ")");
        System.out.println("  托架尺寸: " + length + "×" + width + "×" + height);
        
        // 计算预期范围
        double startHalfL = length / 2, startHalfW = width / 2;
        double endHalfL = length / 2, endHalfW = width / 2;
        
        System.out.println("预期范围:");
        System.out.println("  起点托架: X[" + (startCoords[0] - startHalfL) + ", " + (startCoords[0] + startHalfL) + 
                          "], Y[" + (startCoords[1] - startHalfW) + ", " + (startCoords[1] + startHalfW) + "]");
        System.out.println("  终点托架: X[" + (endCoords[0] - endHalfL) + ", " + (endCoords[0] + endHalfL) + 
                          "], Y[" + (endCoords[1] - endHalfW) + ", " + (endCoords[1] + endHalfW) + "]");
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        if (!segments.isEmpty()) {
            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);
            
            System.out.println("计算结果:");
            System.out.println("  起点端点: " + firstSeg.start);
            System.out.println("  终点端点: " + lastSeg.end);
            
            // 检查起点是否在合理范围内
            boolean startXOk = firstSeg.start.x >= (startCoords[0] - startHalfL - 1) && 
                              firstSeg.start.x <= (startCoords[0] + startHalfL + 1);
            boolean startYOk = firstSeg.start.y >= (startCoords[1] - startHalfW - 1) && 
                              firstSeg.start.y <= (startCoords[1] + startHalfW + 1);
            
            // 检查终点是否在合理范围内
            boolean endXOk = lastSeg.end.x >= (endCoords[0] - endHalfL - 1) && 
                            lastSeg.end.x <= (endCoords[0] + endHalfL + 1);
            boolean endYOk = lastSeg.end.y >= (endCoords[1] - endHalfW - 1) && 
                            lastSeg.end.y <= (endCoords[1] + endHalfW + 1);
            
            System.out.println("验证结果:");
            System.out.println("  起点X范围正确: " + startXOk + " (实际: " + firstSeg.start.x + 
                              ", 预期: [" + (startCoords[0] - startHalfL) + ", " + (startCoords[0] + startHalfL) + "])");
            System.out.println("  起点Y范围正确: " + startYOk + " (实际: " + firstSeg.start.y + 
                              ", 预期: [" + (startCoords[1] - startHalfW) + ", " + (startCoords[1] + startHalfW) + "])");
            System.out.println("  终点X范围正确: " + endXOk + " (实际: " + lastSeg.end.x + 
                              ", 预期: [" + (endCoords[0] - endHalfL) + ", " + (endCoords[0] + endHalfL) + "])");
            System.out.println("  终点Y范围正确: " + endYOk + " (实际: " + lastSeg.end.y + 
                              ", 预期: [" + (endCoords[1] - endHalfW) + ", " + (endCoords[1] + endHalfW) + "])");
            
            if (startXOk && startYOk && endXOk && endYOk) {
                System.out.println("✅ 实际数据测试通过");
            } else {
                System.out.println("❌ 实际数据测试失败 - 坐标超出预期范围");
                
                // 输出详细的错误信息
                if (!startXOk || !startYOk) {
                    System.out.println("起点坐标异常:");
                    System.out.println("  实际起点: " + firstSeg.start);
                    System.out.println("  起点托架中心: (" + startCoords[0] + ", " + startCoords[1] + ")");
                    System.out.println("  起点托架尺寸: " + length + "×" + width);
                }
                
                if (!endXOk || !endYOk) {
                    System.out.println("终点坐标异常:");
                    System.out.println("  实际终点: " + lastSeg.end);
                    System.out.println("  终点托架中心: (" + endCoords[0] + ", " + endCoords[1] + ")");
                    System.out.println("  终点托架尺寸: " + length + "×" + width);
                }
            }
        } else {
            fail("未找到路径");
        }
    }
}
