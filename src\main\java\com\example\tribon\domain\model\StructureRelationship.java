package com.example.tribon.domain.model;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.util.List;

@Erupt(
        name = "安装件关系"
)
@Table(name = "tribon_structure_relationship", uniqueConstraints = {@UniqueConstraint(columnNames = {"structCodeA", "structCodeB"})}
)
@Entity
@Getter
@Setter
public class StructureRelationship extends BaseModel {

    @EruptField(
            views = @View(title = "唯一编号A"),
            edit = @Edit(title = "唯一编号A",
                    notNull = true, search = @Search(vague = true))
    )
    private String structCodeA;

    @ElementCollection
    @CollectionTable(name = "tribon_structure_relationship_flagX1HandlesA", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX1HandlesA;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_relationship_flagX2HandlesA", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX2HandlesA;
    private String cogXMark1StructCodeA;
    private String cogXMark2StructCodeA;

    @EruptField(
            views = @View(title = "唯一编号B"),
            edit = @Edit(title = "唯一编号B",
                    notNull = true, search = @Search(vague = true))
    )
    private String structCodeB;

    @ElementCollection
    @CollectionTable(name = "tribon_structure_relationship_flagX1HandlesB", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX1HandlesB;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_relationship_flagX2HandlesB", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX2HandlesB;
    private String cogXMark1StructCodeB;
    private String cogXMark2StructCodeB;

    /**
     * 关系边在绘图中的handle
     * 使用一个Struct标记路径关系
     */
//    Long lineHandle;
    String edgeStructCode;

    @ElementCollection
    @CollectionTable(name = "tribon_structure_relationship_edgeStructHandle", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> edgeStructHandle;


}
