package com.example.tenant.handler;

import com.example.modeler.processroute.ProcessOperation;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.jpa.model.BaseModel;

import java.util.Set;

@Erupt(name = "登录检查")
public class LoginCheckOperator extends BaseModel {
    @EruptField(
            views = @View(title = "用户id"),
            edit = @Edit(title = "用户id"
                    )
    )
    private Long userId;

    public Long getUserId(){
        return userId;
    }

}
