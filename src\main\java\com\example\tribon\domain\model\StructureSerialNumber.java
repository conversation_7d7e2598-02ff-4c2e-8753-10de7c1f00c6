package com.example.tribon.domain.model;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Erupt(
        name = "Structure序号表")
@Table(name = "tribon_structure_serial_number", uniqueConstraints = {@UniqueConstraint(columnNames = {"projectName", "structureType"})}
)
@Entity
@Getter
@Setter
public class StructureSerialNumber extends BaseModel {

    @EruptField(
            views = @View(title = "项目名称"),
            edit = @Edit(title = "项目名称",
                    notNull = true, search = @Search(vague = true))
    )
    private String projectName;
    @EruptField(
            views = @View(title = "安装件类型"),
            edit = @Edit(title = "安装件类型",
                    notNull = true, search = @Search(vague = true))
    )
    private String structureType;
    @EruptField(
            views = @View(title = "当前序号"),
            edit = @Edit(title = "当前序号",
                    notNull = true, search = @Search(vague = true))
    )
    private Long currentSerialNumber;
}
