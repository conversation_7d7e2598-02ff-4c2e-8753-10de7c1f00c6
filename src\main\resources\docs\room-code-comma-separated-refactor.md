# 房间编码逗号分隔重构

## 🎯 重构目标

简化贯穿件的房间编码存储方式，不使用额外的 `throughPieceRoomCodes` 字段，直接在 `roomCode` 中使用逗号分隔多个房间编码，并确保只有贯穿件才允许有多个值。

## 🔧 重构内容

### 1. 移除额外字段

#### 重构前
```java
private String roomCode;                    // 普通结构件房间编码
private List<String> throughPieceRoomCodes; // 贯穿件房间编码列表
```

#### 重构后
```java
/**
 * 房间编号
 * 普通结构件：单个房间编号
 * 贯穿件：多个房间编号，使用逗号分隔（如：ROOM001,ROOM002,ROOM003）
 */
private String roomCode;
```

### 2. 更新解析逻辑

#### getAllRoomCodes() 方法
```java
public List<String> getAllRoomCodes() {
    if (roomCode == null || roomCode.trim().isEmpty()) {
        return Collections.emptyList();
    }
    
    if (Boolean.TRUE.equals(throughPiece)) {
        // 贯穿件：解析逗号分隔的房间编码
        return Arrays.asList(roomCode.split(","))
                .stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(java.util.stream.Collectors.toList());
    } else {
        // 普通结构件：单个房间编码
        return Arrays.asList(roomCode.trim());
    }
}
```

#### belongsToRoom() 方法
```java
public boolean belongsToRoom(String targetRoomCode) {
    if (targetRoomCode == null || roomCode == null) {
        return false;
    }
    
    if (Boolean.TRUE.equals(throughPiece)) {
        // 贯穿件：检查逗号分隔的房间编码中是否包含目标房间
        return getAllRoomCodes().contains(targetRoomCode);
    } else {
        // 普通结构件：直接比较房间编码
        return targetRoomCode.equals(roomCode.trim());
    }
}
```

### 3. 添加验证逻辑

#### 验证方法
```java
/**
 * 验证房间编码的有效性
 * 只有贯穿件才允许有多个房间编码（逗号分隔）
 */
public boolean isRoomCodeValid() {
    if (roomCode == null || roomCode.trim().isEmpty()) {
        return true; // 允许为空
    }
    
    String[] roomCodes = roomCode.split(",");
    
    // 如果有多个房间编码，必须是贯穿件
    if (roomCodes.length > 1 && !Boolean.TRUE.equals(throughPiece)) {
        return false; // 普通结构件不允许多个房间编码
    }
    
    // 检查每个房间编码是否有效（非空且不全是空白字符）
    for (String code : roomCodes) {
        if (code.trim().isEmpty()) {
            return false;
        }
    }
    
    return true;
}
```

#### 验证器类
```java
@Component
public class RoomCodeValidator implements DataProxy<Structure> {
    
    @Override
    public void beforeAdd(Structure structure) {
        validateRoomCode(structure);
    }

    @Override
    public void beforeUpdate(Structure structure) {
        validateRoomCode(structure);
    }

    private void validateRoomCode(Structure structure) {
        if (!structure.isRoomCodeValid()) {
            String error = structure.getRoomCodeValidationError();
            throw new RuntimeException(error != null ? error : "房间编码格式无效");
        }
    }
}
```

### 4. 更新 Node 类

#### 简化的 Node 类
```java
@Data
public static class Node {
    private String roomCode; // 房间编码（普通结构件单个，贯穿件逗号分隔多个）
    private boolean throughPiece;

    // 相同的 getAllRoomCodes() 和 belongsToRoom() 方法
}
```

#### 更新转换方法
```java
public static Node convertStructToNode(Structure struct) {
    Node node = new Node();
    // ... 其他设置
    
    // 设置房间编码（直接使用 roomCode，支持逗号分隔的多房间）
    node.setRoomCode(struct.getRoomCode());
    
    return node;
}
```

## 📊 数据格式示例

### 普通结构件
```
roomCode: "ROOM001"
throughPiece: false
解析结果: ["ROOM001"]
```

### 贯穿件
```
roomCode: "ROOM001,ROOM002,ROOM003"
throughPiece: true
解析结果: ["ROOM001", "ROOM002", "ROOM003"]
```

### 包含空格的贯穿件
```
roomCode: "ROOM001, ROOM002 , ROOM003"
throughPiece: true
解析结果: ["ROOM001", "ROOM002", "ROOM003"] // 自动去除空格
```

## 🚫 验证规则

### 有效的配置
| 结构件类型 | throughPiece | roomCode | 验证结果 |
|------------|--------------|----------|----------|
| 普通结构件 | false | "ROOM001" | ✅ 有效 |
| 贯穿件 | true | "ROOM001" | ✅ 有效 |
| 贯穿件 | true | "ROOM001,ROOM002" | ✅ 有效 |
| 贯穿件 | true | "ROOM001, ROOM002, ROOM003" | ✅ 有效 |

### 无效的配置
| 结构件类型 | throughPiece | roomCode | 验证结果 | 错误信息 |
|------------|--------------|----------|----------|----------|
| 普通结构件 | false | "ROOM001,ROOM002" | ❌ 无效 | 只有贯穿件才允许配置多个房间编码 |
| 任意 | 任意 | "ROOM001,,ROOM002" | ❌ 无效 | 房间编码不能为空或只包含空白字符 |
| 任意 | 任意 | "ROOM001, , ROOM002" | ❌ 无效 | 房间编码不能为空或只包含空白字符 |

## 🧪 测试验证

### 测试用例
```java
@Test
void testRoomCodeValidation() {
    // 普通结构件的单房间编码
    Node normalNode = createNode("NORMAL001", "设备", "ROOM001", 0, 0, 0);
    assertEquals(1, normalNode.getAllRoomCodes().size());
    assertTrue(normalNode.belongsToRoom("ROOM001"));
    
    // 贯穿件的多房间编码
    Node throughPieceNode = createThroughPieceNode("THROUGH001", "贯穿件", 
        Arrays.asList("ROOM001", "ROOM002", "ROOM003"), 0, 0, 0);
    assertEquals(3, throughPieceNode.getAllRoomCodes().size());
    assertTrue(throughPieceNode.belongsToRoom("ROOM001"));
    assertTrue(throughPieceNode.belongsToRoom("ROOM002"));
    assertTrue(throughPieceNode.belongsToRoom("ROOM003"));
    
    // 包含空格的房间编码解析
    Node nodeWithSpaces = new Node();
    nodeWithSpaces.setRoomCode("ROOM001, ROOM002 , ROOM003");
    nodeWithSpaces.setThroughPiece(true);
    assertEquals(3, nodeWithSpaces.getAllRoomCodes().size());
}
```

## 🎯 重构优势

### 1. 简化数据模型
- ✅ 减少了一个字段 `throughPieceRoomCodes`
- ✅ 统一使用 `roomCode` 字段
- ✅ 减少了数据库表的复杂性

### 2. 提高数据一致性
- ✅ 避免了两个字段之间的数据不一致问题
- ✅ 单一数据源，减少维护成本
- ✅ 更直观的数据存储方式

### 3. 增强验证机制
- ✅ 严格的验证规则确保数据正确性
- ✅ 清晰的错误信息帮助用户理解问题
- ✅ 前端和后端双重验证

### 4. 保持功能完整性
- ✅ 所有原有功能保持不变
- ✅ 跨房间连接功能正常工作
- ✅ 算法逻辑无需大幅修改

## 📋 迁移指南

### 1. 数据迁移
如果已有数据使用了 `throughPieceRoomCodes` 字段，需要执行数据迁移：

```sql
-- 将 throughPieceRoomCodes 的数据合并到 roomCode 中
UPDATE structure 
SET room_code = (
    SELECT GROUP_CONCAT(room_code SEPARATOR ',')
    FROM structure_through_piece_rooms 
    WHERE structure_id = structure.id
)
WHERE through_piece = true 
AND EXISTS (
    SELECT 1 FROM structure_through_piece_rooms 
    WHERE structure_id = structure.id
);

-- 删除旧的关联表
DROP TABLE structure_through_piece_rooms;
```

### 2. 前端适配
- 贯穿件的房间编码输入框支持逗号分隔输入
- 添加实时验证提示
- 显示解析后的房间列表

### 3. API 兼容性
- 保持现有 API 接口不变
- 内部自动处理逗号分隔的解析
- 返回数据格式保持一致

## 🚀 总结

这次重构成功简化了贯穿件房间编码的存储和处理方式：

1. **✅ 数据模型简化**：移除了额外的字段，统一使用 `roomCode`
2. **✅ 验证机制完善**：确保只有贯穿件才能有多个房间编码
3. **✅ 功能保持完整**：所有跨房间连接功能正常工作
4. **✅ 代码更清晰**：逻辑更简单，维护成本更低

现在系统以更简洁的方式支持贯穿件的跨房间功能，同时保持了数据的完整性和一致性。
