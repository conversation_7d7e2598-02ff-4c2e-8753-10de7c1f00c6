package com.example.tribon.domain.enumeration;

import lombok.Getter;
import xyz.erupt.annotation.fun.ChoiceFetchHandler;
import xyz.erupt.annotation.fun.VLModel;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum StructTypeEnum {
    ROUTE_MAP("路线图","ROUTE_MAP");


    private final String name;

    private final String code;

    StructTypeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(StructTypeEnum.values())
                    .map(structTypeEnum -> new VLModel(structTypeEnum.getCode(), structTypeEnum.getName()))
                    .collect(Collectors.toList());

        }

    }

}
