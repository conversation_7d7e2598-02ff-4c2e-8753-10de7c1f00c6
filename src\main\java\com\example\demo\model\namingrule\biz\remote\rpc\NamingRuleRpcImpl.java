//package com.example.demo.model.namingrule.biz.remote.rpc;
//
//import com.example.demo.model.namingrule.api.rpc.NamingRuleRpc;
//import com.example.demo.model.namingrule.biz.service.NamingRuleService;
//import org.apache.dubbo.config.annotation.DubboService;
//import xyz.erupt.annotation.config.Comment;
//
//import javax.annotation.Resource;
//import javax.ws.rs.core.MultivaluedMap;
//import java.util.List;
//import java.util.Map;
//
//@DubboService
//public class NamingRuleRpcImpl implements NamingRuleRpc {
//    @Resource
//    NamingRuleService namingRuleService;
//
//    @Override
//    @Comment("根据命名规则生成编码，参数为：1、命名规则编码，2、需生成的个数，3、变量表")
//    public List<String> getNameCode(MultivaluedMap<String, String> formParams){
//        String ruleCode = formParams.getFirst("ruleCode").toString();
//        int num = Integer.parseInt(formParams.getFirst("num").toString()) ;
//        Map<String, String> variableMap = null;
//        return namingRuleService.getNameCode(ruleCode,num,variableMap);
//    }
//}
