package com.example.tribon.dto;


import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class StructureDataRetriveDto  {


    private String structureName;

    private String componentName;


    private String volumeName;


    /**
     * 三维空间坐标
     */
    double[] poi;

    /**
     * 旋转坐标
     */
    double[] rot;

    /**
     * 路由坐标
     */
    double[] rou;

    /**
     * 重心坐标
     */
    double[] cog;
}
