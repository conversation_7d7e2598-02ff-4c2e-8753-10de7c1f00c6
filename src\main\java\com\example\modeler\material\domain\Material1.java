package com.example.modeler.material.domain;

import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTableType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */
@Table(name = "ms_material")
@Entity
@Erupt(name = "物料")
public class Material1 extends BaseModel {

    @EruptField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", notNull = true, search = @Search)
    )
    private String materialCode;

    @EruptField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", notNull = true, search = @Search)
    )
    private String materialName;

    @OneToOne
    @EruptField(
            views = @View(title = "物料类型", column = "name"),
            edit = @Edit(title = "物料类型", notNull = true, search = @Search, type= EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(label = "name"))
    )
    private MaterialCategory1 materialCategory;


}
