package com.example.modeler.bom.domain;

import com.example.modeler.material.domain.Material1;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Tree;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ReferenceTreeType;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Table(name = "ms_bom_dtl")
@Entity
@Erupt(name="BOM List",
        tree = @Tree(pid = "parent.id"))
public class BomList extends BaseModel {

//    @ManyToOne
//    @EruptField(
//            views = @View(title = "名称"),
//            edit = @Edit(title = "名称", notNull = true)
//    )
//    private Bom bom;

    @ManyToOne
    @EruptField(
            views = @View(title = "消耗物料"),
            edit = @Edit(title = "消耗物料", notNull = true)
    )
    private Material1 material;

    @EruptField(
            views = @View(title = "用量"),
            edit = @Edit(title = "用量", notNull = true)
    )
    private Float usageAmount;

}
