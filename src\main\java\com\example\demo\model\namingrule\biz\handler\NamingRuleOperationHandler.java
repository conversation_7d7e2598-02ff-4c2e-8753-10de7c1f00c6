package com.example.demo.model.namingrule.biz.handler;


import com.example.demo.model.namingrule.biz.service.NamingRuleService;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRule;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.OperationHandler;

import javax.annotation.Resource;
import java.util.List;


@Component
public class NamingRuleOperationHandler implements OperationHandler<NamingRule, Void> {

    @Resource
    NamingRuleService namingRuleService;
    //返回值由前端浏览器执行
    @Override
    public String exec(List<NamingRule> data, Void vo, String[] param) {

        String namecode = namingRuleService.getNameCode(data.get(0).getNamingRuleCode(), 1, null).get(0);
        return "alert('"+ namecode +"')";

        // ✨ 通过顶部消息块展示消息
//         return "msg.info('提示信息')"
        // return "msg.error('错误信息')"
        // return "msg.success('成功信息')";

        // ✨ 自定义按钮返回结果用代码编辑器展示，参数 1 语言，参数 2 代码
        // return "codeModal('sql',`select * from xxx`)"


    }


}

