package com.example.wom.order.domain.handler;

import com.example.modeler.processroute.ProcessOperation;
import com.example.modeler.processroute.ProcessRoute;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.SliderType;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;
import java.util.Set;

@Erupt(name = "工单拆分")
public class OrderExpendOperator extends BaseModel {
    @EruptField(
            views = @View(title = "拆分数量"),
            edit = @Edit(title = "拆分数量"
                    )
    )

    private Integer count;

    @EruptField(
            views = @View(title = "拆分人"),
            edit = @Edit(title = "拆分人"
            )
    )
    private String person;

    @EruptField(
            views = @View(title = "工序", tpl = @Tpl(path = "/tpl/amis.html", width = "80%"))
    )
    private Set<ProcessOperation> processOperations;
}
