package com.example.demo.model.namingrule.biz.domain.namingRule.service;

import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRule;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameter;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.List;

@Service
public class NamingRuleParameterDomainService {
    @Resource
    private EruptDao eruptDao;
    @Comment("根据AK获取行")
    public NamingRuleParameter getParameterByAk(@Comment("命名规则") NamingRule namingRule , @Comment("参数名") String parameterName){
        CriteriaBuilder criteriaBuilder = eruptDao.getEntityManager().getCriteriaBuilder();
        CriteriaQuery<NamingRuleParameter> criteriaQuery = criteriaBuilder.createQuery(NamingRuleParameter.class);
        Root<NamingRuleParameter> namingRuleParameterRoot = criteriaQuery.from(NamingRuleParameter.class);
        criteriaQuery.where(
                criteriaBuilder.and(
                        criteriaBuilder.equal(namingRuleParameterRoot.get("namingRule"), namingRule),
                        criteriaBuilder.equal(namingRuleParameterRoot.get("parameterName"), parameterName)
                )
        );
        List<NamingRuleParameter> results = eruptDao.getEntityManager().createQuery(criteriaQuery).getResultList();
        return results.size() > 0? results.get(0) : null;
    }
}
