package com.example.tribon.domain.model;


import com.example.tribon.domain.model.subModel.StructureExtractForm;
import com.example.tribon.domain.model.subModel.StructureShowRouteForm;
import com.example.tribon.handler.ExistsRemoteModeOperationHandlerImpl;
import com.example.tribon.handler.ExtractStructureOperationHandlerImpl;
import com.example.tribon.handler.HideRouteMapOperationHandlerImpl;
import com.example.tribon.handler.ShowRouteMapOperationHandlerImpl;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.Table;
import java.util.List;

@Erupt(
        name = "安装件",
        rowOperation = {
        @RowOperation(
                title = "同步Structure数据",
                code = "ExtractStructure",
                mode = RowOperation.Mode.BUTTON,
                eruptClass = StructureExtractForm.class,
                operationHandler = ExtractStructureOperationHandlerImpl.class)
        , @RowOperation(
                title = "显示路线图",
                code = "ShowRouteMap",
                mode = RowOperation.Mode.BUTTON,
                eruptClass = StructureShowRouteForm.class,
                operationHandler = ShowRouteMapOperationHandlerImpl.class)
        , @RowOperation(
                title = "隐藏路线图",
                code = "HideRouteMap",
                mode = RowOperation.Mode.BUTTON,
                eruptClass = StructureShowRouteForm.class,
                operationHandler = HideRouteMapOperationHandlerImpl.class)
        , @RowOperation(
        title = "退出远程模式",
        code = "ExistRemoteMode",
        mode = RowOperation.Mode.BUTTON,
        operationHandler = ExistsRemoteModeOperationHandlerImpl.class)
        }
)
@Table(name = "tribon_structure"
)
@Entity
@Getter
@Setter
public class Structure extends BaseModel {

    @EruptField(
            views = @View(title = "唯一编号"),
            edit = @Edit(title = "唯一编号", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private String code;

    @EruptField(
            views = @View(title = "安装件名称"),
            edit = @Edit(title = "安装件名称", readonly = @Readonly(add = false, edit = true),notNull = true, search = @Search(vague = true))
    )
    private String name;

    @EruptField(
            views = @View(title = "Component名称"),
            edit = @Edit(title = "Component名称", readonly = @Readonly(add = false, edit = true),notNull = true, search = @Search(vague = true))
    )
    private String componentName;

    /**
     * 所属房间编号
     */
    private String roomCode;

    /**
     * Volume Name
     */
    private String volumeName;


    /**
     * 安装件类型
     * 设备、扁条、托架、支架、贯穿件
     */
    @EruptField(
        views = @View(title = "类型"),
        edit = @Edit(title = "类型", notNull = false, search = @Search(vague = true))
        )
    private String structType;

    /**
     * 三维空间坐标
     */
    @EruptField(
            views = @View(title = "POI"),
            edit = @Edit(title = "POI", notNull = false, search = @Search(vague = true))
    )
    private String poi;

    /**
     * 旋转坐标
     */
    @EruptField(
            views = @View(title = "ROT"),
            edit = @Edit(title = "ROT", notNull = false, search = @Search(vague = true))
    )
    private String rot;

    /**
     * 路由坐标
     */
    @EruptField(
            views = @View(title = "ROU"),
            edit = @Edit(title = "ROU", notNull = false, search = @Search(vague = true))
    )
    private String rou;

    /**
     * 重心坐标
     */
    @EruptField(
            views = @View(title = "COG"),
            edit = @Edit(title = "COG", notNull = false, search = @Search(vague = true))
    )
    private String cog;

    /**
     * 重心X标记
     * 使用两个struct来绘制重心X标记
     */
//    Long cogXMarkHandle1;
//    Long cogXMarkHandle2;
    private String cogXMark1StructCode;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_flagX1HandlesA", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX1HandlesA;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_flagX2HandlesA", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX2HandlesA;
    private String cogXMark2StructCode;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_flagX1HandlesB", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX1HandlesB;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_flagX2HandlesB", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX2HandlesB;
}
