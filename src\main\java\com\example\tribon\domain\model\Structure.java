package com.example.tribon.domain.model;


import com.example.tribon.domain.enumeration.StructTypeEnum;
import com.example.tribon.domain.model.subModel.StructureExtractForm;
import com.example.tribon.domain.model.subModel.StructureShowRouteForm;
import com.example.tribon.handler.ExistsRemoteModeOperationHandlerImpl;
import com.example.tribon.handler.ExtractStructureOperationHandlerImpl;
import com.example.tribon.handler.HideRouteMapOperationHandlerImpl;
import com.example.tribon.handler.ShowRouteMapOperationHandlerImpl;
import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.Table;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Erupt(
        name = "安装件",
        dataProxy = com.example.tribon.validator.RoomCodeValidator.class,
        rowOperation = {
        @RowOperation(
                title = "同步Structure数据",
                code = "ExtractStructure",
                mode = RowOperation.Mode.BUTTON,
                eruptClass = StructureExtractForm.class,
                operationHandler = ExtractStructureOperationHandlerImpl.class)
        , @RowOperation(
                title = "显示路线图",
                code = "ShowRouteMap",
                mode = RowOperation.Mode.BUTTON,
                eruptClass = StructureShowRouteForm.class,
                operationHandler = ShowRouteMapOperationHandlerImpl.class)
        , @RowOperation(
                title = "隐藏路线图",
                code = "HideRouteMap",
                mode = RowOperation.Mode.BUTTON,
                eruptClass = StructureShowRouteForm.class,
                operationHandler = HideRouteMapOperationHandlerImpl.class)
        , @RowOperation(
        title = "退出远程模式",
        code = "ExistRemoteMode",
        mode = RowOperation.Mode.BUTTON,
        operationHandler = ExistsRemoteModeOperationHandlerImpl.class)
        }
)
@Table(name = "tribon_structure"
)
@Entity
@Getter
@Setter
public class Structure extends BaseModel {

    @EruptField(
            views = @View(title = "唯一编号"),
            edit = @Edit(title = "唯一编号", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private String code;

    @EruptField(
            views = @View(title = "安装件名称"),
            edit = @Edit(title = "安装件名称", notNull = false, search = @Search(vague = true))
    )
    private String name;

    @EruptField(
            views = @View(title = "Component名称"),
            edit = @Edit(title = "Component名称", readonly = @Readonly(add = false, edit = true),notNull = true, search = @Search(vague = true))
    )
    private String componentName;

    /**
     * 房间编号
     * 普通结构件：单个房间编号
     * 贯穿件：多个房间编号，使用逗号分隔（如：ROOM001,ROOM002,ROOM003）
     */
    @EruptField(
            views = @View(title = "房间编号"),
            edit = @Edit(title = "房间编号", notNull = false, search = @Search(vague = true))
    )
    private String roomCode;

    /**
     * 是否是贯穿件
     */
    @EruptField(
            views = @View(title = "是否贯穿件"),
            edit = @Edit(title = "是否贯穿件", type = EditType.BOOLEAN,
                    boolType = @xyz.erupt.annotation.sub_field.sub_edit.BoolType(
                            trueText = "是", falseText = "否"
                    ), notNull = false, search = @Search(vague = true)))
    private Boolean throughPiece;

    /**
     * Volume Name
     */
    @EruptField(
            views = @View(title = "Volume名称"),
            edit = @Edit(title = "Volume名称", notNull = false, search = @Search(vague = true))
    )
    private String volumeName;


    /**
     * 安装件类型
     * 设备、扁条、托架、支架、贯穿件
     */
    @EruptField(
        views = @View(title = "类型"),
        edit = @Edit(title = "类型", notNull = false, search = @Search(vague = true),
        // 枚举
        type = EditType.CHOICE,
        choiceType = @ChoiceType(
                fetchHandler = StructTypeEnum.ChoiceFetch.class
        )
        )
        )
    private String structType;

    /**
     * 三维空间坐标
     */
    @EruptField(
            views = @View(title = "POI"),
            edit = @Edit(title = "POI", notNull = false, search = @Search(vague = true))
    )
    private String poi;

    /**
     * 旋转坐标
     */
    @EruptField(
            views = @View(title = "ROT"),
            edit = @Edit(title = "ROT", notNull = false, search = @Search(vague = true))
    )
    private String rot;

    /**
     * 路由坐标
     */
    @EruptField(
            views = @View(title = "ROU"),
            edit = @Edit(title = "ROU", notNull = false, search = @Search(vague = true))
    )
    private String rou;

    /**
     * 重心坐标
     */
    @EruptField(
            views = @View(title = "COG"),
            edit = @Edit(title = "COG", notNull = false, search = @Search(vague = true))
    )
    private String cog;



    /**
     * 重心X标记
     * 使用两个struct来绘制重心X标记
     */
//    Long cogXMarkHandle1;
//    Long cogXMarkHandle2;
    private String cogXMark1StructCode;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_flagX1HandlesA", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX1HandlesA;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_flagX2HandlesA", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX2HandlesA;
    private String cogXMark2StructCode;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_flagX1HandlesB", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX1HandlesB;
    @ElementCollection
    @CollectionTable(name = "tribon_structure_flagX2HandlesB", joinColumns = @JoinColumn(name = "pid"))
    private List<Integer> flagX2HandlesB;

    /**
     * 获取结构件的所有房间编码
     * 对于普通结构件，返回单个房间编码
     * 对于贯穿件，返回逗号分隔的多个房间编码
     */
    public List<String> getAllRoomCodes() {
        if (roomCode == null || roomCode.trim().isEmpty()) {
            return Collections.emptyList();
        }

        if (Boolean.TRUE.equals(throughPiece)) {
            // 贯穿件：解析逗号分隔的房间编码
            return Arrays.asList(roomCode.split(","))
                    .stream()
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(java.util.stream.Collectors.toList());
        } else {
            // 普通结构件：单个房间编码
            return Arrays.asList(roomCode.trim());
        }
    }

    /**
     * 检查结构件是否属于指定房间
     * 对于普通结构件，检查房间编码是否匹配
     * 对于贯穿件，检查是否包含在逗号分隔的房间列表中
     */
    public boolean belongsToRoom(String targetRoomCode) {
        if (targetRoomCode == null || roomCode == null) {
            return false;
        }

        if (Boolean.TRUE.equals(throughPiece)) {
            // 贯穿件：检查逗号分隔的房间编码中是否包含目标房间
            return getAllRoomCodes().contains(targetRoomCode);
        } else {
            // 普通结构件：直接比较房间编码
            return targetRoomCode.equals(roomCode.trim());
        }
    }

    /**
     * 检查是否为贯穿件
     */
    public boolean isThroughPiece() {
        return Boolean.TRUE.equals(throughPiece);
    }

    /**
     * 验证房间编码的有效性
     * 只有贯穿件才允许有多个房间编码（逗号分隔）
     */
    public boolean isRoomCodeValid() {
        if (roomCode == null || roomCode.trim().isEmpty()) {
            return true; // 允许为空
        }

        String[] roomCodes = roomCode.split(",");

        // 如果有多个房间编码，必须是贯穿件
        if (roomCodes.length > 1 && !Boolean.TRUE.equals(throughPiece)) {
            return false; // 普通结构件不允许多个房间编码
        }

        // 检查每个房间编码是否有效（非空且不全是空白字符）
        for (String code : roomCodes) {
            if (code.trim().isEmpty()) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取房间编码验证错误信息
     */
    public String getRoomCodeValidationError() {
        if (isRoomCodeValid()) {
            return null;
        }

        if (roomCode == null || roomCode.trim().isEmpty()) {
            return null;
        }

        String[] roomCodes = roomCode.split(",");

        if (roomCodes.length > 1 && !Boolean.TRUE.equals(throughPiece)) {
            return "只有贯穿件才允许配置多个房间编码（使用逗号分隔）";
        }

        for (String code : roomCodes) {
            if (code.trim().isEmpty()) {
                return "房间编码不能为空或只包含空白字符";
            }
        }

        return "房间编码格式无效";
    }
}
