package com.example.demo.model.namingrule.biz.remote.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.util.Map;

import com.example.demo.model.namingrule.biz.service.NamingRuleService;
@RestController
@RequiredArgsConstructor
public class NamingRuleController {

    @Resource
    private EruptDao eruptDao;
    private final NamingRuleService namingRuleService;

    //
    @PostMapping({"/namingRule/generateCode"})
//    @EruptRouter(authIndex = 2, verifyType = EruptRouter.VerifyType.ERUPT)
    public String generateCode(@RequestBody Map<String, Object> map) {
        Map<String, String> variablMap = convertJsonToMap(map.get("variables").toString());
        return namingRuleService.getNameCode(map.get("ruleCode").toString(), Integer.parseInt(map.get("num").toString()), variablMap).toString();
    }

    public static Map<String, String> convertJsonToMap(String json) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(json, Map.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}