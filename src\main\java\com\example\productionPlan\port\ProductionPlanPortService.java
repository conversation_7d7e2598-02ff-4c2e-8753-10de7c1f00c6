//package com.example.productionPlan.port;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.ReferenceConfig;
//import org.apache.dubbo.rpc.service.GenericService;
//import org.springframework.stereotype.Service;
//
//@Service
//@Slf4j
//public class ProductionPlanPortService {
//    public Object getOrderNumberByIdFromProductionOrder(String OrderNumber) {
//        ReferenceConfig<GenericService> reference = new ReferenceConfig<GenericService>();
//        reference.setInterface("com.example.wom.order_api.OrderService");
//        reference.setTimeout(30000);
//        reference.setGeneric(true);
//        GenericService genericService = reference.get();
//        Object results = genericService.$invoke("getOrderNumberById", new String[]{"java.lang.String"}, new Object[]{"0001"});
//        return results;
//    }
//}
