package com.example.tribon.domain.model;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(
        name = "安装件关系"
)
@Table(name = "tribon_structure_line"
)
@Entity
@Getter
@Setter
public class Line extends BaseModel {

    @EruptField(
            views = @View(title = "handle"),
            edit = @Edit(title = "handle",
                    notNull = true, search = @Search(vague = true))
    )
    private Integer handle;

}
