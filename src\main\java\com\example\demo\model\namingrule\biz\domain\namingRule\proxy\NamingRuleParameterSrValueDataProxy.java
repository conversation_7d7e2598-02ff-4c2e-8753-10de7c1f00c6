package com.example.demo.model.namingrule.biz.domain.namingRule.proxy;


import com.example.demo.model.namingrule.api.enumeration.NamingRuleEnum;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRule;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameter;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameterSrValue;
import com.example.demo.model.namingrule.biz.domain.namingRule.service.NamingRuleDomainService;
import com.example.demo.model.namingrule.biz.domain.namingRule.service.NamingRuleParameterDomainService;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.util.Calendar;

@Component
public class NamingRuleParameterSrValueDataProxy implements DataProxy<NamingRuleParameterSrValue> {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private NamingRuleDomainService namingRuleDomainService;

    @Resource
    private NamingRuleParameterDomainService namingRuleParameterDomainService;

    @Override
    public void afterAdd(NamingRuleParameterSrValue namingRuleParameterSrValue){
        NamingRule namingRule = namingRuleDomainService.getParametersByCode(namingRuleParameterSrValue.getSrParameter().getNamingRule().getNamingRuleCode());
        String[] dependParameters = namingRuleParameterSrValue.getSrParameter().getDependParameters().split(",");
        // 设置 yearText,monthText,dayText的值
        for (String dependParameter : dependParameters){
            NamingRuleParameter namingRuleParameter = namingRuleParameterDomainService.getParameterByAk(namingRule, dependParameter);
            NamingRuleEnum.Enum type = NamingRuleEnum.Enum.valueOf(namingRuleParameter.getParameterType());
            switch (type) {
                case D2:
                    int day = Calendar.getInstance().get(Calendar.DATE);
                    String formatDay = String.format("%02d", day);
                    namingRuleParameterSrValue.setDayText(formatDay);
                    break;
                case M2:
                    int month = Calendar.getInstance().get(Calendar.MONTH) + 1;
                    String formatMonth = String.format("%02d", month);
                    namingRuleParameterSrValue.setMonthText(formatMonth);
                    break;
                case Y2:
                    int year = Calendar.getInstance().get(Calendar.YEAR);
                    String formatYear = String.format("%02d", year % 100);
                    namingRuleParameterSrValue.setYearText(formatYear);
                    break;
                case Y4:
                    int year4 = Calendar.getInstance().get(Calendar.YEAR);
                    String formatYear4 = String.format("%04d", year4);
                    namingRuleParameterSrValue.setYearText(formatYear4);
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public void afterUpdate(NamingRuleParameterSrValue namingRuleParameterSrValue){
        // 设置 yearText,monthText,dayText的值 （当提交的数据为空时）
        NamingRuleParameterSrValue namingRuleParameterSrValueDb = eruptDao.getEntityManager().find(NamingRuleParameterSrValue.class, namingRuleParameterSrValue.getId());
        if (namingRuleParameterSrValue.getYearText() == null){
            namingRuleParameterSrValue.setYearText(namingRuleParameterSrValueDb.getYearText());
        }
        if (namingRuleParameterSrValue.getMonthText() == null){
            namingRuleParameterSrValue.setMonthText(namingRuleParameterSrValueDb.getMonthText());
        }
        if (namingRuleParameterSrValue.getDayText() == null){
            namingRuleParameterSrValue.setDayText(namingRuleParameterSrValueDb.getDayText());
        }

    }


}
