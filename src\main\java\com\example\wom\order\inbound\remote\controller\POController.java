package com.example.wom.order.inbound.remote.controller;

import com.example.modeler.processroute.ProcessOperation;
import com.example.modeler.processroute.ProcessRoute;
import com.example.wom.order.domain.ProductionOrder;
import com.example.wom.order.inbound.local.ProductionOrderCmdService;
//import com.example.wom.order_api.OrderService;
import com.example.wom.order_api.dto.ConfirmOrderCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
@RestController
@RequiredArgsConstructor
public class POController {

    @Resource
    private EruptDao eruptDao;
    private final ProductionOrderCmdService productionOrderCmdService;
//    private final OrderService orderService;



    //
    @RequestMapping("/poinfo/oplist/{orderId}")
    public List<ProcessOperation> getPoOperation(@PathVariable Long orderId) {
        // Erupt jdbc方式查询
//        return eruptDao.lambdaQuery(ProcessOperation.class).list();
        ProductionOrder po = eruptDao.getEntityManager().find(ProductionOrder.class, orderId);
        List poList = new ArrayList<>();
        poList.addAll(po.getProcessRoute().getProcessOperations());
        return poList;
    }


//    @RequestMapping("/poinfo/oplist/{orderId}")
//    public List<ProcessOperation> getPoMaterial(@PathVariable Long orderId) {
//        // Erupt jdbc方式查询
////        return eruptDao.lambdaQuery(ProcessOperation.class).list();
//        ProductionOrder po = eruptDao.getEntityManager().find(ProductionOrder.class, orderId);
//        List poList = new ArrayList<>();
//        poList.addAll(po.getProcessRoute().getProcessOperations());
//        return poList;
//    }

//    @RequestMapping("/po/confirm")
//    public boolean confirm(@Valid @RequestBody ConfirmOrderCmd cmd) {
//
//        // Erupt jdbc方式查询
//        return productionOrderCmdService.confirmOrder(cmd);
//    }

}