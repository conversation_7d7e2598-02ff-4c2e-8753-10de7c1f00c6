package com.example.tribon.service;

import com.example.tribon.dto.GenerateRouteMapResponseDto;
import com.example.tribon.service.RouteMapService.*;
import com.example.tribon.service.RouteMapRuleService.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.*;

/**
 * 路线图服务简单测试类
 * 验证修复后的算法效果
 */
public class RouteMapServiceSimpleTest {

    private List<Node> testNodes;
    private AdvancedRuleLibrary ruleLibrary;

    @BeforeEach
    void setUp() {
        // 创建测试节点
        testNodes = createTestNodes();
        
        // 创建测试规则库
        ruleLibrary = RuleConfigManager.createDefaultRuleLibrary();
    }

    @Test
    void testNoDuplicateConnections() {
        // 测试无重复连接
        GenerateRouteMapResponseDto result = RouteMapService.findConnections(
            testNodes, ruleLibrary, 3);
        
        Set<String> connectionSet = new HashSet<>();
        int duplicateCount = 0;
        
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            String connection1 = path.getStartStructCode() + "->" + path.getEndStructCode();
            String connection2 = path.getEndStructCode() + "->" + path.getStartStructCode();
            
            if (connectionSet.contains(connection1) || connectionSet.contains(connection2)) {
                duplicateCount++;
            }
            connectionSet.add(connection1);
        }
        
        assertEquals(0, duplicateCount, "不应该存在重复连接");
    }

    @Test
    void testRuleLibraryValidation() {
        // 测试规则库验证
        ValidationResult validation = RuleConfigManager.validateRuleLibrary(ruleLibrary);
        
        assertTrue(validation.isValid(), "默认规则库应该是有效的");
        assertTrue(validation.getErrors().isEmpty(), "不应该有错误");
    }

    @Test
    void testDistanceCalculation() {
        // 测试距离计算
        Node node1 = createNode("N1", "设备", "ROOM001", 0, 0, 0);
        Node node2 = createNode("N2", "扁条", "ROOM001", 3, 4, 0);
        
        // 应该计算出距离为5（3-4-5直角三角形）
        double expectedDistance = 5.0;
        double actualDistance = calculateDistance(node1, node2);
        
        assertEquals(expectedDistance, actualDistance, 0.001, "距离计算应该正确");
    }

    @Test
    void testMaxDistanceConstraint() {
        // 测试每个连接类型的最大距离约束
        AdvancedRuleLibrary shortDistanceRules = RuleLibraryBuilder.create()
            .addConnectionRule("设备", "扁条", 1)
            .setMaxDistanceForConnection("设备", "扁条", 50.0) // 设置很小的最大距离
            .build();

        GenerateRouteMapResponseDto result = RouteMapService.findConnections(
            testNodes, shortDistanceRules, 3);

        // 验证所有连接都在最大距离内
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            Node startNode = findNodeById(testNodes, path.getStartStructCode());
            Node endNode = findNodeById(testNodes, path.getEndStructCode());

            if (startNode != null && endNode != null) {
                double distance = calculateDistance(startNode, endNode);
                Double maxDistanceForConnection = shortDistanceRules.getMaxDistanceForConnection(startNode.getType(), endNode.getType());
                assertTrue(distance <= maxDistanceForConnection,
                    "连接距离应该在最大距离限制内");
            }
        }
    }

    @Test
    void testConnectionLimitsByPriority() {
        // 测试按优先级的连接数量限制
        AdvancedRuleLibrary priorityRules = RuleLibraryBuilder.create()
            .addConnectionRule("设备", "扁条", 1)    // 优先级1
            .addConnectionRule("设备", "托架", 2)    // 优先级2
            .addConnectionRule("设备", "支架", 3)    // 优先级3
            .setMaxDistanceForConnection("设备", "扁条", 1000.0)
            .setMaxDistanceForConnection("设备", "托架", 1000.0)
            .setMaxDistanceForConnection("设备", "支架", 1000.0)
            .setConnectionLimitForConnection("设备", "扁条", 1)  // 扁条只能连1个
            .setConnectionLimitForConnection("设备", "托架", 1)  // 托架只能连1个
            .setConnectionLimitForConnection("设备", "支架", 1)  // 支架只能连1个
            .build();

        // 创建更多测试节点
        List<Node> moreNodes = new ArrayList<>();
        moreNodes.add(createNode("DEV001", "设备", "ROOM001", 0, 0, 0));
        moreNodes.add(createNode("FLAT001", "扁条", "ROOM001", 50, 0, 0));
        moreNodes.add(createNode("FLAT002", "扁条", "ROOM001", 60, 0, 0));
        moreNodes.add(createNode("BRACKET001", "托架", "ROOM001", 100, 0, 0));
        moreNodes.add(createNode("BRACKET002", "托架", "ROOM001", 110, 0, 0));
        moreNodes.add(createNode("SUPPORT001", "支架", "ROOM001", 150, 0, 0));

        GenerateRouteMapResponseDto result = RouteMapService.findConnections(
            moreNodes, priorityRules, 5);

        // 验证连接按优先级和数量限制正确分配
        Map<String, Integer> connectionTypeCount = new HashMap<>();
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            Node startNode = findNodeById(moreNodes, path.getStartStructCode());
            Node endNode = findNodeById(moreNodes, path.getEndStructCode());

            if (startNode != null && endNode != null && startNode.getType().equals("设备")) {
                String targetType = endNode.getType();
                connectionTypeCount.put(targetType, connectionTypeCount.getOrDefault(targetType, 0) + 1);
            }
        }

        // 验证每种类型的连接数不超过限制
        assertTrue(connectionTypeCount.getOrDefault("扁条", 0) <= 1, "扁条连接数应该不超过1");
        assertTrue(connectionTypeCount.getOrDefault("托架", 0) <= 1, "托架连接数应该不超过1");
        assertTrue(connectionTypeCount.getOrDefault("支架", 0) <= 1, "支架连接数应该不超过1");

        // 验证优先级：应该优先连接扁条（优先级1）
        if (connectionTypeCount.containsKey("扁条")) {
            assertEquals(1, connectionTypeCount.get("扁条").intValue(), "应该连接1个扁条（优先级最高）");
        }
    }

    @Test
    void testConnectionTypeLimits() {
        // 测试连接类型限制，防止双向连接导致超出该类型的连接限制
        AdvancedRuleLibrary bidirectionalRules = RuleLibraryBuilder.create()
            // 设备 → 托架：优先级1，限制1个
            .addConnectionRule("设备", "托架", 1)
            .setConnectionLimitForConnection("设备", "托架", 1)
            .setMaxDistanceForConnection("设备", "托架", 1000.0)
            // 托架 → 设备：优先级1，限制1个
            .addConnectionRule("托架", "设备", 1)
            .setConnectionLimitForConnection("托架", "设备", 1)
            .setMaxDistanceForConnection("托架", "设备", 1000.0)
            .setMaxConnections(5) // 每个节点最多5个连接
            .build();

        // 创建测试节点：1个设备，2个托架
        List<Node> bidirectionalNodes = new ArrayList<>();
        bidirectionalNodes.add(createNode("DEV001", "设备", "ROOM001", 0, 0, 0));
        bidirectionalNodes.add(createNode("BRACKET001", "托架", "ROOM001", 100, 0, 0));
        bidirectionalNodes.add(createNode("BRACKET002", "托架", "ROOM001", 200, 0, 0));

        GenerateRouteMapResponseDto result = RouteMapService.findConnections(
            bidirectionalNodes, bidirectionalRules, 5);

        // 统计设备DEV001与托架类型的连接数
        int deviceToBracketCount = 0;
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            String startId = path.getStartStructCode();
            String endId = path.getEndStructCode();

            // 查找设备DEV001与托架的连接（双向）
            if ((startId.equals("DEV001") && endId.startsWith("BRACKET")) ||
                (endId.equals("DEV001") && startId.startsWith("BRACKET"))) {
                deviceToBracketCount++;
            }
        }

        // 验证设备与托架的连接数不超过配置的限制（1个）
        assertTrue(deviceToBracketCount <= 1,
            String.format("设备DEV001与托架的连接数 %d 不应超过限制 1", deviceToBracketCount));

        // 验证不会出现重复连接（A→B 和 B→A 同时存在）
        Set<String> connectionPairs = new HashSet<>();
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            String pair1 = path.getStartStructCode() + "-" + path.getEndStructCode();
            String pair2 = path.getEndStructCode() + "-" + path.getStartStructCode();

            assertFalse(connectionPairs.contains(pair2),
                String.format("不应该存在重复连接: %s 和 %s", pair1, pair2));
            connectionPairs.add(pair1);
        }

        // 输出结果用于调试
        System.out.println("连接结果:");
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            System.out.println(path.getStartStructCode() + " → " + path.getEndStructCode());
        }
        System.out.println("设备DEV001与托架的连接数: " + deviceToBracketCount);
    }

    @Test
    void testThroughPieceCrossRoomConnection() {
        // 测试贯穿件的跨房间连接功能
        AdvancedRuleLibrary throughPieceRules = RuleLibraryBuilder.create()
            .addConnectionRule("设备", "贯穿件", 1)
            .setConnectionLimitForConnection("设备", "贯穿件", 2)
            .setMaxDistanceForConnection("设备", "贯穿件", 1000.0)
            .addConnectionRule("贯穿件", "设备", 2)
            .setConnectionLimitForConnection("贯穿件", "设备", 1)
            .setMaxDistanceForConnection("贯穿件", "设备", 1000.0)
            .setMaxConnections(5)
            .build();

        // 创建测试节点：不同房间的设备和跨房间的贯穿件
        List<Node> crossRoomNodes = new ArrayList<>();

        // 房间1的设备
        Node dev1 = createNode("DEV001", "设备", "ROOM001", 0, 0, 0);
        crossRoomNodes.add(dev1);

        // 房间2的设备
        Node dev2 = createNode("DEV002", "设备", "ROOM002", 500, 0, 0);
        crossRoomNodes.add(dev2);

        // 贯穿件：跨越房间1和房间2
        Node throughPiece = createThroughPieceNode("THROUGH001", "贯穿件",
            Arrays.asList("ROOM001", "ROOM002"), 250, 0, 0);
        crossRoomNodes.add(throughPiece);

        GenerateRouteMapResponseDto result = RouteMapService.findConnections(
            crossRoomNodes, throughPieceRules, 5);

        // 验证连接结果
        boolean dev1ToThroughPiece = false;
        boolean dev2ToThroughPiece = false;

        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            String start = path.getStartStructCode();
            String end = path.getEndStructCode();

            if ((start.equals("DEV001") && end.equals("THROUGH001")) ||
                (start.equals("THROUGH001") && end.equals("DEV001"))) {
                dev1ToThroughPiece = true;
            }

            if ((start.equals("DEV002") && end.equals("THROUGH001")) ||
                (start.equals("THROUGH001") && end.equals("DEV002"))) {
                dev2ToThroughPiece = true;
            }
        }

        // 验证贯穿件能够连接不同房间的设备
        assertTrue(dev1ToThroughPiece || dev2ToThroughPiece,
            "贯穿件应该能够连接到不同房间的设备");

        // 输出结果用于调试
        System.out.println("跨房间连接结果:");
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            System.out.println(path.getStartStructCode() + " → " + path.getEndStructCode());
        }
    }

    // 辅助方法
    private List<Node> createTestNodes() {
        List<Node> nodes = new ArrayList<>();
        
        // 房间1的节点
        nodes.add(createNode("DEV001", "设备", "ROOM001", 0, 0, 0));
        nodes.add(createNode("FLAT001", "扁条", "ROOM001", 100, 0, 0));
        nodes.add(createNode("BRACKET001", "托架", "ROOM001", 200, 0, 0));
        nodes.add(createNode("SUPPORT001", "支架", "ROOM001", 300, 0, 0));
        
        return nodes;
    }

    private Node createNode(String id, String type, String roomCode, double x, double y, double z) {
        Node node = new Node();
        node.setId(id);
        node.setType(type);
        node.setRoomCode(roomCode);
        node.setX(x);
        node.setY(y);
        node.setZ(z);
        node.setThroughPiece(false);
        return node;
    }

    private Node createThroughPieceNode(String id, String type, List<String> roomCodes, double x, double y, double z) {
        Node node = new Node();
        node.setId(id);
        node.setType(type);
        node.setThroughPieceRoomCodes(roomCodes);
        node.setX(x);
        node.setY(y);
        node.setZ(z);
        node.setThroughPiece(true);
        return node;
    }

    private Node findNodeById(List<Node> nodes, String id) {
        return nodes.stream()
            .filter(node -> node.getId().equals(id))
            .findFirst()
            .orElse(null);
    }

    private double calculateDistance(Node a, Node b) {
        return Math.sqrt(
            Math.pow(a.getX() - b.getX(), 2) +
            Math.pow(a.getY() - b.getY(), 2) +
            Math.pow(a.getZ() - b.getZ(), 2));
    }
}
