package com.example.tribon.service;

import com.example.tribon.dto.GenerateRouteMapResponseDto;
import com.example.tribon.service.RouteMapService.*;
import com.example.tribon.service.RouteMapRuleService.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.*;

/**
 * 路线图服务简单测试类
 * 验证修复后的算法效果
 */
public class RouteMapServiceSimpleTest {

    private List<Node> testNodes;
    private AdvancedRuleLibrary ruleLibrary;

    @BeforeEach
    void setUp() {
        // 创建测试节点
        testNodes = createTestNodes();
        
        // 创建测试规则库
        ruleLibrary = RuleConfigManager.createDefaultRuleLibrary();
    }

    @Test
    void testNoDuplicateConnections() {
        // 测试无重复连接
        GenerateRouteMapResponseDto result = RouteMapService.findConnections(
            testNodes, ruleLibrary, 3);
        
        Set<String> connectionSet = new HashSet<>();
        int duplicateCount = 0;
        
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            String connection1 = path.getStartStructCode() + "->" + path.getEndStructCode();
            String connection2 = path.getEndStructCode() + "->" + path.getStartStructCode();
            
            if (connectionSet.contains(connection1) || connectionSet.contains(connection2)) {
                duplicateCount++;
            }
            connectionSet.add(connection1);
        }
        
        assertEquals(0, duplicateCount, "不应该存在重复连接");
    }

    @Test
    void testRuleLibraryValidation() {
        // 测试规则库验证
        ValidationResult validation = RuleConfigManager.validateRuleLibrary(ruleLibrary);
        
        assertTrue(validation.isValid(), "默认规则库应该是有效的");
        assertTrue(validation.getErrors().isEmpty(), "不应该有错误");
    }

    @Test
    void testDistanceCalculation() {
        // 测试距离计算
        Node node1 = createNode("N1", "设备", "ROOM001", 0, 0, 0);
        Node node2 = createNode("N2", "扁条", "ROOM001", 3, 4, 0);
        
        // 应该计算出距离为5（3-4-5直角三角形）
        double expectedDistance = 5.0;
        double actualDistance = calculateDistance(node1, node2);
        
        assertEquals(expectedDistance, actualDistance, 0.001, "距离计算应该正确");
    }

    @Test
    void testMaxDistanceConstraint() {
        // 测试每个连接类型的最大距离约束
        AdvancedRuleLibrary shortDistanceRules = RuleLibraryBuilder.create()
            .addConnectionRule("设备", "扁条", 1)
            .setMaxDistanceForConnection("设备", "扁条", 50.0) // 设置很小的最大距离
            .build();
        
        GenerateRouteMapResponseDto result = RouteMapService.findConnections(
            testNodes, shortDistanceRules, 3);
        
        // 验证所有连接都在最大距离内
        for (GenerateRouteMapResponseDto.RoutePathDto path : result.getRoutePathList()) {
            Node startNode = findNodeById(testNodes, path.getStartStructCode());
            Node endNode = findNodeById(testNodes, path.getEndStructCode());
            
            if (startNode != null && endNode != null) {
                double distance = calculateDistance(startNode, endNode);
                Double maxDistanceForConnection = shortDistanceRules.getMaxDistanceForConnection(startNode.getType(), endNode.getType());
                assertTrue(distance <= maxDistanceForConnection, 
                    "连接距离应该在最大距离限制内");
            }
        }
    }

    // 辅助方法
    private List<Node> createTestNodes() {
        List<Node> nodes = new ArrayList<>();
        
        // 房间1的节点
        nodes.add(createNode("DEV001", "设备", "ROOM001", 0, 0, 0));
        nodes.add(createNode("FLAT001", "扁条", "ROOM001", 100, 0, 0));
        nodes.add(createNode("BRACKET001", "托架", "ROOM001", 200, 0, 0));
        nodes.add(createNode("SUPPORT001", "支架", "ROOM001", 300, 0, 0));
        
        return nodes;
    }

    private Node createNode(String id, String type, String roomCode, double x, double y, double z) {
        Node node = new Node();
        node.setId(id);
        node.setType(type);
        node.setRoomCode(roomCode);
        node.setX(x);
        node.setY(y);
        node.setZ(z);
        node.setThroughPiece(false);
        return node;
    }

    private Node findNodeById(List<Node> nodes, String id) {
        return nodes.stream()
            .filter(node -> node.getId().equals(id))
            .findFirst()
            .orElse(null);
    }

    private double calculateDistance(Node a, Node b) {
        return Math.sqrt(
            Math.pow(a.getX() - b.getX(), 2) +
            Math.pow(a.getY() - b.getY(), 2) +
            Math.pow(a.getZ() - b.getZ(), 2));
    }
}
