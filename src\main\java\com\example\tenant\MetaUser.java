package com.example.tenant;

import com.example.demo.handler.OperationHandlerImpl;
import com.example.modeler.processroute.ProcessRoute;
import com.example.wom.order.domain.handler.OrderExpendOperationHandlerImpl;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.upms.handler.ViaMenuValueCtrl;
import xyz.erupt.upms.model.base.HyperModel;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */

//ProductionOrder

@Table(name = "fd_meta_user")
@Entity
@Inheritance(strategy = InheritanceType.JOINED)
@Erupt(name = "用户元数据",
        power = @Power(importable = false, export = false)
)
public class MetaUser extends HyperModel {

    @EruptField(
            views = @View(title = "账号"),
            edit = @Edit(title = "账号", notNull = true, search = @Search)
    )
    private String account;

    @EruptField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", notNull = true, search = @Search)
    )
    private String name;

    @EruptField(
            edit = @Edit(title = "密码", notNull = true, search = @Search, inputType = @InputType(type = "password"))
    )
    private String password;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn
    @EruptField(
            views = @View(title = "企业信息", column = "name"),
            edit = @Edit(title = "企业信息",
                    type = EditType.COMBINE, readonly = @Readonly(add = true, edit = true)
            )
    )
    private Tenant tenant;

    @EruptField(
            views = @View(title = "是否是租户管理员"),
            edit = @Edit(title = "是否是租户管理员", notNull = true, search = @Search, boolType = @BoolType
            )


    )
    private Boolean tenantAdminFlag;

    public boolean checkIfTenantAdmin() {

        return this.tenantAdminFlag;
    }

    static boolean tenantAdminFlagShow = true ;

}

