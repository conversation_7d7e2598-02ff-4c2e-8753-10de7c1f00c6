package com.example.tribon.service;

import com.example.tribon.dto.GenerateRouteMapResponseDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class RouteMapRuleService {
    // --- 规则管理和验证 ---

    // --- 扩展的规则引擎 ---

    // 高级规则库，支持更复杂的连接规则
    @Data
    @lombok.EqualsAndHashCode(callSuper = false)
    public static class AdvancedRuleLibrary {
        // 连接策略：包括最近优先、优先级优先、最小生成树
        private String connectionStrategy;

        /* 是否开启房间规则
         * 开启后，只允许同房间的节点进行连接
         * */
        private Boolean enableRoomRule = true;

        /* 是否开启贯穿件规则
         * 开启后，跨房间之间，只允许贯穿件节点可以连接到其他节点
         */
        private Boolean enableThroughPieceRule = true;

        // 连接方向性规则：BIDIRECTIONAL, UNIDIRECTIONAL, REVERSE_ONLY
        private Map<String, Map<String, ConnectionDirection>> connectionDirections;

        // 连接数量限制：当前节点最多连接数
        private Integer maxConnections;

        /* 连接优先级规则：每种类型的连接优先级
        * 示例：以下示例表示针对设备，优先找距离100内最近的托架，如果没找到，则找距离100内的扁条，依次按优先级依次搜索
        *      源类型 - 目标类型 - 优先级  - 最大距离 - 连接数量限制（在该中类型中找个指定的数量后，如果总的数量为达到，则继续搜索）
        *      设备  - 托架     - 1     - 100     - 1
        *      设备  - 扁条     - 2     - 100     - 1
        * */
        private Map<String, Map<String, Integer>> connectionPriorityRules;

        // 最大距离规则：不同类型组合的最大连接距离
        private Map<String, Map<String, Double>> maxDistanceRules;

        // 连接数量限制规则：每种连接类型的数量限制
        private Map<String, Map<String, Integer>> connectionLimits;

        // 互斥规则：某些连接类型互斥
        private Map<String, Set<String>> exclusiveConnections;

        // 必需连接规则：某些类型必须连接
        private Map<String, Set<String>> requiredConnections;

        // 条件规则：基于属性的条件连接
        private List<ConditionalRule> conditionalRules;

        // 获取指定连接的最大距离
        public Double getMaxDistanceForConnection(String sourceType, String targetType) {
            if (maxDistanceRules != null && maxDistanceRules.containsKey(sourceType)) {
                return maxDistanceRules.get(sourceType).get(targetType);
            }
            return 1000.0; // 默认最大距离
        }

        // 获取指定连接的数量限制
        public Integer getConnectionLimitForConnection(String sourceType, String targetType) {
            if (connectionLimits != null && connectionLimits.containsKey(sourceType)) {
                return connectionLimits.get(sourceType).get(targetType);
            }
            return 1; // 默认连接限制
        }

        // 获取指定连接的方向
        public ConnectionDirection getConnectionDirection(String sourceType, String targetType) {
            if (connectionDirections != null && connectionDirections.containsKey(sourceType)) {
                ConnectionDirection direction = connectionDirections.get(sourceType).get(targetType);
                return direction != null ? direction : ConnectionDirection.BIDIRECTIONAL;
            }
            return ConnectionDirection.BIDIRECTIONAL; // 默认双向连接
        }
    }

    // 连接方向枚举
    public enum ConnectionDirection {
        BIDIRECTIONAL,    // 双向连接
        UNIDIRECTIONAL,   // 单向连接（仅从源到目标）
        REVERSE_ONLY      // 反向连接（仅从目标到源）
    }

    // 条件规则
    @Data
    public static class ConditionalRule {
        private String sourceType;
        private String targetType;
        private String condition;        // 条件表达式
        private int priority;
        private double maxDistance;
        private boolean enabled;
    }

    // 连接策略枚举
    public enum ConnectionStrategy {
        NEAREST_FIRST,      // 最近优先
        PRIORITY_FIRST,     // 优先级优先
        BALANCED,           // 平衡策略
        MINIMUM_SPANNING,   // 最小生成树
        CUSTOM              // 自定义策略
    }

    // 规则配置管理器
    public static class RuleConfigManager {

        // 验证规则库的一致性
        public static ValidationResult validateRuleLibrary(AdvancedRuleLibrary ruleLibrary) {
            ValidationResult result = new ValidationResult();

            // 检查基本规则
            if (ruleLibrary.getConnectionPriorityRules() == null ||
                    ruleLibrary.getConnectionPriorityRules().isEmpty()) {
                result.addError("连接优先级规则不能为空");
            }

            // 检查最大连接数
            if (ruleLibrary.getMaxConnections() == null || ruleLibrary.getMaxConnections() <= 0) {
                result.addError("最大连接数必须大于0");
            }

            // 检查方向性规则一致性
            if (ruleLibrary.getConnectionDirections() != null) {
                validateDirectionRules(ruleLibrary, result);
            }

            // 检查互斥规则
            if (ruleLibrary.getExclusiveConnections() != null) {
                validateExclusiveRules(ruleLibrary, result);
            }

            // 检查必需连接规则
            if (ruleLibrary.getRequiredConnections() != null) {
                validateRequiredRules(ruleLibrary, result);
            }

            return result;
        }

        // 验证方向性规则
        private static void validateDirectionRules(AdvancedRuleLibrary ruleLibrary, ValidationResult result) {
            Map<String, Map<String, ConnectionDirection>> directions = ruleLibrary.getConnectionDirections();
            Map<String, Map<String, Integer>> priorities = ruleLibrary.getConnectionPriorityRules();

            for (String sourceType : directions.keySet()) {
                if (!priorities.containsKey(sourceType)) {
                    result.addWarning("方向规则中的类型 " + sourceType + " 在优先级规则中不存在");
                }

                Map<String, ConnectionDirection> targetDirections = directions.get(sourceType);
                for (String targetType : targetDirections.keySet()) {
                    if (!priorities.get(sourceType).containsKey(targetType)) {
                        result.addWarning("方向规则中的连接 " + sourceType + "->" + targetType + " 在优先级规则中不存在");
                    }
                }
            }
        }

        // 验证互斥规则
        private static void validateExclusiveRules(AdvancedRuleLibrary ruleLibrary, ValidationResult result) {
            Map<String, Set<String>> exclusives = ruleLibrary.getExclusiveConnections();
            Map<String, Set<String>> required = ruleLibrary.getRequiredConnections();

            if (required != null) {
                for (String type : exclusives.keySet()) {
                    Set<String> exclusiveTypes = exclusives.get(type);
                    Set<String> requiredTypes = required.get(type);

                    if (requiredTypes != null) {
                        Set<String> conflict = new HashSet<>(exclusiveTypes);
                        conflict.retainAll(requiredTypes);
                        if (!conflict.isEmpty()) {
                            result.addError("类型 " + type + " 的互斥规则与必需规则冲突: " + conflict);
                        }
                    }
                }
            }
        }

        // 验证必需连接规则
        private static void validateRequiredRules(AdvancedRuleLibrary ruleLibrary, ValidationResult result) {
            Map<String, Set<String>> required = ruleLibrary.getRequiredConnections();
            Map<String, Map<String, Integer>> priorities = ruleLibrary.getConnectionPriorityRules();

            for (String sourceType : required.keySet()) {
                if (!priorities.containsKey(sourceType)) {
                    result.addError("必需连接规则中的类型 " + sourceType + " 在优先级规则中不存在");
                } else {
                    Set<String> requiredTypes = required.get(sourceType);
                    Map<String, Integer> availableTypes = priorities.get(sourceType);

                    for (String requiredType : requiredTypes) {
                        if (!availableTypes.containsKey(requiredType)) {
                            result.addError("必需连接 " + sourceType + "->" + requiredType + " 在优先级规则中不存在");
                        }
                    }
                }
            }
        }

        // 创建默认规则库
        public static AdvancedRuleLibrary createDefaultRuleLibrary() {
            AdvancedRuleLibrary ruleLibrary = new AdvancedRuleLibrary();

            // 基本配置
            ruleLibrary.setConnectionStrategy("BALANCED");
            ruleLibrary.setEnableRoomRule(true);
            ruleLibrary.setEnableThroughPieceRule(true);
            ruleLibrary.setMaxConnections(5);

            // 基本连接规则
            Map<String, Map<String, Integer>> connectionRules = new HashMap<>();

            // 设备连接规则
            Map<String, Integer> deviceConnections = new HashMap<>();
            deviceConnections.put("扁条", 1);
            deviceConnections.put("托架", 2);
            deviceConnections.put("支架", 3);
            connectionRules.put("设备", deviceConnections);

            // 扁条连接规则
            Map<String, Integer> flatConnections = new HashMap<>();
            flatConnections.put("设备", 1);
            flatConnections.put("托架", 2);
            flatConnections.put("贯穿件", 3);
            connectionRules.put("扁条", flatConnections);

            // 托架连接规则
            Map<String, Integer> bracketConnections = new HashMap<>();
            bracketConnections.put("设备", 1);
            bracketConnections.put("扁条", 2);
            bracketConnections.put("支架", 3);
            connectionRules.put("托架", bracketConnections);

            // 支架连接规则
            Map<String, Integer> supportConnections = new HashMap<>();
            supportConnections.put("设备", 1);
            supportConnections.put("托架", 2);
            supportConnections.put("贯穿件", 3);
            connectionRules.put("支架", supportConnections);

            ruleLibrary.setConnectionPriorityRules(connectionRules);

            // 设置默认方向性规则（双向连接）
            Map<String, Map<String, ConnectionDirection>> directions = new HashMap<>();
            for (String sourceType : connectionRules.keySet()) {
                Map<String, ConnectionDirection> targetDirections = new HashMap<>();
                for (String targetType : connectionRules.get(sourceType).keySet()) {
                    targetDirections.put(targetType, ConnectionDirection.BIDIRECTIONAL);
                }
                directions.put(sourceType, targetDirections);
            }
            ruleLibrary.setConnectionDirections(directions);

            // 设置默认最大距离规则
            Map<String, Map<String, Double>> maxDistanceRules = new HashMap<>();
            for (String sourceType : connectionRules.keySet()) {
                Map<String, Double> targetDistances = new HashMap<>();
                for (String targetType : connectionRules.get(sourceType).keySet()) {
                    targetDistances.put(targetType, 1000.0);
                }
                maxDistanceRules.put(sourceType, targetDistances);
            }
            ruleLibrary.setMaxDistanceRules(maxDistanceRules);

            // 设置默认连接数量限制
            Map<String, Map<String, Integer>> connectionLimits = new HashMap<>();
            for (String sourceType : connectionRules.keySet()) {
                Map<String, Integer> targetLimits = new HashMap<>();
                for (String targetType : connectionRules.get(sourceType).keySet()) {
                    targetLimits.put(targetType, 2);
                }
                connectionLimits.put(sourceType, targetLimits);
            }
            ruleLibrary.setConnectionLimits(connectionLimits);

            return ruleLibrary;
        }
    }

    // 验证结果类
    @Data
    public static class ValidationResult {
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();

        public void addError(String error) {
            errors.add(error);
        }

        public void addWarning(String warning) {
            warnings.add(warning);
        }

        public boolean isValid() {
            return errors.isEmpty();
        }

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }
    }

    // 规则库构建器，支持流式API
    public static class RuleLibraryBuilder {
        private AdvancedRuleLibrary ruleLibrary = new AdvancedRuleLibrary();

        public static RuleLibraryBuilder create() {
            return new RuleLibraryBuilder();
        }

        /*         * 添加连接优先级规则
         * @param sourceType 源类型
         * @param targetType 目标类型
         * @param priority 优先级
         * @return RuleLibraryBuilder
         */
        public RuleLibraryBuilder addConnectionRule(String sourceType, String targetType, int priority) {
            if (ruleLibrary.getConnectionPriorityRules() == null) {
                ruleLibrary.setConnectionPriorityRules(new HashMap<>());
            }
            ruleLibrary.getConnectionPriorityRules()
                    .computeIfAbsent(sourceType, k -> new HashMap<>())
                    .put(targetType, priority);
            return this;
        }

        public RuleLibraryBuilder setMaxConnections(int maxConnections) {
            ruleLibrary.setMaxConnections(maxConnections);
            return this;
        }

        public RuleLibraryBuilder setConnectionDirection(String sourceType, String targetType, ConnectionDirection direction) {
            if (ruleLibrary.getConnectionDirections() == null) {
                ruleLibrary.setConnectionDirections(new HashMap<>());
            }
            ruleLibrary.getConnectionDirections()
                    .computeIfAbsent(sourceType, k -> new HashMap<>())
                    .put(targetType, direction);
            return this;
        }

        public RuleLibraryBuilder setMaxDistanceForConnection(String sourceType, String targetType, double maxDistance) {
            if (ruleLibrary.getMaxDistanceRules() == null) {
                ruleLibrary.setMaxDistanceRules(new HashMap<>());
            }
            ruleLibrary.getMaxDistanceRules()
                    .computeIfAbsent(sourceType, k -> new HashMap<>())
                    .put(targetType, maxDistance);
            return this;
        }

        public RuleLibraryBuilder setConnectionLimitForConnection(String sourceType, String targetType, int connectionLimit) {
            if (ruleLibrary.getConnectionLimits() == null) {
                ruleLibrary.setConnectionLimits(new HashMap<>());
            }
            ruleLibrary.getConnectionLimits()
                    .computeIfAbsent(sourceType, k -> new HashMap<>())
                    .put(targetType, connectionLimit);
            return this;
        }

        public AdvancedRuleLibrary build() {
            ValidationResult validation = RuleConfigManager.validateRuleLibrary(ruleLibrary);
            if (!validation.isValid()) {
                throw new IllegalStateException("规则库验证失败: " + validation.getErrors());
            }
            return ruleLibrary;
        }
    }
}
