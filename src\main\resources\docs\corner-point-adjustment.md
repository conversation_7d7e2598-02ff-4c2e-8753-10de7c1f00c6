# 角点检查与窄边中心点调整

## 🎯 需求背景

在电缆走线计算中，当最短距离的端点位于托架的角上（长边和窄边的交叉点）时，从工程实践角度考虑，应该将出入口调整到窄边的中心点。这是因为：

1. **结构强度**：角点通常是结构的薄弱环节，不适合作为电缆出入口
2. **安装便利性**：窄边中心点更便于电缆的固定和维护
3. **美观性**：避免电缆在角点处的不规则布局
4. **安全性**：减少电缆在角点处的磨损风险

## 🔧 实现方案

### 1. 角点检测

#### 托架顶点获取
```java
/**
 * 获取托架顶面的四个顶点坐标（考虑旋转）
 */
public Point3D[] getTopVertices() {
    double halfL = length / 2;
    double halfW = width / 2;
    
    // 原始顶点（相对于托架中心，未旋转）
    Point3D[] originalVertices = {
        new Point3D(-halfL, -halfW, height / 2), // 左下
        new Point3D(halfL, -halfW, height / 2),  // 右下
        new Point3D(halfL, halfW, height / 2),   // 右上
        new Point3D(-halfL, halfW, height / 2)   // 左上
    };
    
    // 应用旋转变换并平移到世界坐标
    for (int i = 0; i < 4; i++) {
        rotatedVertices[i] = applyRotation(originalVertices[i], rotX, rotY, rotZ);
        rotatedVertices[i] = new Point3D(
            rotatedVertices[i].x + x,
            rotatedVertices[i].y + y,
            rotatedVertices[i].z + (zTop - height / 2)
        );
    }
    
    return rotatedVertices;
}
```

#### 角点检测方法
```java
/**
 * 检查点是否在托架的角上
 * @param point 要检查的点
 * @param tolerance 容差
 * @return 如果点在角上返回true
 */
public boolean isPointOnCorner(Point3D point, double tolerance) {
    Point3D[] vertices = getTopVertices();
    
    for (Point3D vertex : vertices) {
        double dist = distance(point, vertex);
        if (dist <= tolerance) {
            return true;
        }
    }
    
    return false;
}
```

#### 边缘角点检测
```java
/**
 * 检查点是否在边的端点附近（角点）
 */
public boolean isPointNearCorner(Point3D point, double tolerance) {
    double distToStart = distance(point, start);
    double distToEnd = distance(point, end);
    return distToStart <= tolerance || distToEnd <= tolerance;
}
```

### 2. 窄边中心点获取

#### 窄边识别
```java
/**
 * 获取窄边的中心点列表
 * 返回所有窄边（被设为点的边）的中心点
 */
public List<Point3D> getNarrowEdgeCenters() {
    Edge[] edges = getTopEdges();
    List<Point3D> narrowCenters = new ArrayList<>();
    
    for (Edge edge : edges) {
        // 检查是否为点边（窄边）
        boolean isPoint = (edge.start.x == edge.end.x && 
                          edge.start.y == edge.end.y && 
                          edge.start.z == edge.end.z);
        if (isPoint) {
            narrowCenters.add(edge.start); // 点边的起点就是中心点
        }
    }
    
    return narrowCenters;
}
```

#### 最近窄边中心点查找
```java
/**
 * 获取距离指定点最近的窄边中心点
 */
public Point3D getNearestNarrowEdgeCenter(Point3D point) {
    List<Point3D> narrowCenters = getNarrowEdgeCenters();
    
    if (narrowCenters.isEmpty()) {
        // 如果没有窄边（如正方形托架），返回托架中心点
        return new Point3D(x, y, zTop);
    }
    
    Point3D nearest = narrowCenters.get(0);
    double minDist = distance(point, nearest);
    
    for (int i = 1; i < narrowCenters.size(); i++) {
        double dist = distance(point, narrowCenters.get(i));
        if (dist < minDist) {
            minDist = dist;
            nearest = narrowCenters.get(i);
        }
    }
    
    return nearest;
}
```

### 3. 角点调整逻辑

#### 连接点调整
```java
/**
 * 检查连接点是否在角上，如果是则调整到最近的窄边中心点
 */
private static Point3D adjustCornerPointToNarrowEdge(Point3D connectionPoint, Edge edge, Tray tray) {
    if (tray == null) {
        return connectionPoint; // 没有托架信息，无法调整
    }
    
    // 定义角点容差（可以根据实际需要调整）
    double cornerTolerance = Math.min(tray.length, tray.width) * 0.1; // 10%的容差
    
    // 检查连接点是否在边的端点附近（角点）
    if (edge.isPointNearCorner(connectionPoint, cornerTolerance)) {
        // 如果在角上，调整到最近的窄边中心点
        Point3D nearestNarrowCenter = tray.getNearestNarrowEdgeCenter(connectionPoint);
        return nearestNarrowCenter;
    }
    
    return connectionPoint; // 不在角上，保持原连接点
}
```

#### 距离计算增强
```java
/**
 * 计算两条边之间的距离，支持角点检查和窄边中心点调整
 */
private static DistInfo computeDistWithTrayInfo(Edge e, Edge f, Tray eTray, Tray fTray) {
    // ... 计算初始连接点 ...
    
    // 检查两个连接点是否在角上，如果是则调整到窄边中心
    pointOnE = adjustCornerPointToNarrowEdge(pointOnE, e, eTray);
    pointOnF = adjustCornerPointToNarrowEdge(pointOnF, f, fTray);
    
    // ... 计算最终距离 ...
}
```

## 📊 调整场景示例

### 场景1：角点连接调整

#### 调整前
```
托架A: 中心(0,0,0), 尺寸200×60×20
托架B: 中心(250,80,0), 尺寸200×60×20

最短连接点:
- 起点: (100, 30, 10) ← 在托架A的右上角附近
- 终点: (150, 50, 10) ← 在托架B的左下角附近
```

#### 调整后
```
检测到角点连接，调整为:
- 起点: (100, 0, 10) ← 调整到托架A右边窄边中心
- 终点: (150, 0, 10) ← 调整到托架B左边窄边中心
```

### 场景2：非角点连接保持不变

#### 连接情况
```
托架A: 中心(0,0,0), 尺寸200×60×20
托架B: 中心(0,100,0), 尺寸200×60×20

最短连接点:
- 起点: (0, 30, 10) ← 在托架A的上边中心，非角点
- 终点: (0, 70, 10) ← 在托架B的下边中心，非角点

结果: 保持原连接点不变
```

## 🧪 测试验证

### 1. 角点检测测试
```java
@Test
void testCornerDetection() {
    CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20);
    Point3D[] vertices = tray.getTopVertices();
    
    double tolerance = 5.0;
    
    // 测试顶点本身
    for (Point3D vertex : vertices) {
        assertTrue(tray.isPointOnCorner(vertex, tolerance), "顶点应该被识别为角点");
    }
    
    // 测试顶点附近的点
    Point3D nearCorner = new Point3D(vertices[0].x + 2, vertices[0].y + 2, vertices[0].z);
    assertTrue(tray.isPointOnCorner(nearCorner, tolerance), "顶点附近的点应该被识别为角点");
    
    // 测试边中心的点（不应该是角点）
    Point3D edgeCenter = new Point3D(
        (vertices[0].x + vertices[1].x) / 2,
        (vertices[0].y + vertices[1].y) / 2,
        vertices[0].z
    );
    assertFalse(tray.isPointOnCorner(edgeCenter, tolerance), "边中心的点不应该被识别为角点");
}
```

### 2. 角点调整测试
```java
@Test
void testCornerPointAdjustment() {
    // 创建两个托架，使它们的最短连接可能在角点
    List<CableRoutingSolverService.Tray> trays = Arrays.asList(
        new CableRoutingSolverService.Tray(0, 0, 0, 0, 200, 60, 20),
        new CableRoutingSolverService.Tray(1, 250, 80, 0, 200, 60, 20)
    );
    
    List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
    
    // 验证连接点是否接近窄边中心
    // ... 检查逻辑 ...
}
```

## 🎯 容差配置

### 角点容差计算
```java
// 基于托架尺寸的动态容差
double cornerTolerance = Math.min(tray.length, tray.width) * 0.1; // 10%的容差
```

### 容差选择原则
- **小托架**：容差较小，精确检测
- **大托架**：容差较大，避免误判
- **建议范围**：托架最小尺寸的5%-15%

## 📋 配置参数

### 1. 角点检测容差
```java
// 可配置的角点检测容差
public static final double DEFAULT_CORNER_TOLERANCE_RATIO = 0.1; // 10%

// 使用示例
double tolerance = Math.min(tray.length, tray.width) * DEFAULT_CORNER_TOLERANCE_RATIO;
```

### 2. 窄边中心点查找容差
```java
// 窄边中心点匹配容差
public static final double NARROW_CENTER_MATCH_TOLERANCE = 5.0; // 5单位
```

## 🚀 优势总结

1. **✅ 工程合理性**：避免电缆在结构薄弱的角点出入
2. **✅ 结构优化**：利用窄边中心点的结构强度优势
3. **✅ 自动调整**：算法自动检测并调整角点连接
4. **✅ 灵活配置**：支持不同尺寸托架的动态容差
5. **✅ 旋转兼容**：支持任意角度旋转托架的角点检测
6. **✅ 测试完备**：提供全面的角点检测和调整测试

这个实现确保了电缆走线在遇到角点连接时能够自动调整到更合理的窄边中心点，提高了设计的工程实用性和安全性。
