package com.example.tribon.service;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.PriorityQueue;
import java.util.Comparator;
/**
 * 用于在托架顶面边缘之间计算电缆走线的最短路径，并返回每段线的起点/终点坐标
 */
@Component
public class CableRoutingSolverService {
    /** 三维点 */
    public static class Point3D {
        public final double x, y, z;
        public Point3D(double x, double y, double z) {
            this.x = x; this.y = y; this.z = z;
        }
        @Override public String toString() {
            return String.format("(%.3f, %.3f, %.3f)", x, y, z);
        }
    }

    /** 表示一个托架 */
    public static class Tray {
        public final int id;
        public final double x, y, zTop;
        public final double length, width, height;
        public final double rotX, rotY, rotZ; // 旋转角度（弧度）

        public Tray(int id, double x, double y, double zCenter,
                    double length, double width, double height) {
            this(id, x, y, zCenter, length, width, height, 0, 0, 0);
        }

        public Tray(int id, double x, double y, double zCenter,
                    double length, double width, double height,
                    double rotX, double rotY, double rotZ) {
            this.id = id;
            this.x = x;
            this.y = y;
            this.length = length;
            this.width = width;
            this.height = height;
            this.zTop = zCenter + height / 2.0;
            this.rotX = rotX;
            this.rotY = rotY;
            this.rotZ = rotZ;
        }

        /**
         * 获取托架顶面的四个顶点坐标（考虑旋转）
         */
        public Point3D[] getTopVertices() {
            double halfL = length / 2;
            double halfW = width / 2;

            // 原始顶点（相对于托架中心，未旋转）
            Point3D[] originalVertices = {
                new Point3D(-halfL, -halfW, height / 2), // 左下
                new Point3D(halfL, -halfW, height / 2),  // 右下
                new Point3D(halfL, halfW, height / 2),   // 右上
                new Point3D(-halfL, halfW, height / 2)   // 左上
            };

            // 应用旋转变换
            Point3D[] rotatedVertices = new Point3D[4];
            for (int i = 0; i < 4; i++) {
                rotatedVertices[i] = applyRotation(originalVertices[i], rotX, rotY, rotZ);
                // 平移到世界坐标
                rotatedVertices[i] = new Point3D(
                    rotatedVertices[i].x + x,
                    rotatedVertices[i].y + y,
                    rotatedVertices[i].z + (zTop - height / 2)
                );
            }

            return rotatedVertices;
        }

        /**
         * 获取托架顶面的四条边
         */
        public Edge[] getTopEdges() {
            Point3D[] vertices = getTopVertices();
            return new Edge[] {
                new Edge(id, 0, vertices[0], vertices[1]), // 下边：左下 → 右下
                new Edge(id, 1, vertices[2], vertices[3]), // 上边：右上 → 左上
                new Edge(id, 2, vertices[3], vertices[0]), // 左边：左上 → 左下
                new Edge(id, 3, vertices[1], vertices[2])  // 右边：右下 → 右上
            };
        }
    }

    /**
     * 应用三维旋转变换（按 Z-Y-X 顺序）
     * @param point 原始点
     * @param rotX X轴旋转角度（弧度）
     * @param rotY Y轴旋转角度（弧度）
     * @param rotZ Z轴旋转角度（弧度）
     * @return 旋转后的点
     */
    public static Point3D applyRotation(Point3D point, double rotX, double rotY, double rotZ) {
        double x = point.x, y = point.y, z = point.z;

        // Z轴旋转
        if (rotZ != 0) {
            double cosZ = Math.cos(rotZ), sinZ = Math.sin(rotZ);
            double newX = x * cosZ - y * sinZ;
            double newY = x * sinZ + y * cosZ;
            x = newX; y = newY;
        }

        // Y轴旋转
        if (rotY != 0) {
            double cosY = Math.cos(rotY), sinY = Math.sin(rotY);
            double newX = x * cosY + z * sinY;
            double newZ = -x * sinY + z * cosY;
            x = newX; z = newZ;
        }

        // X轴旋转
        if (rotX != 0) {
            double cosX = Math.cos(rotX), sinX = Math.sin(rotX);
            double newY = y * cosX - z * sinX;
            double newZ = y * sinX + z * cosX;
            y = newY; z = newZ;
        }

        return new Point3D(x, y, z);
    }

    /** 顶面四条边 */
    public static class Edge {
        public final int trayId;
        public final int side;
        public final Point3D start, end; // 边的起点和终点
        public final double x1, x2, y1, y2, z; // 为了兼容现有代码

        // 原有构造函数（兼容性）
        public Edge(int trayId, int side,
                    double x1, double x2,
                    double y1, double y2,
                    double z) {
            this.trayId = trayId;
            this.side = side;
            this.x1 = x1; this.x2 = x2;
            this.y1 = y1; this.y2 = y2;
            this.z = z;
            this.start = new Point3D(x1, y1, z);
            this.end = new Point3D(x2, y2, z);
        }

        // 新构造函数（支持任意方向的边）
        public Edge(int trayId, int side, Point3D start, Point3D end) {
            this.trayId = trayId;
            this.side = side;
            this.start = start;
            this.end = end;
            // 为了兼容现有代码，计算边界框
            this.x1 = Math.min(start.x, end.x);
            this.x2 = Math.max(start.x, end.x);
            this.y1 = Math.min(start.y, end.y);
            this.y2 = Math.max(start.y, end.y);
            this.z = (start.z + end.z) / 2; // 平均Z值
        }

        /**
         * 获取边上距离指定点最近的点
         */
        public Point3D getClosestPointTo(Point3D point) {
            // 计算边的方向向量
            double dx = end.x - start.x;
            double dy = end.y - start.y;
            double dz = end.z - start.z;

            // 计算从起点到目标点的向量
            double px = point.x - start.x;
            double py = point.y - start.y;
            double pz = point.z - start.z;

            // 计算投影参数 t
            double edgeLengthSq = dx * dx + dy * dy + dz * dz;
            if (edgeLengthSq == 0) return start; // 退化为点

            double t = (px * dx + py * dy + pz * dz) / edgeLengthSq;
            t = Math.max(0, Math.min(1, t)); // 限制在边的范围内

            return new Point3D(
                start.x + t * dx,
                start.y + t * dy,
                start.z + t * dz
            );
        }
    }

    /** 线段：从 start 到 end */
    public static class Segment {
        public final Point3D start, end;
        public Segment(Point3D start, Point3D end) {
            this.start = start;
            this.end = end;
        }
        @Override public String toString() {
            return String.format("Segment %s -> %s", start, end);
        }
    }

    // 一维区间最小距离
    private static double intervalDist(double a1, double a2, double b1, double b2) {
        if (b1 > a2) return b1 - a2;
        if (a1 > b2) return a1 - b2;
        return 0;
    }

    // 计算两条边最小曼哈顿距离，同时返回最佳点对
    public static class DistInfo {
        double dist;
        Point3D p, q;
    }
    private static DistInfo computeDist(Edge e, Edge f) {
        double dz = Math.abs(e.z - f.z);
        double zMid = (e.z + f.z) / 2.0;

        // X轴距离与点
        double dx = intervalDist(e.x1, e.x2, f.x1, f.x2);
        double px, qx;
        if (e.x2 < f.x1) { px = e.x2; qx = f.x1; }
        else if (f.x2 < e.x1) { px = e.x1; qx = f.x2; }
        else { // 区间重叠
            double overlapStart = Math.max(e.x1, f.x1);
            double overlapEnd = Math.min(e.x2, f.x2);
            px = qx = (overlapStart + overlapEnd) / 2.0;
        }

        // Y轴距离与点
        double dy = intervalDist(e.y1, e.y2, f.y1, f.y2);
        double py, qy;
        if (e.y2 < f.y1) { py = e.y2; qy = f.y1; }
        else if (f.y2 < e.y1) { py = e.y1; qy = f.y2; }
        else {
            double ovs = Math.max(e.y1, f.y1);
            double ove = Math.min(e.y2, f.y2);
            py = qy = (ovs + ove) / 2.0;
        }

        DistInfo info = new DistInfo();
        info.dist = dx + dy + dz;
        info.p = new Point3D(px, py, e.z);
        info.q = new Point3D(qx, qy, f.z);
        return info;
    }

    /**
     * 求解最短路径，并返回线段列表
     * @param trays 托架列表
     * @param srcId 源托架id
     * @param dstId 目标托架id
     */
    public static List<Segment> solve(List<Tray> trays, int srcId, int dstId) {
        // 1. 生成所有边（考虑旋转）
        List<Edge> edges = new ArrayList<>();
        for (Tray t : trays) {
            Edge[] trayEdges = t.getTopEdges();
            for (Edge edge : trayEdges) {
                edges.add(edge);
            }
        }
        int n = edges.size();

        // 2. 计算距离矩阵并记录 DistInfo
        DistInfo[][] infoMat = new DistInfo[n][n];
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i != j) {
                    infoMat[i][j] = computeDist(edges.get(i), edges.get(j));
                }
            }
        }

        // 3. 多源 Dijkstra
        double[] dist = new double[n];
        int[] prev = new int[n];
        Arrays.fill(dist, Double.POSITIVE_INFINITY);
        Arrays.fill(prev, -1);
        PriorityQueue<double[]> pq = new PriorityQueue<>(Comparator.comparingDouble(a -> a[1]));

        for (int i = 0; i < n; i++) {
            if (edges.get(i).trayId == srcId) {
                dist[i] = 0;
                pq.offer(new double[]{i, 0.0});
            }
        }

        boolean[] vis = new boolean[n];
        int target = -1;
        double bestDist = Double.POSITIVE_INFINITY;

        while (!pq.isEmpty()) {
            int u = (int) pq.poll()[0];
            if (vis[u]) continue;
            vis[u] = true;
            if (edges.get(u).trayId == dstId && dist[u] < bestDist) {
                bestDist = dist[u];
                target = u;
            }
            for (int v = 0; v < n; v++) {
                if (vis[v]) continue;
                double alt = dist[u] + infoMat[u][v].dist;
                if (alt < dist[v]) {
                    dist[v] = alt;
                    prev[v] = u;
                    pq.offer(new double[]{v, alt});
                }
            }
        }

        // 4. 还原路径 & 构造 Segment 列表
        List<Segment> result = new ArrayList<>();
        if (target >= 0) {
            List<Integer> path = new ArrayList<>();
            for (int cur = target; cur != -1; cur = prev[cur]) path.add(cur);
            Collections.reverse(path);

            // 记录起点和终点
            Point3D startPoint = null;
            Point3D endPoint = null;

            // 构建中间线段
            for (int i = 0; i < path.size()-1; i++) {
                DistInfo di = infoMat[path.get(i)][path.get(i+1)];
                result.add(new Segment(di.p, di.q));

                // 记录起点（第一段的起点）
                if (i == 0) {
                    startPoint = di.p;
                }

                // 记录终点（最后一段的终点）
                if (i == path.size()-2) {
                    endPoint = di.q;
                }
            }

            // 如果路径为空但找到了起点和终点，添加一个直接连接
            if (result.isEmpty() && startPoint != null && endPoint != null) {
                result.add(new Segment(startPoint, endPoint));
            }
        }
        return result;
    }

    // 示例
//    public static void main(String[] args) {
//        List<Tray> trays = Arrays.asList(
//                new Tray(1, 0, 0, 0, 2, 1, 0.5),
//                new Tray(2, 5, 2, 0, 3, 1.5, 0.5)
//        );
//        List<Segment> route = solve(trays, 1, 2);
//        route.forEach(System.out::println);
//    }
}
