package com.example.tribon.service;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.Comparator;
/**
 * 用于在托架顶面边缘之间计算电缆走线的最短路径，并返回每段线的起点/终点坐标
 */
@Component
public class CableRoutingSolverService {
    /** 三维点 */
    public static class Point3D {
        public final double x, y, z;
        public Point3D(double x, double y, double z) {
            this.x = x; this.y = y; this.z = z;
        }
        @Override public String toString() {
            return String.format("(%.3f, %.3f, %.3f)", x, y, z);
        }
    }

    /** 表示一个托架 */
    public static class Tray {
        public final int id;
        public final double x, y, zTop;
        public final double length, width, height;
        public final double rotX, rotY, rotZ; // 旋转角度（弧度）

        public Tray(int id, double x, double y, double zCenter,
                    double length, double width, double height) {
            this(id, x, y, zCenter, length, width, height, 0, 0, 0);
        }

        public Tray(int id, double x, double y, double zCenter,
                    double length, double width, double height,
                    double rotX, double rotY, double rotZ) {
            this.id = id;
            this.x = x;
            this.y = y;
            this.length = length;
            this.width = width;
            this.height = height;
            this.zTop = zCenter + height / 2.0;
            this.rotX = rotX;
            this.rotY = rotY;
            this.rotZ = rotZ;
        }

        /**
         * 获取托架顶面的四个顶点坐标（考虑旋转）
         */
        public Point3D[] getTopVertices() {
            double halfL = length / 2;
            double halfW = width / 2;

            // 原始顶点（相对于托架中心，未旋转）
            Point3D[] originalVertices = {
                new Point3D(-halfL, -halfW, height / 2), // 左下
                new Point3D(halfL, -halfW, height / 2),  // 右下
                new Point3D(halfL, halfW, height / 2),   // 右上
                new Point3D(-halfL, halfW, height / 2)   // 左上
            };

            // 应用旋转变换
            Point3D[] rotatedVertices = new Point3D[4];
            for (int i = 0; i < 4; i++) {
                rotatedVertices[i] = applyRotation(originalVertices[i], rotX, rotY, rotZ);
                // 平移到世界坐标
                rotatedVertices[i] = new Point3D(
                    rotatedVertices[i].x + x,
                    rotatedVertices[i].y + y,
                    rotatedVertices[i].z + (zTop - height / 2)
                );
            }

            return rotatedVertices;
        }

        /**
         * 获取托架顶面的四条边
         * 根据边的实际长度判断：长边允许沿整条边连接，窄边只允许在中心点连接
         */
        public Edge[] getTopEdges() {
            Point3D[] vertices = getTopVertices();

            // 计算四条边的长度
            double edge0Length = distance(vertices[0], vertices[1]); // 下边：左下 → 右下
            double edge1Length = distance(vertices[2], vertices[3]); // 上边：右上 → 左上
            double edge2Length = distance(vertices[3], vertices[0]); // 左边：左上 → 左下
            double edge3Length = distance(vertices[1], vertices[2]); // 右边：右下 → 右上

            // 判断哪些是长边，哪些是窄边
            // 通常对边长度相等，所以我们比较相邻边的长度
            boolean edge0IsLong = edge0Length >= edge2Length; // 下边是否比左边长
            boolean edge1IsLong = edge1Length >= edge2Length; // 上边是否比左边长
            boolean edge2IsLong = edge2Length >= edge0Length; // 左边是否比下边长
            boolean edge3IsLong = edge3Length >= edge0Length; // 右边是否比下边长

            Edge[] edges = new Edge[4];

            // 边0（下边）
            if (edge0IsLong) {
                edges[0] = new Edge(id, 0, vertices[0], vertices[1]); // 长边，允许沿边连接
            } else {
                Point3D center = new Point3D(
                    (vertices[0].x + vertices[1].x) / 2,
                    (vertices[0].y + vertices[1].y) / 2,
                    (vertices[0].z + vertices[1].z) / 2
                );
                edges[0] = new Edge(id, 0, center, center); // 窄边，只在中心点连接
            }

            // 边1（上边）
            if (edge1IsLong) {
                edges[1] = new Edge(id, 1, vertices[2], vertices[3]); // 长边，允许沿边连接
            } else {
                Point3D center = new Point3D(
                    (vertices[2].x + vertices[3].x) / 2,
                    (vertices[2].y + vertices[3].y) / 2,
                    (vertices[2].z + vertices[3].z) / 2
                );
                edges[1] = new Edge(id, 1, center, center); // 窄边，只在中心点连接
            }

            // 边2（左边）
            if (edge2IsLong) {
                edges[2] = new Edge(id, 2, vertices[3], vertices[0]); // 长边，允许沿边连接
            } else {
                Point3D center = new Point3D(
                    (vertices[3].x + vertices[0].x) / 2,
                    (vertices[3].y + vertices[0].y) / 2,
                    (vertices[3].z + vertices[0].z) / 2
                );
                edges[2] = new Edge(id, 2, center, center); // 窄边，只在中心点连接
            }

            // 边3（右边）
            if (edge3IsLong) {
                edges[3] = new Edge(id, 3, vertices[1], vertices[2]); // 长边，允许沿边连接
            } else {
                Point3D center = new Point3D(
                    (vertices[1].x + vertices[2].x) / 2,
                    (vertices[1].y + vertices[2].y) / 2,
                    (vertices[1].z + vertices[2].z) / 2
                );
                edges[3] = new Edge(id, 3, center, center); // 窄边，只在中心点连接
            }

            return edges;
        }

        /**
         * 计算两点之间的欧几里得距离
         */
        private double distance(Point3D p1, Point3D p2) {
            double dx = p1.x - p2.x;
            double dy = p1.y - p2.y;
            double dz = p1.z - p2.z;
            return Math.sqrt(dx * dx + dy * dy + dz * dz);
        }

        /**
         * 获取窄边的中心点列表
         * 返回所有窄边（被设为点的边）的中心点
         */
        public List<Point3D> getNarrowEdgeCenters() {
            Edge[] edges = getTopEdges();
            List<Point3D> narrowCenters = new ArrayList<>();

            for (Edge edge : edges) {
                // 检查是否为点边（窄边）
                boolean isPoint = (edge.start.x == edge.end.x &&
                                  edge.start.y == edge.end.y &&
                                  edge.start.z == edge.end.z);
                if (isPoint) {
                    narrowCenters.add(edge.start); // 点边的起点就是中心点
                }
            }

            return narrowCenters;
        }

        /**
         * 检查点是否在托架的角上
         * @param point 要检查的点
         * @param tolerance 容差
         * @return 如果点在角上返回true
         */
        public boolean isPointOnCorner(Point3D point, double tolerance) {
            Point3D[] vertices = getTopVertices();

            for (Point3D vertex : vertices) {
                double dist = distance(point, vertex);
                if (dist <= tolerance) {
                    return true;
                }
            }

            return false;
        }

        /**
         * 获取距离指定点最近的窄边中心点
         */
        public Point3D getNearestNarrowEdgeCenter(Point3D point) {
            List<Point3D> narrowCenters = getNarrowEdgeCenters();

            if (narrowCenters.isEmpty()) {
                // 如果没有窄边（如正方形托架），返回托架中心点
                return new Point3D(x, y, zTop);
            }

            Point3D nearest = narrowCenters.get(0);
            double minDist = distance(point, nearest);

            for (int i = 1; i < narrowCenters.size(); i++) {
                double dist = distance(point, narrowCenters.get(i));
                if (dist < minDist) {
                    minDist = dist;
                    nearest = narrowCenters.get(i);
                }
            }

            return nearest;
        }
    }

    /**
     * 应用三维旋转变换（按 Z-Y-X 顺序）
     * @param point 原始点
     * @param rotX X轴旋转角度（弧度）
     * @param rotY Y轴旋转角度（弧度）
     * @param rotZ Z轴旋转角度（弧度）
     * @return 旋转后的点
     */
    public static Point3D applyRotation(Point3D point, double rotX, double rotY, double rotZ) {
        double x = point.x, y = point.y, z = point.z;

        // Z轴旋转
        if (rotZ != 0) {
            double cosZ = Math.cos(rotZ), sinZ = Math.sin(rotZ);
            double newX = x * cosZ - y * sinZ;
            double newY = x * sinZ + y * cosZ;
            x = newX; y = newY;
        }

        // Y轴旋转
        if (rotY != 0) {
            double cosY = Math.cos(rotY), sinY = Math.sin(rotY);
            double newX = x * cosY + z * sinY;
            double newZ = -x * sinY + z * cosY;
            x = newX; z = newZ;
        }

        // X轴旋转
        if (rotX != 0) {
            double cosX = Math.cos(rotX), sinX = Math.sin(rotX);
            double newY = y * cosX - z * sinX;
            double newZ = y * sinX + z * cosX;
            y = newY; z = newZ;
        }

        return new Point3D(x, y, z);
    }

    /** 顶面四条边 */
    public static class Edge {
        public final int trayId;
        public final int side;
        public final Point3D start, end; // 边的起点和终点
        public final double x1, x2, y1, y2, z; // 为了兼容现有代码

        // 原有构造函数（兼容性）
        public Edge(int trayId, int side,
                    double x1, double x2,
                    double y1, double y2,
                    double z) {
            this.trayId = trayId;
            this.side = side;
            this.x1 = x1; this.x2 = x2;
            this.y1 = y1; this.y2 = y2;
            this.z = z;
            this.start = new Point3D(x1, y1, z);
            this.end = new Point3D(x2, y2, z);
        }

        // 新构造函数（支持任意方向的边）
        public Edge(int trayId, int side, Point3D start, Point3D end) {
            this.trayId = trayId;
            this.side = side;
            this.start = start;
            this.end = end;
            // 为了兼容现有代码，计算边界框
            this.x1 = Math.min(start.x, end.x);
            this.x2 = Math.max(start.x, end.x);
            this.y1 = Math.min(start.y, end.y);
            this.y2 = Math.max(start.y, end.y);
            this.z = (start.z + end.z) / 2; // 平均Z值
        }

        /**
         * 获取边上距离指定点最近的点
         */
        public Point3D getClosestPointTo(Point3D point) {
            // 计算边的方向向量
            double dx = end.x - start.x;
            double dy = end.y - start.y;
            double dz = end.z - start.z;

            // 计算从起点到目标点的向量
            double px = point.x - start.x;
            double py = point.y - start.y;
            double pz = point.z - start.z;

            // 计算投影参数 t
            double edgeLengthSq = dx * dx + dy * dy + dz * dz;
            if (edgeLengthSq == 0) return start; // 退化为点

            double t = (px * dx + py * dy + pz * dz) / edgeLengthSq;
            t = Math.max(0, Math.min(1, t)); // 限制在边的范围内

            return new Point3D(
                start.x + t * dx,
                start.y + t * dy,
                start.z + t * dz
            );
        }

        /**
         * 检查点是否在边的端点附近（角点）
         */
        public boolean isPointNearCorner(Point3D point, double tolerance) {
            double distToStart = distance(point, start);
            double distToEnd = distance(point, end);
            return distToStart <= tolerance || distToEnd <= tolerance;
        }

        /**
         * 获取边的中心点
         */
        public Point3D getCenter() {
            return new Point3D(
                (start.x + end.x) / 2,
                (start.y + end.y) / 2,
                (start.z + end.z) / 2
            );
        }

        /**
         * 计算两点之间的距离
         */
        private double distance(Point3D p1, Point3D p2) {
            double dx = p1.x - p2.x;
            double dy = p1.y - p2.y;
            double dz = p1.z - p2.z;
            return Math.sqrt(dx * dx + dy * dy + dz * dz);
        }
    }

    /** 线段：从 start 到 end */
    public static class Segment {
        public final Point3D start, end;
        public Segment(Point3D start, Point3D end) {
            this.start = start;
            this.end = end;
        }
        @Override public String toString() {
            return String.format("Segment %s -> %s", start, end);
        }
    }

    // 一维区间最小距离
    private static double intervalDist(double a1, double a2, double b1, double b2) {
        if (b1 > a2) return b1 - a2;
        if (a1 > b2) return a1 - b2;
        return 0;
    }

    // 计算两条边最小曼哈顿距离，同时返回最佳点对
    public static class DistInfo {
        double dist;
        Point3D p, q;
    }
    private static DistInfo computeDist(Edge e, Edge f) {
        return computeDistWithTrayInfo(e, f, null, null);
    }

    /**
     * 计算两条边之间的距离，支持角点检查和窄边中心点调整
     */
    private static DistInfo computeDistWithTrayInfo(Edge e, Edge f, Tray eTray, Tray fTray) {
        // 使用新的Edge类的方法来计算最优连接点
        Point3D pointOnE, pointOnF;

        // 检查是否为点边（窄边中心点）
        boolean eIsPoint = (e.start.x == e.end.x && e.start.y == e.end.y && e.start.z == e.end.z);
        boolean fIsPoint = (f.start.x == f.end.x && f.start.y == f.end.y && f.start.z == f.end.z);

        if (eIsPoint && fIsPoint) {
            // 两个都是点，直接连接
            pointOnE = e.start;
            pointOnF = f.start;
        } else if (eIsPoint) {
            // e是点，f是边，找f上距离e最近的点
            pointOnE = e.start;
            pointOnF = f.getClosestPointTo(e.start);

            // 检查f上的连接点是否在角上，如果是则调整到窄边中心
            pointOnF = adjustCornerPointToNarrowEdge(pointOnF, f, fTray);
        } else if (fIsPoint) {
            // f是点，e是边，找e上距离f最近的点
            pointOnF = f.start;
            pointOnE = e.getClosestPointTo(f.start);

            // 检查e上的连接点是否在角上，如果是则调整到窄边中心
            pointOnE = adjustCornerPointToNarrowEdge(pointOnE, e, eTray);
        } else {
            // 两个都是边，需要找最优连接点
            Point3D eMid = e.getCenter();
            Point3D fMid = f.getCenter();

            pointOnE = e.getClosestPointTo(fMid);
            pointOnF = f.getClosestPointTo(eMid);

            // 检查两个连接点是否在角上，如果是则调整到窄边中心
            pointOnE = adjustCornerPointToNarrowEdge(pointOnE, e, eTray);
            pointOnF = adjustCornerPointToNarrowEdge(pointOnF, f, fTray);
        }

        // 计算曼哈顿距离
        double dx = Math.abs(pointOnE.x - pointOnF.x);
        double dy = Math.abs(pointOnE.y - pointOnF.y);
        double dz = Math.abs(pointOnE.z - pointOnF.z);

        DistInfo info = new DistInfo();
        info.dist = dx + dy + dz;
        info.p = pointOnE;
        info.q = pointOnF;
        return info;
    }

    /**
     * 检查连接点是否在角上，如果是则调整到最近的窄边中心点
     */
    private static Point3D adjustCornerPointToNarrowEdge(Point3D connectionPoint, Edge edge, Tray tray) {
        if (tray == null) {
            return connectionPoint; // 没有托架信息，无法调整
        }

        // 定义角点容差（可以根据实际需要调整）
        double cornerTolerance = Math.min(tray.length, tray.width) * 0.1; // 10%的容差

        // 检查连接点是否在边的端点附近（角点）
        if (edge.isPointNearCorner(connectionPoint, cornerTolerance)) {
            // 如果在角上，调整到最近的窄边中心点
            Point3D nearestNarrowCenter = tray.getNearestNarrowEdgeCenter(connectionPoint);
            return nearestNarrowCenter;
        }

        return connectionPoint; // 不在角上，保持原连接点
    }

    /**
     * 求解最短路径，并返回线段列表
     * @param trays 托架列表
     * @param srcId 源托架id
     * @param dstId 目标托架id
     */
    public static List<Segment> solve(List<Tray> trays, int srcId, int dstId) {
        // 1. 生成所有边（考虑旋转）
        List<Edge> edges = new ArrayList<>();
        for (Tray t : trays) {
            Edge[] trayEdges = t.getTopEdges();
            for (Edge edge : trayEdges) {
                edges.add(edge);
            }
        }
        int n = edges.size();

        // 2. 计算距离矩阵并记录 DistInfo（包含角点检查）
        DistInfo[][] infoMat = new DistInfo[n][n];

        // 创建托架映射，用于角点检查
        Map<Integer, Tray> trayMap = new HashMap<>();
        for (Tray tray : trays) {
            trayMap.put(tray.id, tray);
        }

        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i != j) {
                    Edge edgeI = edges.get(i);
                    Edge edgeJ = edges.get(j);
                    Tray trayI = trayMap.get(edgeI.trayId);
                    Tray trayJ = trayMap.get(edgeJ.trayId);

                    infoMat[i][j] = computeDistWithTrayInfo(edgeI, edgeJ, trayI, trayJ);
                }
            }
        }

        // 3. 多源 Dijkstra
        double[] dist = new double[n];
        int[] prev = new int[n];
        Arrays.fill(dist, Double.POSITIVE_INFINITY);
        Arrays.fill(prev, -1);
        PriorityQueue<double[]> pq = new PriorityQueue<>(Comparator.comparingDouble(a -> a[1]));

        for (int i = 0; i < n; i++) {
            if (edges.get(i).trayId == srcId) {
                dist[i] = 0;
                pq.offer(new double[]{i, 0.0});
            }
        }

        boolean[] vis = new boolean[n];
        int target = -1;
        double bestDist = Double.POSITIVE_INFINITY;

        while (!pq.isEmpty()) {
            int u = (int) pq.poll()[0];
            if (vis[u]) continue;
            vis[u] = true;
            if (edges.get(u).trayId == dstId && dist[u] < bestDist) {
                bestDist = dist[u];
                target = u;
            }
            for (int v = 0; v < n; v++) {
                if (vis[v]) continue;
                double alt = dist[u] + infoMat[u][v].dist;
                if (alt < dist[v]) {
                    dist[v] = alt;
                    prev[v] = u;
                    pq.offer(new double[]{v, alt});
                }
            }
        }

        // 4. 还原路径 & 构造 Segment 列表
        List<Segment> result = new ArrayList<>();
        if (target >= 0) {
            List<Integer> path = new ArrayList<>();
            for (int cur = target; cur != -1; cur = prev[cur]) path.add(cur);
            Collections.reverse(path);

            // 记录起点和终点
            Point3D startPoint = null;
            Point3D endPoint = null;

            // 构建中间线段
            for (int i = 0; i < path.size()-1; i++) {
                DistInfo di = infoMat[path.get(i)][path.get(i+1)];
                result.add(new Segment(di.p, di.q));

                // 记录起点（第一段的起点）
                if (i == 0) {
                    startPoint = di.p;
                }

                // 记录终点（最后一段的终点）
                if (i == path.size()-2) {
                    endPoint = di.q;
                }
            }

            // 如果路径为空但找到了起点和终点，添加一个直接连接
            if (result.isEmpty() && startPoint != null && endPoint != null) {
                result.add(new Segment(startPoint, endPoint));
            }
        }
        return result;
    }

    // 示例
//    public static void main(String[] args) {
//        List<Tray> trays = Arrays.asList(
//                new Tray(1, 0, 0, 0, 2, 1, 0.5),
//                new Tray(2, 5, 2, 0, 3, 1.5, 0.5)
//        );
//        List<Segment> route = solve(trays, 1, 2);
//        route.forEach(System.out::println);
//    }
}
