package com.example.tribon.service;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.PriorityQueue;
import java.util.Comparator;
/**
 * 用于在托架顶面边缘之间计算电缆走线的最短路径，并返回每段线的起点/终点坐标
 */
@Component
public class CableRoutingSolverService {
    /** 三维点 */
    public static class Point3D {
        public final double x, y, z;
        public Point3D(double x, double y, double z) {
            this.x = x; this.y = y; this.z = z;
        }
        @Override public String toString() {
            return String.format("(%.3f, %.3f, %.3f)", x, y, z);
        }
    }

    /** 表示一个托架 */
    public static class Tray {
        public final int id;
        public final double x, y, zTop;
        public final double length, width;
        public Tray(int id, double x, double y, double zCenter,
                    double length, double width, double height) {
            this.id = id;
            this.x = x;
            this.y = y;
            this.length = length;
            this.width = width;
            this.zTop = zCenter + height / 2.0;
        }
    }

    /** 顶面四条边 */
    public static class Edge {
        public final int trayId;
        public final int side;
        public final double x1, x2, y1, y2, z;
        public Edge(int trayId, int side,
                    double x1, double x2,
                    double y1, double y2,
                    double z) {
            this.trayId = trayId;
            this.side = side;
            this.x1 = x1; this.x2 = x2;
            this.y1 = y1; this.y2 = y2;
            this.z = z;
        }
    }

    /** 线段：从 start 到 end */
    public static class Segment {
        public final Point3D start, end;
        public Segment(Point3D start, Point3D end) {
            this.start = start;
            this.end = end;
        }
        @Override public String toString() {
            return String.format("Segment %s -> %s", start, end);
        }
    }

    // 一维区间最小距离
    private static double intervalDist(double a1, double a2, double b1, double b2) {
        if (b1 > a2) return b1 - a2;
        if (a1 > b2) return a1 - b2;
        return 0;
    }

    // 计算两条边最小曼哈顿距离，同时返回最佳点对
    private static class DistInfo {
        double dist;
        Point3D p, q;
    }
    private static DistInfo computeDist(Edge e, Edge f) {
        double dz = Math.abs(e.z - f.z);
        double zMid = (e.z + f.z) / 2.0;

        // X轴距离与点
        double dx = intervalDist(e.x1, e.x2, f.x1, f.x2);
        double px, qx;
        if (e.x2 < f.x1) { px = e.x2; qx = f.x1; }
        else if (f.x2 < e.x1) { px = e.x1; qx = f.x2; }
        else { // 区间重叠
            double overlapStart = Math.max(e.x1, f.x1);
            double overlapEnd = Math.min(e.x2, f.x2);
            px = qx = (overlapStart + overlapEnd) / 2.0;
        }

        // Y轴距离与点
        double dy = intervalDist(e.y1, e.y2, f.y1, f.y2);
        double py, qy;
        if (e.y2 < f.y1) { py = e.y2; qy = f.y1; }
        else if (f.y2 < e.y1) { py = e.y1; qy = f.y2; }
        else {
            double ovs = Math.max(e.y1, f.y1);
            double ove = Math.min(e.y2, f.y2);
            py = qy = (ovs + ove) / 2.0;
        }

        DistInfo info = new DistInfo();
        info.dist = dx + dy + dz;
        info.p = new Point3D(px, py, e.z);
        info.q = new Point3D(qx, qy, f.z);
        return info;
    }

    /**
     * 求解最短路径，并返回线段列表
     * @param trays 托架列表
     * @param srcId 源托架id
     * @param dstId 目标托架id
     */
    public static List<Segment> solve(List<Tray> trays, int srcId, int dstId) {
        // 1. 生成所有边
        List<Edge> edges = new ArrayList<>();
        for (Tray t : trays) {
            double halfL = t.length / 2;
            double halfW = t.width / 2;
            double z = t.zTop;
            edges.add(new Edge(t.id, 0, t.x-halfL, t.x+halfL, t.y-halfW, t.y-halfW, z));
            edges.add(new Edge(t.id, 1, t.x-halfL, t.x+halfL, t.y+halfW, t.y+halfW, z));
            edges.add(new Edge(t.id, 2, t.x-halfL, t.x-halfL, t.y-halfW, t.y+halfW, z));
            edges.add(new Edge(t.id, 3, t.x+halfL, t.x+halfL, t.y-halfW, t.y+halfW, z));
        }
        int n = edges.size();

        // 2. 计算距离矩阵并记录 DistInfo
        DistInfo[][] infoMat = new DistInfo[n][n];
        for (int i = 0; i < n; i++) {
            for (int j = i+1; j < n; j++) {
                infoMat[i][j] = computeDist(edges.get(i), edges.get(j));
                infoMat[j][i] = computeDist(edges.get(j), edges.get(i));
            }
        }

        // 3. 多源 Dijkstra
        double[] dist = new double[n];
        int[] prev = new int[n];
        Arrays.fill(dist, Double.POSITIVE_INFINITY);
        Arrays.fill(prev, -1);
        PriorityQueue<int[]> pq = new PriorityQueue<>(Comparator.comparingDouble(a -> a[1]));

        for (int i = 0; i < n; i++) {
            if (edges.get(i).trayId == srcId) {
                dist[i] = 0;
                pq.offer(new int[]{i, 0});
            }
        }

        boolean[] vis = new boolean[n];
        int target = -1;
        double bestDist = Double.POSITIVE_INFINITY;

        while (!pq.isEmpty()) {
            int u = pq.poll()[0];
            if (vis[u]) continue;
            vis[u] = true;
            if (edges.get(u).trayId == dstId && dist[u] < bestDist) {
                bestDist = dist[u];
                target = u;
            }
            for (int v = 0; v < n; v++) {
                if (vis[v]) continue;
                double alt = dist[u] + infoMat[u][v].dist;
                if (alt < dist[v]) {
                    dist[v] = alt;
                    prev[v] = u;
                    pq.offer(new int[]{v, (int)alt});
                }
            }
        }

        // 4. 还原路径 & 构造 Segment 列表
        List<Segment> result = new ArrayList<>();
        if (target >= 0) {
            List<Integer> path = new ArrayList<>();
            for (int cur = target; cur != -1; cur = prev[cur]) path.add(cur);
            Collections.reverse(path);
            for (int i = 0; i < path.size()-1; i++) {
                DistInfo di = infoMat[path.get(i)][path.get(i+1)];
                result.add(new Segment(di.p, di.q));
            }
        }
        return result;
    }

    // 示例
//    public static void main(String[] args) {
//        List<Tray> trays = Arrays.asList(
//                new Tray(1, 0, 0, 0, 2, 1, 0.5),
//                new Tray(2, 5, 2, 0, 3, 1.5, 0.5)
//        );
//        List<Segment> route = solve(trays, 1, 2);
//        route.forEach(System.out::println);
//    }
}
