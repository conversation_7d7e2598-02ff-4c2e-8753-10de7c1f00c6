package com.example.wom.order.domain.handler;


import com.example.wom.order.domain.ProductionOrder;
import com.example.wom.order.domain.ProductionOrderP2;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.invoke.DataProcessorManager;
import xyz.erupt.core.service.EruptCoreService;
import xyz.erupt.core.view.EruptModel;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.service.EruptDataServiceDbImpl;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-10.
 */
@Component
public class OrderRelease2OperationHandlerImpl implements OperationHandler<ProductionOrder, ProductionOrderP2> {

    @Resource
    private HttpServletRequest request; //展示自动注入功能

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptDataServiceDbImpl eruptDataServiceDb;

    @Override
    public String exec(List<ProductionOrder> data, ProductionOrderP2 orderExpendOperator, String[] param) {

//        EruptModel eruptModel = EruptCoreService.getErupt(erupt);
//        Erupts.powerLegal(eruptModel, PowerObject::isAdd);
//        Map<String, Object> extraData = new HashMap<>();
//        this.setLinkValue(eruptModel, extraData);
//        this.setDrillValue(eruptModel, extraData);
//        EruptApiModel eruptApiModel = EruptUtil.validateEruptValue(eruptModel, data);
//        if (eruptApiModel.getStatus() == EruptApiModel.Status.ERROR) return eruptApiModel;
//        Object obj = EruptUtil.jsonToEruptEntity(eruptModel, data, extraData);
//        DataProxyInvoke.invoke(eruptModel, (dataProxy -> dataProxy.beforeAdd(obj)));
//        DataProcessorManager.getEruptDataProcessor(eruptModel.getClazz()).addData(eruptModel, obj);
//        this.modifyLog(eruptModel, "ADD", data.toString());
//        DataProxyInvoke.invoke(eruptModel, (dataProxy -> dataProxy.afterAdd(obj)));
//        return EruptApiModel.successApi();
        EruptModel eruptModel = EruptCoreService.getErupt("ProductionOrder");
        DataProcessorManager.getEruptDataProcessor(eruptModel.getClazz()).addData(eruptModel, orderExpendOperator);
        return "alert('成功')";
//        throw new EruptApiErrorTip(new EruptApiModel(EruptApiModel.Status.WARNING,
//                "自定义报错提示：" + request.getServletPath(), EruptApiModel.PromptWay.NOTIFY));
    }

}
