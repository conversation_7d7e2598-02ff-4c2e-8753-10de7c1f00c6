package com.example.wom.order.domain.handler;


import com.example.wom.order.domain.ProductionOrder;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.core.view.EruptApiModel;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-10.
 */
@Component
public class OrderExpendOperationHandlerImpl implements OperationHandler<ProductionOrder, OrderExpendOperator> {

    @Resource
    private HttpServletRequest request; //展示自动注入功能

    @Override
    public String exec(List<ProductionOrder> data, OrderExpendOperator orderExpendOperator, String[] param) {
        throw new EruptApiErrorTip(new EruptApiModel(EruptApiModel.Status.WARNING,
                "自定义报错提示：" + request.getServletPath(), EruptApiModel.PromptWay.NOTIFY));
    }

}
