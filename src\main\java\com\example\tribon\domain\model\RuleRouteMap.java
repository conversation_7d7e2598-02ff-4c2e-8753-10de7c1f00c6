package com.example.tribon.domain.model;

import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;
import java.util.List;
import java.util.Set;

@Erupt(
        name = "安装件路线图规则"
)
@Table(name = "tribon_rule_route_map")
@Entity
@Getter
@Setter
public class RuleRouteMap extends BaseModel {

    @EruptField(
            views = @View(title = "规则名称"),
            edit = @Edit(title = "规则名称", notNull = true, search = @Search(vague = true))
    )
    @Column(length = 100)
    private String ruleName;

    @EruptField(
            views = @View(title = "规则描述"),
            edit = @Edit(title = "规则描述", type = EditType.TEXTAREA)
    )
    @Column(length = 500)
    private String description;

    @EruptField(
            views = @View(title = "连接策略"),
            edit = @Edit(title = "连接策略", type = EditType.CHOICE,
                    choiceType = @xyz.erupt.annotation.sub_field.sub_edit.ChoiceType(
                            vl = {
                                    @xyz.erupt.annotation.sub_field.sub_edit.VL(value = "NEAREST_FIRST", label = "最近优先"),
                                    @xyz.erupt.annotation.sub_field.sub_edit.VL(value = "PRIORITY_FIRST", label = "优先级优先"),
                                    @xyz.erupt.annotation.sub_field.sub_edit.VL(value = "BALANCED", label = "平衡策略"),
                                    @xyz.erupt.annotation.sub_field.sub_edit.VL(value = "MINIMUM_SPANNING", label = "最小生成树")
                            }
                    ))
    )
    @Enumerated(EnumType.STRING)
    private ConnectionStrategy connectionStrategy = ConnectionStrategy.BALANCED;

    @EruptField(
            views = @View(title = "启用房间规则"),
            edit = @Edit(title = "启用房间规则", type = EditType.BOOLEAN,
                    boolType = @xyz.erupt.annotation.sub_field.sub_edit.BoolType(
                            trueText = "启用", falseText = "禁用"
                    ))
    )
    private Boolean enableRoomRule = true;

    @EruptField(
            views = @View(title = "启用贯穿件规则"),
            edit = @Edit(title = "启用贯穿件规则", type = EditType.BOOLEAN,
                    boolType = @xyz.erupt.annotation.sub_field.sub_edit.BoolType(
                            trueText = "启用", falseText = "禁用"
                    ))
    )
    private Boolean enableThroughPieceRule = true;

    @EruptField(
            views = @View(title = "最大连接数"),
            edit = @Edit(title = "最大连接数", type = EditType.NUMBER,
                    numberType = @NumberType(min = 1, max = 100))
    )
    private Integer maxConnections = 5;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "rule_route_map_id")
    @EruptField(
            views = @View(title = "连接优先级规则"),
            edit = @Edit(title = "连接优先级规则", notNull = true, search = @Search(vague = true),
                    type = EditType.TAB_TABLE_ADD)
    )
    private Set<RuleRouteMapConnectionPriority> ruleRouteMapConnectionPrioritys;

    // 连接策略枚举
    public enum ConnectionStrategy {
        NEAREST_FIRST,      // 最近优先
        PRIORITY_FIRST,     // 优先级优先
        BALANCED,           // 平衡策略
        MINIMUM_SPANNING    // 最小生成树
    }

    // 连接方向枚举
    public enum ConnectionDirection {
        BIDIRECTIONAL,    // 双向连接
        UNIDIRECTIONAL,   // 单向连接（仅从源到目标）
        REVERSE_ONLY      // 反向连接（仅从目标到源）
    }
}
