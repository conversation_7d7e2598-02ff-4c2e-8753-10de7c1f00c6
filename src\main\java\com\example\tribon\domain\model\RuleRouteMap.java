package com.example.tribon.domain.model;

import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Erupt(
           name = "安装件路线图规则"
    )
    @Table(name = "tribon_rule_route_map")
    @Entity
    @Getter
    @Setter
    public class RuleRouteMap extends BaseModel {


    @OneToMany(cascade = CascadeType.ALL)
    @EruptField(
            views = @View(title = "规则"),
            edit = @Edit(title = "规则", notNull = true, search = @Search(vague = true),
            type = EditType.TAB_TABLE_ADD )
    )
    private Set<RuleRouteMapConnectionPriority> ruleRouteMapConnectionPrioritys;

    @EruptField(
            views = @View(title = "最大距离"),
            edit = @Edit(title = "最大距离", notNull = true, type = EditType.NUMBER, numberType = @NumberType(min = 0), search = @Search(vague = true))
    )
    private double maxDistance;


}
