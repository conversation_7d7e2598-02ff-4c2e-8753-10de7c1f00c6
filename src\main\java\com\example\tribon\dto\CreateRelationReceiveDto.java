package com.example.tribon.dto;

import lombok.Data;

@Data
/**
 * 定义接收来自交互端创建关系的对象结构
 */
public class CreateRelationReceiveDto {
    private StartStruct startStruct;
    private EndStruct endStruct;
    private String edgeFlagStructCode;

    @Data
    public static class StartStruct {
        private String structCode;
        private String x1FlagStructCode;
        private String x2FlagStructCode;
    }

    @Data
    public static class EndStruct {
        private String structCode;
        private String x1FlagStructCode;
        private String x2FlagStructCode;
    }
}
