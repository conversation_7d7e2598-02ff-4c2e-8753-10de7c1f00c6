package com.example.demo.model.namingrule.biz.domain.namingRule.proxy;


import com.example.demo.model.namingrule.api.enumeration.NamingRuleEnum;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRule;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameter;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;

import java.util.Arrays;
import java.util.List;

public class NamingRuleDataProxy implements DataProxy<NamingRule> {

    @Override
    public void afterAdd(NamingRule namingRule){
//      校验SR类型参数指定的依赖参数的有效性
        if (!checkSrParameterDependencies(namingRule.getNamingRuleParameterList())){
            throw new EruptApiErrorTip("依赖参数不正确");
        }
    }

    @Override
    public void afterUpdate(NamingRule namingRule){
//      校验SR类型参数指定的依赖参数的有效性
        if (!checkSrParameterDependencies(namingRule.getNamingRuleParameterList())){
            throw new EruptApiErrorTip("依赖参数不正确");
//            迁移后使用国际化
//            throw new FabosJsonApiErrorTip(FabosI18nTranslate.$translate("namingRule.depend_parameter_not_exist"));
        }
    }

    public static boolean checkSrParameterDependencies(List<NamingRuleParameter> namingRuleParameters) {
        for (NamingRuleParameter parameter : namingRuleParameters) {
            if (NamingRuleEnum.Enum.SR.name().equals(parameter.getParameterType())){
                String[] dependencies = parameter.getDependParameters().split(",");
//              检查依赖参数中是否包含自身
                if (Arrays.stream(dependencies).anyMatch(parameter.getParameterName()::equals)){
                    return false;
                }
//              检查依赖参数是否存在
                for (String dependency : dependencies) {
                    boolean found = false;
                    for (NamingRuleParameter p : namingRuleParameters) {
                        if (p.getParameterName().equals(dependency.trim())) {
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

}
