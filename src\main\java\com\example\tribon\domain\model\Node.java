package com.example.tribon.domain.model;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.List;

@Erupt(
        name = "节点"
)
@Table(name = "tribon_node"
)
@Entity
@Getter
@Setter
public class Node extends BaseModel {
    @EruptField(
            views = @View(title = "唯一编号"),
            edit = @Edit(title = "唯一编号", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private String code;

    @EruptField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    @Comment("节点类型:实节点、虚节点。交叉点（拐弯点）为实节点，其他为虚节点")
    private String type;

    @Comment("关联对象类型, 该节点关联的对象类型，包括设备、扁条、托架、支架、贯穿件")
    @EruptField(
            views = @View(title = "关联对象类型"),
            edit = @Edit(title = "关联对象类型", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private String relatedStructType;

    @Comment("关联对象编码, 该节点关联的对象编码，包括设备、扁条、托架、支架，贯穿件")
    @EruptField(
            views = @View(title = "关联对象编码"),
            edit = @Edit(title = "关联对象编码", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private String relatedStructCode;

    /**
     * 所属房间编号
     */
    private String roomCode;

    @Comment("节点X坐标")
    @EruptField(
            views = @View(title = "节点X坐标"),
            edit = @Edit(title = "节点X坐标", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private Double x;

    @Comment("节点Y坐标")
    @EruptField(
            views = @View(title = "节点Y坐标"),
            edit = @Edit(title = "节点Y坐标", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private Double y;

    @Comment("节点Z坐标")
    @EruptField(
            views = @View(title = "节点Z坐标"),
            edit = @Edit(title = "节点Z坐标", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private Double z;

    @Comment("下一节点编号List, 逗号分隔")
    @EruptField(
            views = @View(title = "下一节点编号List"),
            edit = @Edit(title = "下一节点编号List", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private String nextNodeCodeList;


}
