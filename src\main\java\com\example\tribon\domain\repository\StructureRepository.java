package com.example.tribon.domain.repository;

import com.example.tribon.domain.model.Structure;
import com.example.tribon.domain.model.dto.StructureDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface StructureRepository extends JpaRepository<Structure, Long> {
    @Query("select new com.example.tribon.domain.model.dto.StructureDto(s.cogXMark1StructCode, s.cogXMark2StructCode) from Structure s where upper(s.code) = upper(?1)")
    List<StructureDto> getStructureXExact(String code);

    @Query("select new com.example.tribon.domain.model.dto.StructureDto(s.cogXMark1StructCode, s.cogXMark2StructCode) from Structure s where upper(s.code) like upper(?1)")
    List<StructureDto> getStructureXLike(String code);
}