package com.example.demo.model.material.biz.domain.proxy;

import com.example.demo.model.material.biz.domain.model.MaterialCategory;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRule;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameter;
import com.example.demo.model.namingrule.biz.domain.namingRule.proxy.NamingRuleDataProxy;
import com.example.demo.model.namingrule.biz.domain.namingRule.service.NamingRuleDomainService;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;

@Component
public class MaterialCategoryDataProxy implements DataProxy<MaterialCategory> {
    @Resource
    private EruptDao eruptDao;


    @Override
    public void afterAdd(MaterialCategory materialCategory){
        //设置级别
        if (materialCategory.getParentCategory() == null){
            materialCategory.setCategoryLevel(1);
        } else {
            MaterialCategory parentMaterialCategory = eruptDao.getEntityManager().find(MaterialCategory.class, materialCategory.getParentCategory().getId());
            materialCategory.setCategoryLevel(parentMaterialCategory.getCategoryLevel());
        }
    }

    @Override
    public void afterUpdate(MaterialCategory materialCategory){
        //调整级别
        if (materialCategory.getParentCategory() == null){
            materialCategory.setCategoryLevel(1);
        } else {
            MaterialCategory parentMaterialCategory = eruptDao.getEntityManager().find(MaterialCategory.class, materialCategory.getParentCategory().getId());
            materialCategory.setCategoryLevel(parentMaterialCategory.getCategoryLevel() + 1);
        }
    }
}
