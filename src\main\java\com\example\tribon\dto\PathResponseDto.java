package com.example.tribon.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 定义两个安装件直接的路径起点和终端3D坐标
 */
@Getter
@Setter
public class PathResponseDto {
    private Point startPoint; // 起点坐标
    private Point endPoint;   // 终点坐标


    @Data
    private static class Point {
        private double x; // X坐标
        private double y; // Y坐标
        private double z; // Z坐标

        public Point(double x, double y, double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }

    }
}


