package com.example.tribon.handler;

import com.example.tribon.domain.model.Structure;
import com.example.tribon.domain.model.subModel.StructureExtractForm;
import com.example.tribon.service.TribonService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import xyz.erupt.annotation.fun.OperationHandler;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.dao.EruptJpaDao;

import javax.annotation.Resource;
import java.util.List;

@Component
@Transactional
public class ExistsRemoteModeOperationHandlerImpl implements OperationHandler<Structure, StructureExtractForm> {

    @Resource
    private EruptJpaDao eruptJpaDao;

    @Resource
    private EruptDao eruptDao;

    @Resource
    private TribonService tribonService;

    @Override
    public String exec(List<Structure> data, StructureExtractForm structureExtractForm, String[] param) {
        tribonService.existRemoteMode();
        return null;
    }

}
