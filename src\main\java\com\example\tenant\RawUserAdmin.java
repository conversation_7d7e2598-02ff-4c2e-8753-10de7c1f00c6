package com.example.tenant;

import com.google.gson.annotations.Expose;
import org.springframework.data.annotation.Transient;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.EruptSmartSkipSerialize;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table()
@Entity
@Erupt(name = "租户管理员",
        power = @Power(importable = false, export = false)
)
public class RawUserAdmin extends RawUser {

    @EruptField(
            views = @View(title = "管理员账号"),
            edit = @Edit(title = "管理员账号", notNull = true, search = @Search)
    )
    private String adminAccount;


    @EruptField(
            views = @View(title = "账号1", show = false),
            edit = @Edit(title = "账号1", show = false)
    )
    private String account;

    @EruptField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", notNull = true, search = @Search)
    )
    private String name;

    @EruptField(
            views = @View(title = "是否是租户管理员"),
            edit = @Edit(title = "是否是租户管理员", notNull = true, search = @Search, boolType = @BoolType
            )
    )
    private Boolean tenantAdminFlag;



}