package com.example.tribon.domain.model;


import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.Readonly;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.List;
@Erupt(
        name = "房间"
)
@Table(name = "tribon_room"
)
@Entity
@Getter
@Setter
public class Room extends BaseModel {

    @EruptField(
            views = @View(title = "唯一编号"),
            edit = @Edit(title = "唯一编号", readonly = @Readonly(add = false, edit = true),
                    notNull = false, search = @Search(vague = true))
    )
    private String code;

    @EruptField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", readonly = @Readonly(add = false, edit = true),notNull = true, search = @Search(vague = true))
    )
    private String name;


    /**
     * 房间坐标点系，至少三个点，该点系范围内的所有点都在该房间内
     * 格式为[[1,2,3],[2,3,4]]
     */
    @EruptField(
            views = @View(title = "房间坐标点系"),
            edit = @Edit(title = "房间坐标点系", notNull = false, search = @Search(vague = true))
    )
    private String roomPoints;
}
