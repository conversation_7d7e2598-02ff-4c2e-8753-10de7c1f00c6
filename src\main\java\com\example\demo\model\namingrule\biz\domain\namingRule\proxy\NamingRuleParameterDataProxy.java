package com.example.demo.model.namingrule.biz.domain.namingRule.proxy;



import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRule;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameter;
import org.springframework.stereotype.Component;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.core.exception.EruptApiErrorTip;
import xyz.erupt.jpa.dao.EruptDao;
import com.example.demo.model.namingrule.biz.domain.namingRule.service.NamingRuleDomainService;

import javax.annotation.Resource;

@Component
public class NamingRuleParameterDataProxy implements DataProxy<NamingRuleParameter> {

    @Resource
    private EruptDao eruptDao;
    @Resource
    private NamingRuleDomainService namingRuleDomainService;

    @Override
    public void afterAdd(NamingRuleParameter namingRuleParameter){
        NamingRule namingRule = namingRuleDomainService.getParametersByCode(namingRuleParameter.getNamingRule().getNamingRuleCode());
        //      校验SR类型参数指定的依赖参数的有效性
        if (!NamingRuleDataProxy.checkSrParameterDependencies(namingRule.getNamingRuleParameterList())){
            throw new EruptApiErrorTip("依赖参数不正确");
        }
    }

    @Override
    public void afterUpdate(NamingRuleParameter namingRuleParameter){

        NamingRule namingRule = namingRuleDomainService.getParametersByCode(namingRuleParameter.getNamingRule().getNamingRuleCode());
//      校验SR类型参数指定的依赖参数的有效性
        if (!NamingRuleDataProxy.checkSrParameterDependencies(namingRule.getNamingRuleParameterList())){
            throw new EruptApiErrorTip("依赖参数不正确");
        }
    }


}
