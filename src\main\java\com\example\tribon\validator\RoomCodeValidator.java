package com.example.tribon.validator;

import com.example.tribon.domain.model.Structure;
import xyz.erupt.annotation.fun.DataProxy;

/**
 * 房间编码验证器
 * 确保只有贯穿件才允许配置多个房间编码（逗号分隔）
 */
public class RoomCodeValidator implements DataProxy<Structure> {

    @Override
    public void beforeAdd(Structure structure) {
        validateRoomCode(structure);
    }

    @Override
    public void beforeUpdate(Structure structure) {
        validateRoomCode(structure);
    }

    /**
     * 验证房间编码的有效性
     */
    private void validateRoomCode(Structure structure) {
        if (!structure.isRoomCodeValid()) {
            String error = structure.getRoomCodeValidationError();
            throw new RuntimeException(error != null ? error : "房间编码格式无效");
        }
    }
}
