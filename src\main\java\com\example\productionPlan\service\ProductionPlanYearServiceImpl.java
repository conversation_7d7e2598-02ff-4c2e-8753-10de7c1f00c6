//package com.example.productionPlan.service;
//
//import com.alibaba.fastjson2.JSON;
//import com.alibaba.fastjson2.JSONObject;
//import com.example.productionPlan.port.ProductionPlanPortService;
//import com.example.productionPlan_api.service.ProductionPlanYearService;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.springframework.stereotype.Service;
//import xyz.erupt.jpa.service.EruptDataServiceDbImpl;
//
//@Service
//@DubboService
//public class ProductionPlanYearServiceImpl implements ProductionPlanYearService {
//
//    private final ProductionPlanPortService productionPlanPortService;
//
//    public ProductionPlanYearServiceImpl(ProductionPlanPortService productionPlanPortService) {
//        this.productionPlanPortService = productionPlanPortService;
//    }
//
////    @Override
////    public String getOrderNumberById(String OrderNumber){
////        Object resultFromProductionOrder = productionPlanPortService.getOrderNumberByIdFromProductionOrder("Order001");
////        return "getOrderNumberByIdFromPlanYear:" + resultFromProductionOrder;
////    }
////    @Override
////    public String updateOrder(String jsonObject){
////        JSONObject order =  JSON.parseObject(jsonObject);
////        return "updateOrder";
////    }
//}
