package com.example.wom.order.inbound.local;

import com.example.wom.order.domain.ProductionOrder;
import com.example.wom.order_api.dto.ConfirmOrderCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Service
@RequiredArgsConstructor
public class ProductionOrderCmdService {

    @Resource
    private EruptDao eruptDao;

    @Transactional
    public boolean confirmOrder(ConfirmOrderCmd cmd){
        ProductionOrder po = eruptDao.findById(ProductionOrder.class, cmd.getId());
        po.confirm();
        eruptDao.persistAndFlush(po);
        return true;
    }
}
