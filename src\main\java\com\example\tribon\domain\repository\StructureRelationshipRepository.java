package com.example.tribon.domain.repository;

import com.example.tribon.domain.model.StructureRelationship;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface StructureRelationshipRepository extends JpaRepository<StructureRelationship, Long> {
    @Query("select s from StructureRelationship s where upper(s.structCodeA) = upper(?1) or upper(s.structCodeB) = upper(?2)")
    List<StructureRelationship> getListExact(String structCodeA, String structCodeB);

    @Query("select s from StructureRelationship s " +
            "where upper(s.structCodeA) like upper(?1) or upper(s.structCodeB) = upper(?2)")
    List<StructureRelationship> getListLike(String structCodeA, String structCodeB);
}