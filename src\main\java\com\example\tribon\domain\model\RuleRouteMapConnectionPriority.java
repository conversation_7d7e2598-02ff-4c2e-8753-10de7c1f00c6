package com.example.tribon.domain.model;

import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.Entity;
import javax.persistence.Table;

@Erupt(
       name = "节点路线图规则-连接优先级"
)
@Table(name = "tribon_rule_route_map_conn_priority")
@Entity
@Getter
@Setter
public class RuleRouteMapConnectionPriority extends BaseModel {


    @EruptField(
            views = @View(title = "源结构类型代码"),
            edit = @Edit(title = "源结构类型代码", notNull = true, search = @Search(vague = true))
    )
    private String sourceStructTypeCode;

    @EruptField(
            views = @View(title = "目标结构类型代码"),
            edit = @Edit(title = "目标结构类型代码", notNull = true, search = @Search(vague = true))
    )
    private String targetStructTypeCode;

    @EruptField(
            views = @View(title = "连接优先级"),
            edit = @Edit(title = "连接优先级", notNull = true, type= EditType.NUMBER, numberType = @NumberType(min = 1, max = 99), search = @Search(vague = true))
    )
    private Integer priority;


}
