package com.example.tribon.domain.model;

import lombok.Getter;
import lombok.Setter;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.ChoiceType;
import xyz.erupt.annotation.sub_field.sub_edit.NumberType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;
import xyz.erupt.annotation.sub_field.sub_edit.VL;
import xyz.erupt.jpa.model.BaseModel;

import javax.persistence.*;

import static com.example.tribon.domain.model.RuleRouteMap.ConnectionDirection;

@Erupt(
        name = "节点路线图规则-连接优先级"
)
@Table(name = "tribon_rule_route_map_conn_priority")
@Entity
@Getter
@Setter
public class RuleRouteMapConnectionPriority extends BaseModel {

    @EruptField(
            views = @View(title = "源结构类型代码"),
            edit = @Edit(title = "源结构类型代码", notNull = true, search = @Search(vague = true))
    )
    @Column(length = 50)
    private String sourceStructTypeCode;

    @EruptField(
            views = @View(title = "目标结构类型代码"),
            edit = @Edit(title = "目标结构类型代码", notNull = true, search = @Search(vague = true))
    )
    @Column(length = 50)
    private String targetStructTypeCode;

    @EruptField(
            views = @View(title = "连接优先级"),
            edit = @Edit(title = "连接优先级", notNull = true, type = EditType.NUMBER,
                    numberType = @NumberType(min = 1, max = 99), search = @Search(vague = true))
    )
    private Integer priority;

    @EruptField(
            views = @View(title = "最大距离"),
            edit = @Edit(title = "最大距离", type = EditType.NUMBER,
                    numberType = @NumberType(min = 0))
    )
    private Double maxDistance = 1000.0;

    @EruptField(
            views = @View(title = "连接数量限制"),
            edit = @Edit(title = "连接数量限制", type = EditType.NUMBER,
                    numberType = @NumberType(min = 1, max = 50))
    )
    private Integer connectionLimit = 1;

    @EruptField(
            views = @View(title = "连接方向"),
            edit = @Edit(title = "连接方向", type = EditType.CHOICE,
                    choiceType = @xyz.erupt.annotation.sub_field.sub_edit.ChoiceType(
                            vl = {
                                    @xyz.erupt.annotation.sub_field.sub_edit.VL(value = "BIDIRECTIONAL", label = "双向连接"),
                                    @xyz.erupt.annotation.sub_field.sub_edit.VL(value = "UNIDIRECTIONAL", label = "单向连接"),
                                    @xyz.erupt.annotation.sub_field.sub_edit.VL(value = "REVERSE_ONLY", label = "反向连接")
                            }
                    ))
    )
    @Enumerated(EnumType.STRING)
    private RuleRouteMap.ConnectionDirection connectionDirection = RuleRouteMap.ConnectionDirection.BIDIRECTIONAL;

    @EruptField(
            views = @View(title = "是否启用"),
            edit = @Edit(title = "是否启用", type = EditType.BOOLEAN,
                    boolType = @xyz.erupt.annotation.sub_field.sub_edit.BoolType(
                            trueText = "启用", falseText = "禁用"
                    ))
    )
    private Boolean enabled = true;

    @EruptField(
            views = @View(title = "规则描述"),
            edit = @Edit(title = "规则描述", type = EditType.TEXTAREA)
    )
    @Column(length = 500)
    private String ruleDescription;
}
