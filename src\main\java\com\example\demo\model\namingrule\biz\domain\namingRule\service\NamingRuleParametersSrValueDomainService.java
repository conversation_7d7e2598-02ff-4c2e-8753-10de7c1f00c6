package com.example.demo.model.namingrule.biz.domain.namingRule.service;

import com.example.demo.model.namingrule.api.enumeration.NamingRuleEnum;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameter;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameterSrValue;
import com.example.demo.model.namingrule.biz.util.StringUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

@Service
public class NamingRuleParametersSrValueDomainService {

    @Resource
    private EruptDao eruptDao;

    @Comment("根据AK获取行")
    public NamingRuleParameterSrValue getSrValueByAk(NamingRuleParameter srParameter, String srParameterVersion){
        CriteriaBuilder criteriaBuilder = eruptDao.getEntityManager().getCriteriaBuilder();
        CriteriaQuery<NamingRuleParameterSrValue> criteriaQuery = criteriaBuilder.createQuery(NamingRuleParameterSrValue.class);
        Root<NamingRuleParameterSrValue> namingRuleRoot = criteriaQuery.from(NamingRuleParameterSrValue.class);
        criteriaQuery.where(
                criteriaBuilder.and(
                        criteriaBuilder.equal(namingRuleRoot.get("srParameter"), srParameter),
                        criteriaBuilder.equal(namingRuleRoot.get("srParameterVersion"), srParameterVersion)
                )
        );
        List<NamingRuleParameterSrValue> results = eruptDao.getEntityManager().createQuery(criteriaQuery).getResultList();
        return results.size() > 0? results.get(0) : null;
    }

    @Comment("生成不带序号的规则编码")
    public String generatorNameCodeExceptSequence(@Comment("每个序号类型的参数，及其对应的依赖参数List") Map<NamingRuleParameter, List<String>> serialMap,
                                                  @Comment("每个序号类型的参数，及其在序号池中对应的一个版本数据行") Map<NamingRuleParameter, NamingRuleParameterSrValue> serialVersionMap,
                                                  @Comment("该命名规则下的所有参数List") List<NamingRuleParameter> parameters,
                                                  @Comment("变量映射表，当编码规则中有变量时，根据变量编码在该映射表中取变量参数值") Map<String, String> variableMap) {
        StringBuilder name = new StringBuilder();
        parameters.forEach(namingRuleParameter -> {
            NamingRuleEnum.Enum type = NamingRuleEnum.Enum.valueOf(namingRuleParameter.getParameterType());
            switch (type) {
                case CS:
                    name.append(namingRuleParameter.getConstantValue());
                    serialMap.forEach((k, v) -> {
                        if (v.contains(namingRuleParameter.getParameterName())) {
                            NamingRuleParameterSrValue namingRuleParameterSrValue = serialVersionMap.get(k);
                            namingRuleParameterSrValue.setSrParameterVersion(StringUtils.join(namingRuleParameterSrValue.getSrParameterVersion(), namingRuleParameter.getConstantValue()));
                        }
                    });
                    break;
                case D2:
                    int day = Calendar.getInstance().get(Calendar.DATE);
                    String formatDay = String.format("%02d", day);
                    name.append(formatDay);
                    serialMap.forEach((k, v) -> {
                        if (v.contains(namingRuleParameter.getParameterName())) {
                            NamingRuleParameterSrValue namingRuleParameterSrValue = serialVersionMap.get(k);
                            namingRuleParameterSrValue.setSrParameterVersion(StringUtils.join(namingRuleParameterSrValue.getSrParameterVersion(), formatDay));
                            namingRuleParameterSrValue.setDayText(formatDay);
                        }
                    });
                    break;
                case M2:
                    int month = Calendar.getInstance().get(Calendar.MONTH) + 1;
                    String formatMonth = String.format("%02d", month);
                    name.append(formatMonth);
                    serialMap.forEach((k, v) -> {
                        if (v.contains(namingRuleParameter.getParameterName())) {
                            NamingRuleParameterSrValue namingRuleParameterSrValue = serialVersionMap.get(k);
                            namingRuleParameterSrValue.setSrParameterVersion(StringUtils.join(namingRuleParameterSrValue.getSrParameterVersion(), formatMonth));
                            namingRuleParameterSrValue.setMonthText(formatMonth);
                        }
                    });
                    break;
                case SR:
                    name.append("<*").append(namingRuleParameter.getParameterName()).append("*>");
                    break;
                case VA:
                    if (variableMap.containsKey(namingRuleParameter.getParameterName())) {
                        String variable = variableMap.get(namingRuleParameter.getParameterName());
                        name.append(variable);
                        serialMap.forEach((k, v) -> {
                            if (v.contains(namingRuleParameter.getParameterName())) {
                                NamingRuleParameterSrValue namingRuleParameterSrValue = serialVersionMap.get(k);
                                namingRuleParameterSrValue.setSrParameterVersion(StringUtils.join(namingRuleParameterSrValue.getSrParameterVersion(), variable));
                            }
                        });
                    }
                    break;
                case Y2:
                    int year = Calendar.getInstance().get(Calendar.YEAR);
                    String formatYear = String.format("%02d", year % 100);
                    name.append(formatYear);
                    serialMap.forEach((k, v) -> {
                        if (v.contains(namingRuleParameter.getParameterName())) {
                            NamingRuleParameterSrValue namingRuleParameterSrValue = serialVersionMap.get(k);
                            namingRuleParameterSrValue.setSrParameterVersion(StringUtils.join(namingRuleParameterSrValue.getSrParameterVersion(), formatYear));
                            namingRuleParameterSrValue.setYearText(String.valueOf(year));
                        }
                    });
                    break;
                case Y4:
                    int year4 = Calendar.getInstance().get(Calendar.YEAR);
                    String formatYear4 = String.format("%04d", year4);
                    name.append(formatYear4);
                    serialMap.forEach((k, v) -> {
                        if (v.contains(namingRuleParameter.getParameterName())) {
                            NamingRuleParameterSrValue namingRuleParameterSrValue = serialVersionMap.get(k);
                            namingRuleParameterSrValue.setSrParameterVersion(StringUtils.join(namingRuleParameterSrValue.getSrParameterVersion(), formatYear4));
                            namingRuleParameterSrValue.setYearText(formatYear4);
                        }
                    });
                    break;
                default:
                    break;
            }
        });
        return name.toString();
    }

    @Comment("序号累加")
    public List<String> increaseSequenceNumber(Map<NamingRuleParameter, NamingRuleParameterSrValue> serialVersionMap) {
        List<String> sequenceNumber = new ArrayList<>();
        serialVersionMap.forEach((k, v) -> {
            NamingRuleParameterSrValue pool = getSrValueByAk(v.getSrParameter(), v.getSrParameterVersion());
            if (pool == null) {
                v.setSrNumber(k.getStartNumber());
                v.setSrParameterName(v.getSrParameter().getParameterName());
                eruptDao.persist(v);
                String format = String.format("%0" + k.getLengthNumber() + "d", k.getStartNumber());
                sequenceNumber.add(format);
            } else {
                Integer serialNumber = pool.getSrNumber() + 1;
                if (String.valueOf(serialNumber).length() > k.getLengthNumber()) {
                    serialNumber = k.getStartNumber();
                }
                pool.setSrNumber(serialNumber);
                eruptDao.merge(pool);
                String format = String.format("%0" + k.getLengthNumber() + "d", serialNumber);
                sequenceNumber.add(format);
            }

        });
        return sequenceNumber;
    }
}
