package com.example.tribon.service;
import com.example.tribon.domain.model.Structure;
import com.example.tribon.dto.GenerateRouteMapResponseDto;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;
public class RouteMapService {
        // --- 输入/输出 数据结构 ---

        // 将 Struct 转换为 Node
        public static Node convertStructToNode(Structure struct) {
            Node node = new Node();
            node.setId(struct.getCode());
            node.setType(struct.getStructType());
            node.setThroughPiece(false);
            node.setRoomCode(struct.getRoomCode()); // 设置房间编码

            // 解析坐标 (cog) 为 x, y, z
            String[] coordinates = struct.getCog().split(",");
            if (coordinates.length == 3) {
                node.setX(Double.parseDouble(coordinates[0].trim()));
                node.setY(Double.parseDouble(coordinates[1].trim()));
                node.setZ(Double.parseDouble(coordinates[2].trim()));
            }

            return node;
        }

        // 将 RuleRouteMap 转换为 RuleLibrary
//        public static RuleLibrary convertRuleRouteMapToRuleLibrary(RuleRouteMap ruleRouteMap) {
//            RuleLibrary ruleLibrary = new RuleLibrary();
//
//            // 转换 connectionPriorityRules
//            Map<String, Map<String, Integer>> connectionPriorityRules = new HashMap<>();
//            for (RuleRouteMapConnectionPriority ruleRouteMapConnectionPriority : ruleRouteMap.getRuleRouteMapConnectionPrioritys()) {
//                    connectionPriorityRules.computeIfAbsent(ruleRouteMapConnectionPriority.getSourceStructTypeCode(), k -> new HashMap<>())
//                            .put(ruleRouteMapConnectionPriority.getTargetStructTypeCode(), ruleRouteMapConnectionPriority.getPriority());
//            }
//            ruleLibrary.setConnectionPriorityRules(connectionPriorityRules);
//
//            // 设置最大距离
//            ruleLibrary.setMaxDistance(ruleRouteMap.getMaxDistance());
//
//            return ruleLibrary;
//        }
        @Data
        public static class Node {
            private String id;
            private String type;
            private double x, y, z;
            private String roomCode;
            private boolean throughPiece;
        }

//        @Data
//        public static class RuleLibrary {
//            // type -> (connectableType -> priority)
//            private Map<String, Map<String, Integer>> connectionPriorityRules;
//            private double maxDistance;
//        }

        // 连接候选对象
        @Data
        public static class ConnectionCandidate {
            private Node source;
            private Node target;
            private int priority;
            private double distance;

            public ConnectionCandidate(Node source, Node target, int priority, double distance) {
                this.source = source;
                this.target = target;
                this.priority = priority;
                this.distance = distance;
            }
        }


        // --- 简易 3D KD-Tree 实现 ---
        public static class KdTree {
            private static class KdNode {
                Node point;
                KdNode left, right;
                int axis;
                public KdNode(Node pt, int ax) { point = pt; axis = ax; }
            }

            private KdNode root;
            private final int K = 3;

            public KdTree(List<Node> points) {
                root = build(points, 0);
            }

            private KdNode build(List<Node> pts, int depth) {
                if (pts.isEmpty()) return null;
                int axis = depth % K;
                pts.sort(Comparator.comparingDouble(n -> coord(n, axis)));
                int mid = pts.size() / 2;
                KdNode node = new KdNode(pts.get(mid), axis);
                node.left  = build(pts.subList(0, mid), depth + 1);
                node.right = build(pts.subList(mid + 1, pts.size()), depth + 1);
                return node;
            }

            private double coord(Node n, int axis) {
                switch(axis) {
                    case 0: return n.getX();
                    case 1: return n.getY();
                    default: return n.getZ();
                }
            }

            /** 查找给定点 p 的 k 个最近邻（包括自身，调用时需过滤） */
            public List<Node> findKNearest(Node p, int k) {
                PriorityQueue<Map.Entry<Node, Double>> pq =
                        new PriorityQueue<>(Map.Entry.<Node,Double>comparingByValue().reversed());
                search(root, p, k + 1, pq); // 查询时多找一个，以防包含自己

                // 过滤自己，排序后返回
                return pq.stream()
                        .filter(entry -> !entry.getKey().getId().equals(p.getId())) // 排除自己
                        .sorted(Map.Entry.comparingByValue()) // 按距离升序
                        .limit(k) // 最终保留k个
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            }

            private void search(KdNode node, Node target, int k,
                                PriorityQueue<Map.Entry<Node,Double>> pq) {
                if (node == null) return;
                double dist = distance(node.point, target);
                // 保持 pq 大小 ≤ k，最大堆，移除最远的
                pq.offer(new AbstractMap.SimpleEntry<>(node.point, dist));
                if (pq.size() > k) pq.poll();

                int axis = node.axis;
                double diff = coord(target, axis) - coord(node.point, axis);
                KdNode near = diff <= 0 ? node.left : node.right;
                KdNode far  = diff <= 0 ? node.right : node.left;

                search(near, target, k, pq);
                // 如果超平面到目标的距离 < 当前堆中最远距离，则还要搜索另一边
                if (pq.size() < k || Math.abs(diff) < pq.peek().getValue()) {
                    search(far, target, k, pq);
                }
            }

            private double distance(Node a, Node b) {
                return Math.sqrt(
                        (a.getX()-b.getX())*(a.getX()-b.getX()) +
                                (a.getY()-b.getY())*(a.getY()-b.getY()) +
                                (a.getZ()-b.getZ())*(a.getZ()-b.getZ()) );
            }
        }


        // --- 优化的主算法：避免重复路线 & 支持扩展规则 ---
        public static GenerateRouteMapResponseDto findConnections(
                List<Node> nodes,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary,
                int kPerPriority) {

            // 1. 构建房间内的 KD-Tree
            Map<String, KdTree> kdByRoom = buildKdTreesByRoom(nodes);

            // 2. 使用去重策略生成连接
            Set<String> processedConnections = new HashSet<>();
            List<GenerateRouteMapResponseDto.RoutePathDto> result = new ArrayList<>();

            // 3. 按节点ID排序，确保处理顺序的一致性
            List<Node> sortedNodes = nodes.stream()
                    .filter(n -> !n.isThroughPiece() && n.getRoomCode() != null)
                    .sorted(Comparator.comparing(Node::getId))
                    .collect(Collectors.toList());

            for (Node src : sortedNodes) {
                Map<String, Integer> connMap = ruleLibrary.getConnectionPriorityRules()
                        .getOrDefault(src.getType(), Collections.emptyMap());
                if (connMap.isEmpty()) continue;

                // 按优先级处理连接，每种类型达到限制后继续下一优先级
                processConnectionsByPriority(src, connMap, kdByRoom, ruleLibrary,
                    kPerPriority, processedConnections, result);
            }

            GenerateRouteMapResponseDto resp = new GenerateRouteMapResponseDto();
            resp.setRoutePathList(result);
            return resp;
        }

        // 按优先级处理连接，每种类型达到限制后继续下一优先级
        private static void processConnectionsByPriority(
                Node src, Map<String, Integer> connMap, Map<String, KdTree> kdByRoom,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int kPerPriority,
                Set<String> processedConnections, List<GenerateRouteMapResponseDto.RoutePathDto> result) {

            // 按优先级分组
            TreeMap<Integer, Set<String>> byPriority = new TreeMap<>();
            connMap.forEach((type, priority) ->
                    byPriority.computeIfAbsent(priority, p -> new HashSet<>()).add(type));

            KdTree tree = kdByRoom.get(src.getRoomCode());
            if (tree == null) return;

            // 获取足够多的最近邻候选
            int searchCount = Math.max(kPerPriority * 3, 20);
            List<Node> nearestNodes = tree.findKNearest(src, searchCount);

            // 记录每种连接类型的已连接数量
            Map<String, Integer> connectionCounts = new HashMap<>();
            int totalConnections = 0;

            // 按优先级逐个处理
            for (Map.Entry<Integer, Set<String>> entry : byPriority.entrySet()) {
                int priority = entry.getKey();
                Set<String> typesAtThisPriority = entry.getValue();

                // 为当前优先级查找候选连接
                List<ConnectionCandidate> priorityCandidates = new ArrayList<>();

                for (Node candidate : nearestNodes) {
                    if (candidate.getId().equals(src.getId())) continue;
                    if (!typesAtThisPriority.contains(candidate.getType())) continue;

                    // 检查是否已经处理过这个连接
                    if (isConnectionProcessed(src, candidate, processedConnections)) continue;

                    double distance = calculateDistance(src, candidate);
                    // 使用每个连接类型的最大距离限制
                    Double maxDistanceForConnection = ruleLibrary.getMaxDistanceForConnection(src.getType(), candidate.getType());
                    if (distance > maxDistanceForConnection) continue;

                    priorityCandidates.add(new ConnectionCandidate(src, candidate, priority, distance));
                }

                // 按距离排序当前优先级的候选
                priorityCandidates.sort(Comparator.comparingDouble(ConnectionCandidate::getDistance));

                // 为每种类型分配连接，直到达到该类型的限制
                for (String targetType : typesAtThisPriority) {
                    Integer connectionLimit = ruleLibrary.getConnectionLimitForConnection(src.getType(), targetType);
                    int currentCount = connectionCounts.getOrDefault(targetType, 0);

                    // 为当前类型选择连接
                    for (ConnectionCandidate candidate : priorityCandidates) {
                        if (!candidate.getTarget().getType().equals(targetType)) continue;
                        if (currentCount >= connectionLimit) break;
                        if (totalConnections >= kPerPriority) break;

                        // 检查源节点和目标节点的连接数是否已达到限制
                        String sourceId = candidate.getSource().getId();
                        String targetId = candidate.getTarget().getId();

                        int sourceConnections = getNodeConnectionCount(sourceId, result, processedConnections);
                        int targetConnections = getNodeConnectionCount(targetId, result, processedConnections);

                        // 获取节点的最大连接数限制（从规则库中获取，如果没有配置则使用默认值）
                        int sourceMaxConnections = getNodeMaxConnections(candidate.getSource(), ruleLibrary);
                        int targetMaxConnections = getNodeMaxConnections(candidate.getTarget(), ruleLibrary);

                        // 如果任一节点的连接数已达到限制，跳过这个连接
                        if (sourceConnections >= sourceMaxConnections || targetConnections >= targetMaxConnections) {
                            continue;
                        }

                        // 创建连接
                        GenerateRouteMapResponseDto.RoutePathDto path = new GenerateRouteMapResponseDto.RoutePathDto();
                        path.setStartStructCode(candidate.getSource().getId());
                        path.setEndStructCode(candidate.getTarget().getId());
                        result.add(path);

                        // 更新计数和记录
                        connectionCounts.put(targetType, currentCount + 1);
                        totalConnections++;

                        // 记录已处理的连接（双向）
                        processedConnections.add(getConnectionKey(candidate.getSource(), candidate.getTarget()));
                        processedConnections.add(getConnectionKey(candidate.getTarget(), candidate.getSource()));

                        currentCount++;
                    }
                }

                // 如果总连接数已达到限制，停止处理
                if (totalConnections >= kPerPriority) break;
            }
        }

        // 构建房间内的 KD-Tree
        private static Map<String, KdTree> buildKdTreesByRoom(List<Node> nodes) {
            Map<String, KdTree> kdByRoom = new HashMap<>();
            Map<String, List<Node>> nodesByRoom = nodes.stream()
                    .filter(n -> !n.isThroughPiece() && n.getRoomCode() != null)
                    .collect(Collectors.groupingBy(Node::getRoomCode));

            nodesByRoom.forEach((roomCode, roomNodes) -> {
                if (!roomNodes.isEmpty()) {
                    kdByRoom.put(roomCode, new KdTree(roomNodes));
                }
            });
            return kdByRoom;
        }

        // 查找连接候选
        private static List<ConnectionCandidate> findConnectionCandidates(
                Node src, Map<String, Integer> connMap, Map<String, KdTree> kdByRoom,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int kPerPriority) {

            List<ConnectionCandidate> candidates = new ArrayList<>();

            // 按优先级分组
            TreeMap<Integer, Set<String>> byPriority = new TreeMap<>();
            connMap.forEach((type, priority) ->
                    byPriority.computeIfAbsent(priority, p -> new HashSet<>()).add(type));

            KdTree tree = kdByRoom.get(src.getRoomCode());
            if (tree == null) return candidates;

            // 获取足够多的最近邻候选（考虑过滤后可能不够）
            int searchCount = Math.max(kPerPriority * 3, 20);
            List<Node> nearestNodes = tree.findKNearest(src, searchCount);

            // 按优先级处理候选节点
            for (Map.Entry<Integer, Set<String>> entry : byPriority.entrySet()) {
                int priority = entry.getKey();
                Set<String> typesAtThisPriority = entry.getValue();

                for (Node candidate : nearestNodes) {
                    if (candidate.getId().equals(src.getId())) continue;
                    if (!typesAtThisPriority.contains(candidate.getType())) continue;

                    double distance = calculateDistance(src, candidate);
                    // 使用每个连接类型的最大距离限制
                    Double maxDistanceForConnection = ruleLibrary.getMaxDistanceForConnection(src.getType(), candidate.getType());
                    if (distance > maxDistanceForConnection) continue;

                    candidates.add(new ConnectionCandidate(src, candidate, priority, distance));
                }
            }

            return candidates;
        }

        // 选择最优连接（去重 + 优化选择）
        private static List<ConnectionCandidate> selectOptimalConnections(
                List<ConnectionCandidate> candidates, Set<String> processedConnections, int maxConnections) {

            return candidates.stream()
                    .filter(c -> !isConnectionProcessed(c, processedConnections))
                    .sorted(Comparator.comparingInt(ConnectionCandidate::getPriority)
                            .thenComparingDouble(ConnectionCandidate::getDistance))
                    .limit(maxConnections)
                    .collect(Collectors.toList());
        }

        // 检查连接是否已处理
        private static boolean isConnectionProcessed(ConnectionCandidate candidate, Set<String> processedConnections) {
            String key1 = getConnectionKey(candidate.getSource(), candidate.getTarget());
            String key2 = getConnectionKey(candidate.getTarget(), candidate.getSource());
            return processedConnections.contains(key1) || processedConnections.contains(key2);
        }

        // 检查连接是否已处理（重载版本）
        private static boolean isConnectionProcessed(Node source, Node target, Set<String> processedConnections) {
            String key1 = getConnectionKey(source, target);
            String key2 = getConnectionKey(target, source);
            return processedConnections.contains(key1) || processedConnections.contains(key2);
        }

        // 获取节点的当前连接数（包括作为源和目标的连接）
        private static int getNodeConnectionCount(String nodeId, List<GenerateRouteMapResponseDto.RoutePathDto> existingConnections, Set<String> processedConnections) {
            int count = 0;

            // 统计已创建的连接中涉及该节点的数量
            for (GenerateRouteMapResponseDto.RoutePathDto path : existingConnections) {
                if (path.getStartStructCode().equals(nodeId) || path.getEndStructCode().equals(nodeId)) {
                    count++;
                }
            }

            return count;
        }

        // 获取节点的最大连接数限制
        private static int getNodeMaxConnections(Node node, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary) {
            // 首先尝试从规则库的全局最大连接数获取
            Integer globalMaxConnections = ruleLibrary.getMaxConnections();
            if (globalMaxConnections != null && globalMaxConnections > 0) {
                return globalMaxConnections;
            }

            // 如果没有全局配置，可以根据节点类型设置不同的默认值
            switch (node.getType()) {
                case "设备":
                    return 5;  // 设备默认最多5个连接
                case "扁条":
                    return 10; // 扁条默认最多10个连接
                case "托架":
                    return 8;  // 托架默认最多8个连接
                case "支架":
                    return 6;  // 支架默认最多6个连接
                case "贯穿件":
                    return 20; // 贯穿件默认最多20个连接
                default:
                    return 5;  // 其他类型默认5个连接
            }
        }

        // 生成连接的唯一键
        private static String getConnectionKey(Node from, Node to) {
            return from.getId() + "->" + to.getId();
        }

        // 计算两点间距离
        private static double calculateDistance(Node a, Node b) {
            return Math.sqrt(
                    Math.pow(a.getX() - b.getX(), 2) +
                    Math.pow(a.getY() - b.getY(), 2) +
                    Math.pow(a.getZ() - b.getZ(), 2));
        }



        // 优化的连接查找算法，支持多种策略
        public static GenerateRouteMapResponseDto findConnectionsAdvanced(
                List<Node> nodes,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary,
                RouteMapRuleService.ConnectionStrategy strategy) {

            // 从规则库中获取最大连接数
            int maxConnectionsPerNode = ruleLibrary.getMaxConnections() != null ?
                ruleLibrary.getMaxConnections() : 5; // 默认值

            switch (strategy) {
                case NEAREST_FIRST:
                    return findConnectionsNearestFirst(nodes, ruleLibrary, maxConnectionsPerNode);
                case PRIORITY_FIRST:
                    return findConnectionsPriorityFirst(nodes, ruleLibrary, maxConnectionsPerNode);
                case BALANCED:
                    return findConnectionsBalanced(nodes, ruleLibrary, maxConnectionsPerNode);
                case MINIMUM_SPANNING:
                    return findConnectionsMinimumSpanning(nodes, ruleLibrary);
                default:
                    return findConnections(nodes, ruleLibrary, maxConnectionsPerNode);
            }
        }

        // 最近优先策略
        private static GenerateRouteMapResponseDto findConnectionsNearestFirst(
                List<Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int maxConnections) {
            // 实现最近优先的连接算法
            return findConnections(nodes, ruleLibrary, maxConnections);
        }

        // 优先级优先策略
        private static GenerateRouteMapResponseDto findConnectionsPriorityFirst(
                List<Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int maxConnections) {
            // 实现优先级优先的连接算法
            return findConnections(nodes, ruleLibrary, maxConnections);
        }

        // 平衡策略
        private static GenerateRouteMapResponseDto findConnectionsBalanced(
                List<Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int maxConnections) {
            // 实现平衡的连接算法，考虑连接分布的均匀性
            return findConnections(nodes, ruleLibrary, maxConnections);
        }

        // 最小生成树策略
        private static GenerateRouteMapResponseDto findConnectionsMinimumSpanning(
                List<Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary) {
            // 实现基于最小生成树的连接算法
            return findConnections(nodes, ruleLibrary, 1);
        }


}
