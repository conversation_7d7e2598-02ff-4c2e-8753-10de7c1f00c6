package com.example.tribon.service;
import com.example.tribon.domain.model.RuleRouteMap;
import com.example.tribon.domain.model.RuleRouteMapConnectionPriority;
import com.example.tribon.domain.model.Structure;
import com.example.tribon.dto.GenerateRouteMapResponseDto;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;
public class RouteMapService {
        // --- 输入/输出 数据结构 ---

        // 将 Struct 转换为 Node
        public static Node convertStructToNode(Structure struct) {
            Node node = new Node();
            node.setId(struct.getCode());
            node.setType(struct.getStructType());
            node.setThroughPiece(false);
            node.setRoomCode(struct.getRoomCode()); // 设置房间编码

            // 解析坐标 (cog) 为 x, y, z
            String[] coordinates = struct.getCog().split(",");
            if (coordinates.length == 3) {
                node.setX(Double.parseDouble(coordinates[0].trim()));
                node.setY(Double.parseDouble(coordinates[1].trim()));
                node.setZ(Double.parseDouble(coordinates[2].trim()));
            }

            return node;
        }

        // 将 RuleRouteMap 转换为 RuleLibrary
        public static RuleLibrary convertRuleRouteMapToRuleLibrary(RuleRouteMap ruleRouteMap) {
            RuleLibrary ruleLibrary = new RuleLibrary();

            // 转换 connectionPriorityRules
            Map<String, Map<String, Integer>> connectionPriorityRules = new HashMap<>();
            for (RuleRouteMapConnectionPriority ruleRouteMapConnectionPriority : ruleRouteMap.getRuleRouteMapConnectionPrioritys()) {
                    connectionPriorityRules.computeIfAbsent(ruleRouteMapConnectionPriority.getSourceStructTypeCode(), k -> new HashMap<>())
                            .put(ruleRouteMapConnectionPriority.getTargetStructTypeCode(), ruleRouteMapConnectionPriority.getPriority());
            }
            ruleLibrary.setConnectionPriorityRules(connectionPriorityRules);

            // 设置最大距离
            ruleLibrary.setMaxDistance(ruleRouteMap.getMaxDistance());

            return ruleLibrary;
        }
        @Data
        public static class Node {
            private String id;
            private String type;
            private double x, y, z;
            private String roomCode;
            private boolean throughPiece;
        }

        @Data
        public static class RuleLibrary {
            // type -> (connectableType -> priority)
            private Map<String, Map<String, Integer>> connectionPriorityRules;
            private double maxDistance;
        }


        // --- 简易 3D KD-Tree 实现 ---
        public static class KdTree {
            private static class KdNode {
                Node point;
                KdNode left, right;
                int axis;
                public KdNode(Node pt, int ax) { point = pt; axis = ax; }
            }

            private KdNode root;
            private final int K = 3;

            public KdTree(List<Node> points) {
                root = build(points, 0);
            }

            private KdNode build(List<Node> pts, int depth) {
                if (pts.isEmpty()) return null;
                int axis = depth % K;
                pts.sort(Comparator.comparingDouble(n -> coord(n, axis)));
                int mid = pts.size() / 2;
                KdNode node = new KdNode(pts.get(mid), axis);
                node.left  = build(pts.subList(0, mid), depth + 1);
                node.right = build(pts.subList(mid + 1, pts.size()), depth + 1);
                return node;
            }

            private double coord(Node n, int axis) {
                switch(axis) {
                    case 0: return n.getX();
                    case 1: return n.getY();
                    default: return n.getZ();
                }
            }

            /** 查找给定点 p 的 k 个最近邻（包括自身，调用时需过滤） */
            public List<Node> findKNearest(Node p, int k) {
                PriorityQueue<Map.Entry<Node, Double>> pq =
                        new PriorityQueue<>(Map.Entry.<Node,Double>comparingByValue().reversed());
                search(root, p, k + 1, pq); // 查询时多找一个，以防包含自己

                // 过滤自己，排序后返回
                return pq.stream()
                        .filter(entry -> !entry.getKey().getId().equals(p.getId())) // 排除自己
                        .sorted(Map.Entry.comparingByValue()) // 按距离升序
                        .limit(k) // 最终保留k个
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            }

            private void search(KdNode node, Node target, int k,
                                PriorityQueue<Map.Entry<Node,Double>> pq) {
                if (node == null) return;
                double dist = distance(node.point, target);
                // 保持 pq 大小 ≤ k，最大堆，移除最远的
                pq.offer(new AbstractMap.SimpleEntry<>(node.point, dist));
                if (pq.size() > k) pq.poll();

                int axis = node.axis;
                double diff = coord(target, axis) - coord(node.point, axis);
                KdNode near = diff <= 0 ? node.left : node.right;
                KdNode far  = diff <= 0 ? node.right : node.left;

                search(near, target, k, pq);
                // 如果超平面到目标的距离 < 当前堆中最远距离，则还要搜索另一边
                if (pq.size() < k || Math.abs(diff) < pq.peek().getValue()) {
                    search(far, target, k, pq);
                }
            }

            private double distance(Node a, Node b) {
                return Math.sqrt(
                        (a.getX()-b.getX())*(a.getX()-b.getX()) +
                                (a.getY()-b.getY())*(a.getY()-b.getY()) +
                                (a.getZ()-b.getZ())*(a.getZ()-b.getZ()) );
            }
        }


        // --- 主算法：结合规则 & KD-Tree ---
        public static GenerateRouteMapResponseDto findConnections(
                List<Node> nodes,
                RuleLibrary ruleLibrary,
                int kPerPriority) {

            // 1. 先为同房间、非贯穿件节点分别建 KD-Tree
            //    这样每次查询只会返回房间内的候选，无需再过滤房间
            Map<String, KdTree> kdByRoom = new HashMap<>();

            // 按房间分组非贯穿件节点
            Map<String, List<Node>> nodesByRoom = nodes.stream()
                    .filter(n -> !n.isThroughPiece() && n.getRoomCode() != null)
                    .collect(Collectors.groupingBy(Node::getRoomCode));

            // 为每个房间创建 KD-Tree
            nodesByRoom.forEach((roomCode, roomNodes) -> {
                if (!roomNodes.isEmpty()) {
                    kdByRoom.put(roomCode, new KdTree(roomNodes));
                }
            });

            List<GenerateRouteMapResponseDto.RoutePathDto> result = new ArrayList<>();

            for (Node src : nodes) {
                // 源节点自己如果是贯穿件就跳过
                if (src.isThroughPiece()) continue;

                // 如果源节点没有房间编码，跳过
                if (src.getRoomCode() == null) continue;

                Map<String,Integer> connMap =
                        ruleLibrary.getConnectionPriorityRules().getOrDefault(src.getType(), Collections.emptyMap());
                if (connMap.isEmpty()) continue;

                // 按优先级从小到大分组：1->[types],2->[types],...
                TreeMap<Integer, Set<String>> byPriority = new TreeMap<>();
                connMap.forEach((t, pri) ->
                        byPriority.computeIfAbsent(pri, p -> new HashSet<>()).add(t)
                );

                // 从优先级最低数值（最高优先）开始尝试，直到找到不超过 k 条连接
                int found = 0;
                for (Map.Entry<Integer, Set<String>> entry : byPriority.entrySet()) {
                    if (found >= kPerPriority) break;
                    Set<String> typesAtThisPriority = entry.getValue();

                    // 在同房间 KD-Tree 中找 kPerPriority 最近候选
                    KdTree tree = kdByRoom.get(src.getRoomCode());
                    if (tree == null) continue;
                    List<Node> knn = tree.findKNearest(src, kPerPriority);

                    // 过滤：排除自己、类型不匹配、超距
                    for (Node cand : knn) {
                        if (found >= kPerPriority) break;
                        if (cand.getId().equals(src.getId())) continue;
                        if (!typesAtThisPriority.contains(cand.getType())) continue;
                        double dist = Math.sqrt(
                                Math.pow(src.getX()-cand.getX(),2) +
                                        Math.pow(src.getY()-cand.getY(),2) +
                                        Math.pow(src.getZ()-cand.getZ(),2) );
                        if (dist > ruleLibrary.getMaxDistance()) continue;

                        // 通过所有过滤，记录连接
                        GenerateRouteMapResponseDto.RoutePathDto path =
                                new GenerateRouteMapResponseDto.RoutePathDto();
                        path.setStartStructCode(src.getId());
                        path.setEndStructCode(cand.getId());
                        result.add(path);
                        found++;
                    }
                }
            }

            GenerateRouteMapResponseDto resp = new GenerateRouteMapResponseDto();
            resp.setRoutePathList(result);
            return resp;
        }
}
