package com.example.tribon.service;
import com.example.tribon.domain.model.Structure;
import com.example.tribon.dto.GenerateRouteMapResponseDto;
import lombok.Data;

import java.util.Arrays;
import java.util.Collections;

import java.util.*;
import java.util.stream.Collectors;
public class RouteMapService {
        // --- 输入/输出 数据结构 ---

        // 将 Struct 转换为 Node
        public static Node convertStructToNode(Structure struct) {
            Node node = new Node();
            node.setId(struct.getCode());
            node.setType(struct.getStructType());

            // 设置贯穿件标识
            if (struct.getThroughPiece() != null) {
                node.setThroughPiece(struct.getThroughPiece());
            } else {
                node.setThroughPiece(false); // 默认值
            }

            // 设置房间编码（直接使用 roomCode，支持逗号分隔的多房间）
            node.setRoomCode(struct.getRoomCode());

            // 解析坐标 (cog) 为 x, y, z
            String[] coordinates = struct.getCog().split(",");
            if (coordinates.length == 3) {
                node.setX(Double.parseDouble(coordinates[0].trim()));
                node.setY(Double.parseDouble(coordinates[1].trim()));
                node.setZ(Double.parseDouble(coordinates[2].trim()));
            }

            return node;
        }

        @Data
        public static class Node {
            private String id;
            private String type;
            private double x, y, z;
            private String roomCode; // 房间编码（普通结构件单个，贯穿件逗号分隔多个）
            private boolean throughPiece;

            /**
             * 获取节点的所有房间编码
             */
            public List<String> getAllRoomCodes() {
                if (roomCode == null || roomCode.trim().isEmpty()) {
                    return Collections.emptyList();
                }

                if (throughPiece) {
                    // 贯穿件：解析逗号分隔的房间编码
                    return Arrays.asList(roomCode.split(","))
                            .stream()
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .collect(java.util.stream.Collectors.toList());
                } else {
                    // 普通结构件：单个房间编码
                    return Arrays.asList(roomCode.trim());
                }
            }

            /**
             * 检查节点是否属于指定房间
             */
            public boolean belongsToRoom(String targetRoomCode) {
                if (targetRoomCode == null || roomCode == null) {
                    return false;
                }

                if (throughPiece) {
                    // 贯穿件：检查逗号分隔的房间编码中是否包含目标房间
                    return getAllRoomCodes().contains(targetRoomCode);
                } else {
                    // 普通结构件：直接比较房间编码
                    return targetRoomCode.equals(roomCode.trim());
                }
            }

            /**
             * 检查是否为贯穿件
             */
            public boolean isThroughPiece() {
                return throughPiece;
            }
        }

//        @Data
//        public static class RuleLibrary {
//            // type -> (connectableType -> priority)
//            private Map<String, Map<String, Integer>> connectionPriorityRules;
//            private double maxDistance;
//        }

        // 连接候选对象
        @Data
        public static class ConnectionCandidate {
            private Node source;
            private Node target;
            private int priority;
            private double distance;

            public ConnectionCandidate(Node source, Node target, int priority, double distance) {
                this.source = source;
                this.target = target;
                this.priority = priority;
                this.distance = distance;
            }
        }


        // --- 简易 3D KD-Tree 实现 ---
        public static class KdTree {
            private static class KdNode {
                Node point;
                KdNode left, right;
                int axis;
                public KdNode(Node pt, int ax) { point = pt; axis = ax; }
            }

            private KdNode root;
            private final int K = 3;

            public KdTree(List<Node> points) {
                root = build(points, 0);
            }

            private KdNode build(List<Node> pts, int depth) {
                if (pts.isEmpty()) return null;
                int axis = depth % K;
                pts.sort(Comparator.comparingDouble(n -> coord(n, axis)));
                int mid = pts.size() / 2;
                KdNode node = new KdNode(pts.get(mid), axis);
                node.left  = build(pts.subList(0, mid), depth + 1);
                node.right = build(pts.subList(mid + 1, pts.size()), depth + 1);
                return node;
            }

            private double coord(Node n, int axis) {
                switch(axis) {
                    case 0: return n.getX();
                    case 1: return n.getY();
                    default: return n.getZ();
                }
            }

            /** 查找给定点 p 的 k 个最近邻（包括自身，调用时需过滤） */
            public List<Node> findKNearest(Node p, int k) {
                PriorityQueue<Map.Entry<Node, Double>> pq =
                        new PriorityQueue<>(Map.Entry.<Node,Double>comparingByValue().reversed());
                search(root, p, k + 1, pq); // 查询时多找一个，以防包含自己

                // 过滤自己，排序后返回
                return pq.stream()
                        .filter(entry -> !entry.getKey().getId().equals(p.getId())) // 排除自己
                        .sorted(Map.Entry.comparingByValue()) // 按距离升序
                        .limit(k) // 最终保留k个
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            }

            private void search(KdNode node, Node target, int k,
                                PriorityQueue<Map.Entry<Node,Double>> pq) {
                if (node == null) return;
                double dist = distance(node.point, target);
                // 保持 pq 大小 ≤ k，最大堆，移除最远的
                pq.offer(new AbstractMap.SimpleEntry<>(node.point, dist));
                if (pq.size() > k) pq.poll();

                int axis = node.axis;
                double diff = coord(target, axis) - coord(node.point, axis);
                KdNode near = diff <= 0 ? node.left : node.right;
                KdNode far  = diff <= 0 ? node.right : node.left;

                search(near, target, k, pq);
                // 如果超平面到目标的距离 < 当前堆中最远距离，则还要搜索另一边
                if (pq.size() < k || Math.abs(diff) < pq.peek().getValue()) {
                    search(far, target, k, pq);
                }
            }

            private double distance(Node a, Node b) {
                return Math.sqrt(
                        (a.getX()-b.getX())*(a.getX()-b.getX()) +
                                (a.getY()-b.getY())*(a.getY()-b.getY()) +
                                (a.getZ()-b.getZ())*(a.getZ()-b.getZ()) );
            }
        }


        // --- 优化的主算法：避免重复路线 & 支持扩展规则 ---
        public static GenerateRouteMapResponseDto findConnections(
                List<Node> nodes,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary,
                int kPerPriority) {

            // 1. 构建房间内的 KD-Tree
            Map<String, KdTree> kdByRoom = buildKdTreesByRoom(nodes);

            // 2. 使用去重策略生成连接
            Set<String> processedConnections = new HashSet<>();
            List<GenerateRouteMapResponseDto.RoutePathDto> result = new ArrayList<>();

            // 3. 按节点ID排序，确保处理顺序的一致性
            List<Node> sortedNodes = nodes.stream()
                    .filter(n -> !n.isThroughPiece() && n.getRoomCode() != null)
                    .sorted(Comparator.comparing(Node::getId))
                    .collect(Collectors.toList());

            for (Node src : sortedNodes) {
                Map<String, Integer> connMap = ruleLibrary.getConnectionPriorityRules()
                        .getOrDefault(src.getType(), Collections.emptyMap());
                if (connMap.isEmpty()) continue;

                // 按优先级处理连接，每种类型达到限制后继续下一优先级
                processConnectionsByPriority(src, connMap, kdByRoom, ruleLibrary,
                    kPerPriority, processedConnections, result, sortedNodes);
            }

            GenerateRouteMapResponseDto resp = new GenerateRouteMapResponseDto();
            resp.setRoutePathList(result);
            return resp;
        }

        // 按优先级处理连接，每种类型达到限制后继续下一优先级
        private static void processConnectionsByPriority(
                Node src, Map<String, Integer> connMap, Map<String, KdTree> kdByRoom,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int kPerPriority,
                Set<String> processedConnections, List<GenerateRouteMapResponseDto.RoutePathDto> result,
                List<Node> allNodes) {

            // 按优先级分组
            TreeMap<Integer, Set<String>> byPriority = new TreeMap<>();
            connMap.forEach((type, priority) ->
                    byPriority.computeIfAbsent(priority, p -> new HashSet<>()).add(type));

            // 获取候选节点：根据房间规则和贯穿件规则
            List<Node> nearestNodes = getCandidateNodes(src, kdByRoom, ruleLibrary, kPerPriority);

            // 记录每种连接类型的已连接数量
            Map<String, Integer> connectionCounts = new HashMap<>();
            int totalConnections = 0;

            // 按优先级逐个处理
            for (Map.Entry<Integer, Set<String>> entry : byPriority.entrySet()) {
                int priority = entry.getKey();
                Set<String> typesAtThisPriority = entry.getValue();

                // 为当前优先级查找候选连接
                List<ConnectionCandidate> priorityCandidates = new ArrayList<>();

                for (Node candidate : nearestNodes) {
                    if (candidate.getId().equals(src.getId())) continue;
                    if (!typesAtThisPriority.contains(candidate.getType())) continue;

                    // 检查是否已经处理过这个连接
                    if (isConnectionProcessed(src, candidate, processedConnections)) continue;

                    double distance = calculateDistance(src, candidate);
                    // 使用每个连接类型的最大距离限制
                    Double maxDistanceForConnection = ruleLibrary.getMaxDistanceForConnection(src.getType(), candidate.getType());
                    if (distance > maxDistanceForConnection) continue;

                    priorityCandidates.add(new ConnectionCandidate(src, candidate, priority, distance));
                }

                // 按距离排序当前优先级的候选
                priorityCandidates.sort(Comparator.comparingDouble(ConnectionCandidate::getDistance));

                // 为每种类型分配连接，直到达到该类型的限制
                for (String targetType : typesAtThisPriority) {
                    Integer connectionLimit = ruleLibrary.getConnectionLimitForConnection(src.getType(), targetType);
                    int currentCount = connectionCounts.getOrDefault(targetType, 0);

                    // 为当前类型选择连接
                    for (ConnectionCandidate candidate : priorityCandidates) {
                        if (!candidate.getTarget().getType().equals(targetType)) continue;
                        if (currentCount >= connectionLimit) break;
                        if (totalConnections >= kPerPriority) break;

                        // 检查该连接类型的已连接数是否已达到限制
                        Node sourceNode = candidate.getSource();
                        Node targetNode = candidate.getTarget();
                        String sourceType = sourceNode.getType();
                        String candidateTargetType = targetNode.getType();

                        // 获取源节点与候选目标类型的当前连接数
                        int currentConnectionCount = getConnectionTypeCount(sourceNode, candidateTargetType, result, allNodes);

                        // 获取该连接类型的最大连接数限制
                        int connectionTypeLimit = getConnectionTypeLimit(sourceType, candidateTargetType, ruleLibrary);
                        // 获取该连接类型的最大连接数限制(反向)
                        int connectionTypeLimitVerse = getConnectionTypeLimit(candidateTargetType,sourceType, ruleLibrary);
                        if (connectionTypeLimit > connectionTypeLimitVerse) {
                            connectionTypeLimit = connectionTypeLimitVerse;
                        }

                        // 如果该连接类型的连接数已达到限制，跳过这个连接
                        if (currentConnectionCount >= connectionTypeLimit) {
                            continue;
                        }






                        // 创建连接
                        GenerateRouteMapResponseDto.RoutePathDto path = new GenerateRouteMapResponseDto.RoutePathDto();
                        path.setStartStructCode(candidate.getSource().getId());
                        path.setEndStructCode(candidate.getTarget().getId());
                        result.add(path);

                        // 更新计数和记录
                        connectionCounts.put(targetType, currentCount + 1);
                        totalConnections++;

                        // 记录已处理的连接（双向）
                        processedConnections.add(getConnectionKey(candidate.getSource(), candidate.getTarget()));
                        processedConnections.add(getConnectionKey(candidate.getTarget(), candidate.getSource()));

                        currentCount++;
                    }
                }

                // 如果总连接数已达到限制，停止处理
                if (totalConnections >= kPerPriority) break;
            }
        }

        // 构建房间内的 KD-Tree（包括贯穿件的跨房间支持）
        private static Map<String, KdTree> buildKdTreesByRoom(List<Node> nodes) {
            Map<String, KdTree> kdByRoom = new HashMap<>();
            Map<String, List<Node>> nodesByRoom = new HashMap<>();

            // 处理所有节点，包括贯穿件
            for (Node node : nodes) {
                List<String> roomCodes = node.getAllRoomCodes();

                // 将节点添加到它所属的所有房间中
                for (String roomCode : roomCodes) {
                    if (roomCode != null) {
                        nodesByRoom.computeIfAbsent(roomCode, k -> new ArrayList<>()).add(node);
                    }
                }
            }

            // 为每个房间构建 KD-Tree
            nodesByRoom.forEach((roomCode, roomNodes) -> {
                if (!roomNodes.isEmpty()) {
                    kdByRoom.put(roomCode, new KdTree(roomNodes));
                }
            });

            return kdByRoom;
        }

        // 获取候选节点：根据房间规则和贯穿件规则
        private static List<Node> getCandidateNodes(Node src, Map<String, KdTree> kdByRoom,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int kPerPriority) {

            Set<Node> candidateSet = new HashSet<>();
            int searchCount = Math.max(kPerPriority * 3, 20);

            // 获取源节点的所有房间编码
            List<String> srcRoomCodes = src.getAllRoomCodes();

            for (String roomCode : srcRoomCodes) {
                KdTree tree = kdByRoom.get(roomCode);
                if (tree != null) {
                    List<Node> roomCandidates = tree.findKNearest(src, searchCount);

                    for (Node candidate : roomCandidates) {
                        // 跳过自己
                        if (candidate.getId().equals(src.getId())) continue;

                        // 检查连接规则
                        if (canConnect(src, candidate, ruleLibrary)) {
                            candidateSet.add(candidate);
                        }
                    }
                }
            }

            return new ArrayList<>(candidateSet);
        }

        // 检查两个节点是否可以连接（根据房间规则和贯穿件规则）
        private static boolean canConnect(Node src, Node target, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary) {
            // 如果禁用了房间规则，任何节点都可以连接
            if (!ruleLibrary.getEnableRoomRule()) {
                return true;
            }

            // 获取源节点和目标节点的房间编码
            List<String> srcRooms = src.getAllRoomCodes();
            List<String> targetRooms = target.getAllRoomCodes();

            // 检查是否有共同房间
            for (String srcRoom : srcRooms) {
                if (targetRooms.contains(srcRoom)) {
                    return true; // 同房间可以连接
                }
            }

            // 如果启用了贯穿件规则，检查跨房间连接
            if (ruleLibrary.getEnableThroughPieceRule()) {
                // 如果源节点或目标节点是贯穿件，可以跨房间连接
                if (src.isThroughPiece() || target.isThroughPiece()) {
                    return true;
                }
            }

            return false; // 不同房间且没有贯穿件，不能连接
        }

        // 查找连接候选
        private static List<ConnectionCandidate> findConnectionCandidates(
                Node src, Map<String, Integer> connMap, Map<String, KdTree> kdByRoom,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int kPerPriority) {

            List<ConnectionCandidate> candidates = new ArrayList<>();

            // 按优先级分组
            TreeMap<Integer, Set<String>> byPriority = new TreeMap<>();
            connMap.forEach((type, priority) ->
                    byPriority.computeIfAbsent(priority, p -> new HashSet<>()).add(type));

            KdTree tree = kdByRoom.get(src.getRoomCode());
            if (tree == null) return candidates;

            // 获取足够多的最近邻候选（考虑过滤后可能不够）
            int searchCount = Math.max(kPerPriority * 3, 20);
            List<Node> nearestNodes = tree.findKNearest(src, searchCount);

            // 按优先级处理候选节点
            for (Map.Entry<Integer, Set<String>> entry : byPriority.entrySet()) {
                int priority = entry.getKey();
                Set<String> typesAtThisPriority = entry.getValue();

                for (Node candidate : nearestNodes) {
                    if (candidate.getId().equals(src.getId())) continue;
                    if (!typesAtThisPriority.contains(candidate.getType())) continue;

                    double distance = calculateDistance(src, candidate);
                    // 使用每个连接类型的最大距离限制
                    Double maxDistanceForConnection = ruleLibrary.getMaxDistanceForConnection(src.getType(), candidate.getType());
                    if (distance > maxDistanceForConnection) continue;

                    candidates.add(new ConnectionCandidate(src, candidate, priority, distance));
                }
            }

            return candidates;
        }

        // 选择最优连接（去重 + 优化选择）
        private static List<ConnectionCandidate> selectOptimalConnections(
                List<ConnectionCandidate> candidates, Set<String> processedConnections, int maxConnections) {

            return candidates.stream()
                    .filter(c -> !isConnectionProcessed(c, processedConnections))
                    .sorted(Comparator.comparingInt(ConnectionCandidate::getPriority)
                            .thenComparingDouble(ConnectionCandidate::getDistance))
                    .limit(maxConnections)
                    .collect(Collectors.toList());
        }

        // 检查连接是否已处理
        private static boolean isConnectionProcessed(ConnectionCandidate candidate, Set<String> processedConnections) {
            String key1 = getConnectionKey(candidate.getSource(), candidate.getTarget());
            String key2 = getConnectionKey(candidate.getTarget(), candidate.getSource());
            return processedConnections.contains(key1) || processedConnections.contains(key2);
        }

        // 检查连接是否已处理（重载版本）
        private static boolean isConnectionProcessed(Node source, Node target, Set<String> processedConnections) {
            String key1 = getConnectionKey(source, target);
            String key2 = getConnectionKey(target, source);
            return processedConnections.contains(key1) || processedConnections.contains(key2);
        }

        // 获取源节点与指定目标类型的当前已连接数
        // sourceNode: 当前处理的源节点
        // targetType: 目标节点类型
        // existingConnections: 已创建的连接列表
        // allNodes: 所有节点列表，用于查找节点类型
        private static int getConnectionTypeCount(Node sourceNode, String targetType,
                List<GenerateRouteMapResponseDto.RoutePathDto> existingConnections, List<Node> allNodes) {
            int count = 0;
            String sourceId = sourceNode.getId();

            // 创建节点ID到类型的映射，提高查找效率
            Map<String, String> nodeTypeMap = new HashMap<>();
            for (Node node : allNodes) {
                nodeTypeMap.put(node.getId(), node.getType());
            }

            // 统计源节点与目标类型的已连接数
            for (GenerateRouteMapResponseDto.RoutePathDto path : existingConnections) {
                // 检查源节点作为起点连接到目标类型的数量
                if (path.getStartStructCode().equals(sourceId)) {
                    String endNodeType = nodeTypeMap.get(path.getEndStructCode());
                    if (targetType.equals(endNodeType)) {
                        count++;
                    }
                }
                // 检查源节点作为终点被目标类型连接的数量（双向连接情况）
                if (path.getEndStructCode().equals(sourceId)) {
                    String startNodeType = nodeTypeMap.get(path.getStartStructCode());
                    if (targetType.equals(startNodeType)) {
                        count++;
                    }
                }
            }

            return count;
        }

        // 获取特定连接类型的最大连接数限制
        private static int getConnectionTypeLimit(String sourceType, String targetType, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary) {
            // 从规则库中获取该连接类型的限制
            Integer limit = ruleLibrary.getConnectionLimitForConnection(sourceType, targetType);
            return limit != null ? limit : Integer.MAX_VALUE; // 如果没有配置限制，返回最大值
        }

        // 生成连接的唯一键
        private static String getConnectionKey(Node from, Node to) {
            return from.getId() + "->" + to.getId();
        }

        // 计算两点间距离
        private static double calculateDistance(Node a, Node b) {
            return Math.sqrt(
                    Math.pow(a.getX() - b.getX(), 2) +
                    Math.pow(a.getY() - b.getY(), 2) +
                    Math.pow(a.getZ() - b.getZ(), 2));
        }



        // 优化的连接查找算法，支持多种策略
        public static GenerateRouteMapResponseDto findConnectionsAdvanced(
                List<Node> nodes,
                RouteMapRuleService.AdvancedRuleLibrary ruleLibrary,
                RouteMapRuleService.ConnectionStrategy strategy) {

            // 从规则库中获取最大连接数
            int maxConnectionsPerNode = ruleLibrary.getMaxConnections() != null ?
                ruleLibrary.getMaxConnections() : 5; // 默认值

            switch (strategy) {
                case NEAREST_FIRST:
                    return findConnectionsNearestFirst(nodes, ruleLibrary, maxConnectionsPerNode);
                case PRIORITY_FIRST:
                    return findConnectionsPriorityFirst(nodes, ruleLibrary, maxConnectionsPerNode);
                case BALANCED:
                    return findConnectionsBalanced(nodes, ruleLibrary, maxConnectionsPerNode);
                case MINIMUM_SPANNING:
                    return findConnectionsMinimumSpanning(nodes, ruleLibrary);
                default:
                    return findConnections(nodes, ruleLibrary, maxConnectionsPerNode);
            }
        }

        // 最近优先策略
        private static GenerateRouteMapResponseDto findConnectionsNearestFirst(
                List<Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int maxConnections) {
            // 实现最近优先的连接算法
            return findConnections(nodes, ruleLibrary, maxConnections);
        }

        // 优先级优先策略
        private static GenerateRouteMapResponseDto findConnectionsPriorityFirst(
                List<Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int maxConnections) {
            // 实现优先级优先的连接算法
            return findConnections(nodes, ruleLibrary, maxConnections);
        }

        // 平衡策略
        private static GenerateRouteMapResponseDto findConnectionsBalanced(
                List<Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary, int maxConnections) {
            // 实现平衡的连接算法，考虑连接分布的均匀性
            return findConnections(nodes, ruleLibrary, maxConnections);
        }

        // 最小生成树策略
        private static GenerateRouteMapResponseDto findConnectionsMinimumSpanning(
                List<Node> nodes, RouteMapRuleService.AdvancedRuleLibrary ruleLibrary) {
            // 实现基于最小生成树的连接算法
            return findConnections(nodes, ruleLibrary, 1);
        }


}
