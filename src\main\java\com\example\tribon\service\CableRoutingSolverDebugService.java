package com.example.tribon.service;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.PriorityQueue;
import java.util.Comparator;

/**
 * CableRoutingSolverService 的调试版本
 * 用于详细追踪算法执行过程，找出坐标计算错误的原因
 */
@Component
public class CableRoutingSolverDebugService {

    /**
     * 调试版本的solve方法，输出详细的计算过程
     */
    public static List<CableRoutingSolverService.Segment> solveWithDebug(
            List<CableRoutingSolverService.Tray> trays, int srcId, int dstId) {
        
        System.out.println("=== 开始调试电缆走线计算 ===");
        
        // 1. 生成所有边
        List<CableRoutingSolverService.Edge> edges = new ArrayList<>();
        for (CableRoutingSolverService.Tray t : trays) {
            double halfL = t.length / 2;
            double halfW = t.width / 2;
            double z = t.zTop;
            
            System.out.println("托架 " + t.id + ":");
            System.out.println("  中心: (" + t.x + ", " + t.y + ", " + (t.zTop - t.length/2) + ")"); // 估算原始zCenter
            System.out.println("  尺寸: " + t.length + "×" + t.width + "×" + "?");
            System.out.println("  zTop: " + t.zTop);
            System.out.println("  halfL: " + halfL + ", halfW: " + halfW);
            
            // 生成四条边
            CableRoutingSolverService.Edge edge0 = new CableRoutingSolverService.Edge(t.id, 0, t.x-halfL, t.x+halfL, t.y-halfW, t.y-halfW, z);
            CableRoutingSolverService.Edge edge1 = new CableRoutingSolverService.Edge(t.id, 1, t.x-halfL, t.x+halfL, t.y+halfW, t.y+halfW, z);
            CableRoutingSolverService.Edge edge2 = new CableRoutingSolverService.Edge(t.id, 2, t.x-halfL, t.x-halfL, t.y-halfW, t.y+halfW, z);
            CableRoutingSolverService.Edge edge3 = new CableRoutingSolverService.Edge(t.id, 3, t.x+halfL, t.x+halfL, t.y-halfW, t.y+halfW, z);
            
            edges.add(edge0);
            edges.add(edge1);
            edges.add(edge2);
            edges.add(edge3);
            
            System.out.println("  边0 (下): x[" + edge0.x1 + ", " + edge0.x2 + "], y[" + edge0.y1 + ", " + edge0.y2 + "], z=" + edge0.z);
            System.out.println("  边1 (上): x[" + edge1.x1 + ", " + edge1.x2 + "], y[" + edge1.y1 + ", " + edge1.y2 + "], z=" + edge1.z);
            System.out.println("  边2 (左): x[" + edge2.x1 + ", " + edge2.x2 + "], y[" + edge2.y1 + ", " + edge2.y2 + "], z=" + edge2.z);
            System.out.println("  边3 (右): x[" + edge3.x1 + ", " + edge3.x2 + "], y[" + edge3.y1 + ", " + edge3.y2 + "], z=" + edge3.z);
        }
        
        int n = edges.size();
        System.out.println("总边数: " + n);
        
        // 2. 计算距离矩阵
        CableRoutingSolverService.DistInfo[][] infoMat = new CableRoutingSolverService.DistInfo[n][n];
        System.out.println("\n=== 计算距离矩阵 ===");
        
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i != j) {
                    infoMat[i][j] = computeDistWithDebug(edges.get(i), edges.get(j), i, j);
                }
            }
        }
        
        // 3. 多源 Dijkstra
        System.out.println("\n=== 执行 Dijkstra 算法 ===");
        double[] dist = new double[n];
        int[] prev = new int[n];
        Arrays.fill(dist, Double.POSITIVE_INFINITY);
        Arrays.fill(prev, -1);
        PriorityQueue<double[]> pq = new PriorityQueue<>(Comparator.comparingDouble(a -> a[1]));

        // 找到源托架的所有边
        System.out.println("源托架 " + srcId + " 的边:");
        for (int i = 0; i < n; i++) {
            if (edges.get(i).trayId == srcId) {
                dist[i] = 0;
                pq.offer(new double[]{i, 0.0});
                System.out.println("  边" + i + " (side " + edges.get(i).side + ")");
            }
        }

        boolean[] vis = new boolean[n];
        int target = -1;
        double bestDist = Double.POSITIVE_INFINITY;

        while (!pq.isEmpty()) {
            int u = (int) pq.poll()[0];
            if (vis[u]) continue;
            vis[u] = true;
            
            if (edges.get(u).trayId == dstId && dist[u] < bestDist) {
                bestDist = dist[u];
                target = u;
                System.out.println("找到目标边: " + u + " (side " + edges.get(u).side + "), 距离: " + dist[u]);
            }
            
            for (int v = 0; v < n; v++) {
                if (vis[v]) continue;
                double alt = dist[u] + infoMat[u][v].dist;
                if (alt < dist[v]) {
                    dist[v] = alt;
                    prev[v] = u;
                    pq.offer(new double[]{v, alt});
                }
            }
        }

        // 4. 还原路径
        System.out.println("\n=== 路径重构 ===");
        List<CableRoutingSolverService.Segment> result = new ArrayList<>();
        if (target >= 0) {
            List<Integer> path = new ArrayList<>();
            for (int cur = target; cur != -1; cur = prev[cur]) path.add(cur);
            Collections.reverse(path);
            
            System.out.println("路径: " + path);
            for (int i = 0; i < path.size(); i++) {
                int edgeIdx = path.get(i);
                CableRoutingSolverService.Edge edge = edges.get(edgeIdx);
                System.out.println("  步骤" + i + ": 边" + edgeIdx + " (托架" + edge.trayId + ", side" + edge.side + ")");
            }
            
            for (int i = 0; i < path.size()-1; i++) {
                int fromEdge = path.get(i);
                int toEdge = path.get(i+1);
                CableRoutingSolverService.DistInfo di = infoMat[fromEdge][toEdge];
                
                System.out.println("线段 " + i + ": 从边" + fromEdge + "到边" + toEdge);
                System.out.println("  起点: " + di.p + " (在边" + fromEdge + "上)");
                System.out.println("  终点: " + di.q + " (在边" + toEdge + "上)");
                
                result.add(new CableRoutingSolverService.Segment(di.p, di.q));
            }
        } else {
            System.out.println("未找到路径！");
        }
        
        System.out.println("=== 调试完成 ===\n");
        return result;
    }
    
    /**
     * 带调试信息的距离计算
     */
    private static CableRoutingSolverService.DistInfo computeDistWithDebug(
            CableRoutingSolverService.Edge e, CableRoutingSolverService.Edge f, int eIdx, int fIdx) {
        
        // 只输出关键的距离计算，避免输出过多
        if ((e.trayId == 0 && f.trayId == 1) || (e.trayId == 1 && f.trayId == 0)) {
            System.out.println("计算边" + eIdx + "到边" + fIdx + "的距离:");
            System.out.println("  边" + eIdx + ": 托架" + e.trayId + ", x[" + e.x1 + "," + e.x2 + "], y[" + e.y1 + "," + e.y2 + "], z=" + e.z);
            System.out.println("  边" + fIdx + ": 托架" + f.trayId + ", x[" + f.x1 + "," + f.x2 + "], y[" + f.y1 + "," + f.y2 + "], z=" + f.z);
        }
        
        // 使用原始的computeDist逻辑
        double dz = Math.abs(e.z - f.z);
        
        // X轴距离与点
        double dx = intervalDist(e.x1, e.x2, f.x1, f.x2);
        double px, qx;
        if (e.x2 < f.x1) { px = e.x2; qx = f.x1; }
        else if (f.x2 < e.x1) { px = e.x1; qx = f.x2; }
        else { // 区间重叠
            double overlapStart = Math.max(e.x1, f.x1);
            double overlapEnd = Math.min(e.x2, f.x2);
            px = qx = (overlapStart + overlapEnd) / 2.0;
        }

        // Y轴距离与点
        double dy = intervalDist(e.y1, e.y2, f.y1, f.y2);
        double py, qy;
        if (e.y2 < f.y1) { py = e.y2; qy = f.y1; }
        else if (f.y2 < e.y1) { py = e.y1; qy = f.y2; }
        else {
            double ovs = Math.max(e.y1, f.y1);
            double ove = Math.min(e.y2, f.y2);
            py = qy = (ovs + ove) / 2.0;
        }

        CableRoutingSolverService.DistInfo info = new CableRoutingSolverService.DistInfo();
        info.dist = dx + dy + dz;
        info.p = new CableRoutingSolverService.Point3D(px, py, e.z);
        info.q = new CableRoutingSolverService.Point3D(qx, qy, f.z);
        
        if ((e.trayId == 0 && f.trayId == 1) || (e.trayId == 1 && f.trayId == 0)) {
            System.out.println("  结果: p=" + info.p + ", q=" + info.q + ", dist=" + info.dist);
        }
        
        return info;
    }
    
    // 一维区间最小距离
    private static double intervalDist(double a1, double a2, double b1, double b2) {
        if (b1 > a2) return b1 - a2;
        if (a1 > b2) return a1 - b2;
        return 0;
    }
}
