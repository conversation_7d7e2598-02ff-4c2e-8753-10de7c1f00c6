package com.example.wom.order.domain;

import com.example.demo.handler.OperationHandlerImpl;
import com.example.modeler.processroute.ProcessRoute;
import com.example.wom.order.domain.handler.OrderExpendOperationHandlerImpl;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.expr.ExprBool;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_erupt.RowOperation;
import xyz.erupt.annotation.sub_erupt.Tpl;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.EditType;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.*;
import xyz.erupt.jpa.model.BaseModel;
import xyz.erupt.upms.handler.ViaMenuValueCtrl;

import javax.persistence.Entity;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */

//ProductionOrder


@Table(name = "bm_wom_ordr")
@Entity
@Erupt(name = "生产工单"
)
public class ProductionOrderP1 extends BaseModel {

    @EruptField(
            views = @View(title = "生产工单号"),
            edit = @Edit(title = "生产工单号", notNull = true, search = @Search)
    )
    private String productionOrderNumber;

    @OneToOne
    @EruptField(
            views = @View(title = "工艺路线", column = "routeCode"),
            edit = @Edit(title = "工艺路线", notNull = true, search = @Search,
            type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(label = "routeCode")
            )
    )
    private ProcessRoute processRoute;

    @EruptField(
            views = @View(title = "计划生产数量", sortable = true),
            edit = @Edit(title = "计划生产数量", search = @Search)
    )
    private Float productionRequestQuantity;

    @EruptField(
            views = @View(title = "是否完工"),
            edit = @Edit(title = "是否完工")
    )
    private Boolean bool;

    @EruptField(
            views = @View(title = "计划开始时间"),
            edit = @Edit(title = "计划时间", search = @Search(vague = true))
    )
    private Date startDate;


    @EruptField(
            views = @View(title = "计划完成时间"),
            edit = @Edit(title = "计划完成时间", search = @Search(vague = true))
    )
    private Date endDate;

    @EruptField(
            views = @View(title = "优先级"),
            edit = @Edit(title = "优先级", type = EditType.SLIDER, search = @Search,
                    sliderType = @SliderType(max = 10, markPoints = {3, 6, 9}))
    )
    private Integer priority;

    @EruptField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", notNull = true, search = @Search, type=EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {
                            @VL(value = "EDIT", label="新建"),
                            @VL(value = "NEW", label="展开"),
                            @VL(value = "ALLSET", label="齐套"),
                            @VL(value = "ACTIVE", label="下达"),
                            @VL(value = "COMPLETE", label="完工"),
                    })
            )
    )
    private String orderState;


    @EruptField(
            views = @View(title = "P1字段"),
            edit = @Edit(title = "P1字段", notNull = true, search = @Search)
    )
    private String p1Field;


}
