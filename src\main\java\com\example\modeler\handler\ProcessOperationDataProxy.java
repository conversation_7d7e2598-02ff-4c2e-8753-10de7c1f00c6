package com.example.modeler.handler;

import com.example.modeler.processroute.ProcessOperation;
import org.apache.poi.ss.usermodel.Workbook;
import xyz.erupt.annotation.fun.DataProxy;
import xyz.erupt.annotation.query.Condition;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/2 21:29
 */
@org.springframework.stereotype.Component
public class ProcessOperationDataProxy implements DataProxy<ProcessOperation> {

    @Override
    public void addBehavior(ProcessOperation o) {
    }

    @Override
    public void beforeAdd(ProcessOperation o) {
        o.setOperationName("aa" + o.getOperationName());
    }

    @Override
    public void afterAdd(ProcessOperation o) {
        System.err.println("afterAdd");
    }

    @Override
    public void beforeUpdate(ProcessOperation o) {
        System.err.println("beforeUpdate");
    }

    @Override
    public void afterUpdate(ProcessOperation o) {
        System.err.println("afterUpdate");
    }

    @Override
    public void beforeDelete(ProcessOperation o) {
        System.err.println("beforeDelete");
    }

    @Override
    public void afterDelete(ProcessOperation o) {
        System.err.println("afterDelete");
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        System.err.println("beforeFetch");
        return null;
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        System.err.println("afterFetch");
    }

    @Override
    public void editBehavior(ProcessOperation o) {
        System.err.println("editBehavior");
    }

    @Override
    public void excelExport(Object obj) {
        Workbook wb = (Workbook) obj;
        System.err.println("excelExport");
    }

    @Override
    public void excelImport(Object o) {
        System.err.println("excelImport");
    }

}
