package com.example.tenant;

import org.springframework.data.annotation.Transient;
import xyz.erupt.annotation.Erupt;
import xyz.erupt.annotation.EruptField;
import xyz.erupt.annotation.sub_erupt.Power;
import xyz.erupt.annotation.sub_field.Edit;
import xyz.erupt.annotation.sub_field.View;
import xyz.erupt.annotation.sub_field.sub_edit.BoolType;
import xyz.erupt.annotation.sub_field.sub_edit.Search;

import javax.persistence.AttributeOverride;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * <AUTHOR>
 * @date 2020/12/28 11:24
 */

//ProductionOrder


@Table()
@Entity
@Erupt(name = "业务用户",
        power = @Power(importable = false, export = false)
)
public class NormalUser extends MetaUser {

    @EruptField(
            views = @View(title = "普通账号"),
            edit = @Edit(title = "普通账号", notNull = true, search = @Search)
    )
    private String normalAccount;

    @EruptField(
            views = @View(title = "XXX"),
            edit = @Edit(title = "XXX", notNull = true, search = @Search, boolType = @BoolType)
    )
    private Boolean xxx;

}
