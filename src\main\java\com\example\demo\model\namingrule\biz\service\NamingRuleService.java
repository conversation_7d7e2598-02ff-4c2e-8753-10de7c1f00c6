package com.example.demo.model.namingrule.biz.service;

import com.example.demo.model.namingrule.api.enumeration.NamingRuleEnum;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRule;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameter;
import com.example.demo.model.namingrule.biz.domain.namingRule.model.NamingRuleParameterSrValue;
import com.example.demo.model.namingrule.biz.service.NamingRuleService;
import com.example.demo.model.namingrule.biz.domain.namingRule.service.NamingRuleDomainService;
import com.example.demo.model.namingrule.biz.domain.namingRule.service.NamingRuleParametersSrValueDomainService;
import com.example.demo.model.namingrule.biz.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import xyz.erupt.annotation.config.Comment;
import xyz.erupt.jpa.dao.EruptDao;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class NamingRuleService  {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private NamingRuleDomainService namingRuleDomainService;

    @Resource
    private NamingRuleParametersSrValueDomainService namingRuleParametersSrValueDomainService;

    @Transactional
    @Comment("根据命名规则生成编码，参数为：1、命名规则编码，2、需生成的个数，3、变量表")
    public List<String> getNameCode(String ruleCode, int num, Map<String, String> variableMap) {
        NamingRule namingRule = namingRuleDomainService.getParametersByCode(ruleCode);
//      //获取按顺序号升序排列的编码规则参数List
        List<NamingRuleParameter> parameters = namingRule.getNamingRuleParameterList().stream().sorted((p1, p2) -> p1.getParameterSequence() - p2.getParameterSequence())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parameters) && num > 0) {
            List<String> nameCode = new ArrayList<>();
            for (int i = 0; i < num; i++) {
                // 由于sequence参数的依赖项会在整个生成过程的最后确定，为了避免多次重复生成，先使用占位符先插入各个sequence参数，
                // 并在生成过程中收集版本信息，最后使用收集到的版本信息对sequence参数统一进行替换
                // 此处先查询各个sequence参数的依赖信息进行整理，便于之后的收集
                List<NamingRuleParameter> sequenceParameters = parameters.stream().filter(it -> StringUtils.equals(it.getParameterType(), NamingRuleEnum.Enum.SR.name())).collect(Collectors.toList());
                Map<NamingRuleParameter, NamingRuleParameterSrValue> serialVersionMap = new LinkedHashMap<>();
                Map<NamingRuleParameter, List<String>> serialMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(sequenceParameters)) {
                    sequenceParameters.forEach(sequence -> {
                        NamingRuleParameterSrValue namingRuleParameterSrValue = new NamingRuleParameterSrValue();
                        namingRuleParameterSrValue.setSrParameter(sequence);
                        serialVersionMap.put(sequence, namingRuleParameterSrValue);
                        // 获取依赖参数
                        List<String> parameter = Arrays.stream( sequence.getDependParameters().split(","))
                                .collect(Collectors.toList());
                        serialMap.put(sequence, parameter);
                    });
                }
                //serialMap: 每个序号类型的参数，及其对应的依赖参数List
                //serialVersionMap: 每个序号类型的参数，及其在序号池中对应的一个版本数据行
                //parameters: 该命名规则下的所有参数List
                //variableMap: 变量映射表，当编码规则中有变量时，根据变量编码在该映射表中取变量参数值
                String name = namingRuleParametersSrValueDomainService.generatorNameCodeExceptSequence(serialMap, serialVersionMap, parameters, variableMap);
                // 最后对已插入的sequence参数的占位符进行统一替换
                List<String> sequenceNumber = namingRuleParametersSrValueDomainService.increaseSequenceNumber(serialVersionMap);
                String[] sequenceRegex = sequenceParameters.stream().map(it -> "<*" + it.getParameterName() + "*>").toArray(String[]::new);
                String[] finalSequence = sequenceNumber.toArray(new String[0]);
                nameCode.add(StringUtils.replaceEach(name, sequenceRegex, finalSequence));
            }
            return nameCode;
        } else {
            return null;
        }
    }
}
