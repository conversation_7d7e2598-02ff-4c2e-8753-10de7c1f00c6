# Structure Luckysheet 使用说明

## 功能概述

基于 Luckysheet 的 Structure 数据管理系统，提供类似 Excel 的编辑体验，支持：

- 表格形式展示 Structure 数据
- 在线编辑单元格内容
- 多条件查询和过滤
- 批量数据操作
- 数据导出功能
- 新增和删除记录

## 访问方式

1. 启动应用后，登录 Erupt 管理后台
2. 在菜单中找到 "Structure表格编辑" 选项
3. 点击进入 Luckysheet 编辑界面

或者直接访问：`http://localhost:8080/tpl/structure-luckysheet.html`

## 功能说明

### 查询功能
- **基础查询**：支持按唯一编号、安装件名称、Component名称、安装件类型、房间编号、Volume名称进行查询
- **高级查询**：支持按POI坐标、COG坐标、重心X标记等进行查询
- **日期范围查询**：支持按创建时间范围查询
- **模糊匹配**：文本字段支持模糊查询

### 编辑功能
- **单元格编辑**：双击单元格即可编辑
- **批量编辑**：支持复制粘贴等批量操作
- **数据验证**：自动验证数据格式和必填项
- **实时保存**：Ctrl+S 快速保存

### 数据操作
- **新增记录**：点击"新增行"按钮添加新记录
- **删除记录**：选中行后点击"删除选中"按钮
- **批量导入**：支持从剪贴板粘贴数据
- **数据导出**：支持导出为 Excel 格式

## 快捷键

- `Ctrl + S`：保存数据
- `Ctrl + F`：切换高级查询
- `Enter`：在查询框中按回车执行查询
- `双击单元格`：编辑单元格内容

## 字段说明

| 字段名 | 说明 | 是否必填 |
|--------|------|----------|
| ID | 系统自动生成的主键 | 否（系统生成） |
| 唯一编号 | Structure的唯一标识 | 是 |
| 安装件名称 | Structure的名称 | 是 |
| Component名称 | 关联的Component名称 | 否 |
| 房间编号 | 所属房间的编号 | 否 |
| Volume名称 | Volume名称 | 否 |
| 安装件类型 | 类型：设备、扁条、托架、支架、贯穿件 | 否 |
| POI坐标 | 三维空间坐标 | 否 |
| ROT坐标 | 旋转坐标 | 否 |
| ROU坐标 | 路由坐标 | 否 |
| COG坐标 | 重心坐标 | 否 |
| 重心X标记1 | 重心X标记1结构编码 | 否 |
| 重心X标记2 | 重心X标记2结构编码 | 否 |
| 创建时间 | 记录创建时间 | 否（系统生成） |
| 更新时间 | 记录更新时间 | 否（系统生成） |

## 注意事项

1. **数据安全**：编辑前建议先导出备份数据
2. **并发编辑**：多人同时编辑时注意数据冲突
3. **数据格式**：坐标字段请按照规定格式输入
4. **性能考虑**：大量数据时建议使用查询条件过滤
5. **浏览器兼容**：建议使用 Chrome、Firefox 等现代浏览器

## API 接口

### 数据查询
```
GET /api/structure-luckysheet/data
参数：code, name, componentName, structType, roomCode, volumeName, poi, cog, etc.
```

### 数据保存
```
POST /api/structure-luckysheet/save
Body: Luckysheet 格式的数据
```

### 获取类型列表
```
GET /api/structure-luckysheet/struct-types
GET /api/structure-luckysheet/room-codes
```

### 批量删除
```
DELETE /api/structure-luckysheet/batch-delete
Body: [id1, id2, id3, ...]
```

### 数据导出
```
GET /api/structure-luckysheet/export
参数：与查询接口相同
```

## 技术架构

- **前端**：Luckysheet + Vue.js + Element UI
- **后端**：Spring Boot + Erupt Framework
- **数据库**：MySQL + JPA
- **数据转换**：自定义 DTO 和 Service 层

## 扩展开发

如需扩展功能，可以：

1. 修改 `StructureLuckysheetController` 添加新的 API 接口
2. 更新 `StructureLuckysheetService` 实现新的业务逻辑
3. 在前端页面添加新的 UI 组件和交互功能
4. 扩展 `StructureLuckysheetDto` 支持更多字段

## 故障排除

### 常见问题

1. **页面无法访问**：检查应用是否正常启动，端口是否被占用
2. **数据加载失败**：检查数据库连接和表结构
3. **保存失败**：检查数据格式和必填项验证
4. **查询无结果**：检查查询条件和数据库中的数据

### 日志查看

应用日志位置：`logs/application.log`
关键日志关键字：`StructureLuckysheet`

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持基础的 CRUD 操作
- 集成 Luckysheet 表格编辑
- 实现多条件查询和过滤
- 支持数据导入导出
