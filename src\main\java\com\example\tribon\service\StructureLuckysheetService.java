package com.example.tribon.service;

import com.example.tribon.domain.model.Structure;
import com.example.tribon.dto.StructureLuckysheetDto;
import org.springframework.stereotype.Service;
import xyz.erupt.jpa.dao.EruptDao;
import xyz.erupt.jpa.dao.EruptJpaDao;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Structure Luckysheet 服务类
 * 处理 Structure 实体与 Luckysheet 数据之间的转换
 */
@Service
public class StructureLuckysheetService {

    @Resource
    private EruptDao eruptDao;

    @Resource
    private EruptJpaDao eruptJpaDao;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 将 Structure 列表转换为 Luckysheet 格式
     */
    public Map<String, Object> convertToLuckysheetFormat(List<Structure> structures) {
        Map<String, Object> result = new HashMap<>();
        
        // 创建表头
        List<Map<String, Object>> celldata = new ArrayList<>();

        // 表头行
        List<Object> headers = Arrays.asList(
            "ID", "唯一编号", "安装件名称", "Component名称", "房间编号", "Volume名称",
            "安装件类型", "POI坐标", "ROT坐标", "ROU坐标", "COG坐标",
            "重心X标记1", "重心X标记2", "创建时间", "更新时间"
        );

        // 添加表头到 celldata
        for (int col = 0; col < headers.size(); col++) {
            Map<String, Object> cell = new HashMap<>();
            cell.put("r", 0);
            cell.put("c", col);
            Map<String, Object> v = new HashMap<>();
            v.put("v", headers.get(col));
            v.put("m", headers.get(col));
            cell.put("v", v);
            celldata.add(cell);
        }
        
        // 添加数据行
        for (int row = 0; row < structures.size(); row++) {
            Structure structure = structures.get(row);
            List<Object> rowData = Arrays.asList(
                structure.getId(),
                structure.getCode(),
                structure.getName(),
                structure.getComponentName(),
                structure.getRoomCode(),
                structure.getVolumeName(),
                structure.getStructType(),
                structure.getPoi(),
                structure.getRot(),
                structure.getRou(),
                structure.getCog(),
                structure.getCogXMark1StructCode(),
                structure.getCogXMark2StructCode()
//                structure.getCreateTime() != null ? dateFormat.format(structure.getCreateTime()) : "",
//                structure.getUpdateTime() != null ? dateFormat.format(structure.getUpdateTime()) : ""
            );
            
            for (int col = 0; col < rowData.size(); col++) {
                Map<String, Object> cell = new HashMap<>();
                cell.put("r", row + 1);
                cell.put("c", col);
                Map<String, Object> v = new HashMap<>();
                Object value = rowData.get(col);
                v.put("v", value != null ? value : "");
                v.put("m", value != null ? value.toString() : "");
                cell.put("v", v);
                celldata.add(cell);
            }
        }
        
        // 构建 Luckysheet 工作表数据
        Map<String, Object> sheet = new HashMap<>();
        sheet.put("name", "Structure数据");
        sheet.put("index", "0");
        sheet.put("order", 0);
        sheet.put("status", 1);
        sheet.put("row", Math.max(structures.size() + 1, 20));
        sheet.put("column", headers.size());
        sheet.put("celldata", celldata);
        
        // 设置列宽
        Map<String, Object> config = new HashMap<>();
        Map<String, Integer> columnlen = new HashMap<>();
        for (int i = 0; i < headers.size(); i++) {
            columnlen.put(String.valueOf(i), 120);
        }
        config.put("columnlen", columnlen);
        sheet.put("config", config);
        
        result.put("sheets", Arrays.asList(sheet));
        result.put("title", "Structure数据管理");
        result.put("userInfo", new HashMap<>());
        
        return result;
    }

    /**
     * 从 Luckysheet 格式转换为 StructureLuckysheetDto 列表
     */
    public List<StructureLuckysheetDto> convertFromLuckysheetFormat(Map<String, Object> luckysheetData) {
        List<StructureLuckysheetDto> result = new ArrayList<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> sheets = (List<Map<String, Object>>) luckysheetData.get("sheets");
            
            if (sheets == null || sheets.isEmpty()) {
                return result;
            }
            
            Map<String, Object> sheet = sheets.get(0);
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> celldata = (List<Map<String, Object>>) sheet.get("celldata");

            if (celldata == null) {
                return result;
            }

            // 构建行列数据映射
            Map<String, String> cellMap = new HashMap<>();
            for (Map<String, Object> cell : celldata) {
                Long r = (Long) cell.get("r");
                Long c = (Long) cell.get("c");
                @SuppressWarnings("unchecked")
                Map<String, Object> v = (Map<String, Object>) cell.get("v");
                if (v != null && v.get("v") != null) {
                    cellMap.put(r + "," + c, v.get("v").toString());
                }
            }
            
            // 获取数据行数
            Long rowCount = (Long) sheet.get("row");
            if (rowCount == null) rowCount = 20L;
            
            // 从第二行开始处理数据（第一行是表头）
            for (int row = 1; row < rowCount; row++) {
                String idStr = cellMap.get(row + ",0");
                if (idStr == null || idStr.trim().isEmpty()) {
                    continue; // 跳过空行
                }
                
                StructureLuckysheetDto dto = new StructureLuckysheetDto();
                dto.setRowIndex(row);
                
                try {
                    dto.setId(Long.parseLong(idStr));
                } catch (NumberFormatException e) {
                    dto.setIsNew(true);
                }
                
                dto.setCode(cellMap.get(row + ",1"));
                dto.setName(cellMap.get(row + ",2"));
                dto.setComponentName(cellMap.get(row + ",3"));
                dto.setRoomCode(cellMap.get(row + ",4"));
                dto.setVolumeName(cellMap.get(row + ",5"));
                dto.setStructType(cellMap.get(row + ",6"));
                dto.setPoi(cellMap.get(row + ",7"));
                dto.setRot(cellMap.get(row + ",8"));
                dto.setRou(cellMap.get(row + ",9"));
                dto.setCog(cellMap.get(row + ",10"));
                dto.setCogXMark1StructCode(cellMap.get(row + ",11"));
                dto.setCogXMark2StructCode(cellMap.get(row + ",12"));
                
                result.add(dto);
            }
            
        } catch (Exception e) {
            throw new RuntimeException("解析 Luckysheet 数据失败: " + e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 保存 Structure 数据
     */
    public int saveStructures(List<StructureLuckysheetDto> dtos) {
        int updatedCount = 0;
        
        for (StructureLuckysheetDto dto : dtos) {
            try {
                if (dto.getIsNew() != null && dto.getIsNew()) {
                    // 新增记录
                    Structure structure = new Structure();
                    copyDtoToEntity(dto, structure);
                    eruptJpaDao.addEntity(StructureLuckysheetService.class, structure);
                    updatedCount++;
                } else if (dto.getId() != null) {
                    // 更新记录
                    Structure structure = eruptDao.queryEntity(Structure.class, "id = :id",
                        new HashMap<String, Object>() {{ put("id", dto.getId()); }});
                    if (structure != null) {
                        copyDtoToEntity(dto, structure);
                        eruptJpaDao.editEntity(StructureLuckysheetService.class, structure);
                        updatedCount++;
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException("保存记录失败 (行: " + dto.getRowIndex() + "): " + e.getMessage(), e);
            }
        }
        
        return updatedCount;
    }

    /**
     * 批量删除
     */
    public int batchDelete(List<Long> ids) {
        int deletedCount = 0;
        for (Long id : ids) {
            Structure structure = eruptDao.queryEntity(Structure.class, "id = :id",
                new HashMap<String, Object>() {{ put("id", id); }});
            if (structure != null) {
                eruptJpaDao.removeEntity(StructureLuckysheetService.class, structure);
                deletedCount++;
            }
        }
        return deletedCount;
    }

    /**
     * 导出到 Excel 格式
     */
    public Map<String, Object> exportToExcel(List<Structure> structures) {
        // 这里可以实现具体的 Excel 导出逻辑
        // 暂时返回 Luckysheet 格式，前端可以使用 Luckysheet 的导出功能
        return convertToLuckysheetFormat(structures);
    }

    /**
     * 将 DTO 数据复制到实体
     */
    private void copyDtoToEntity(StructureLuckysheetDto dto, Structure structure) {
        if (dto.getCode() != null) structure.setCode(dto.getCode());
        if (dto.getName() != null) structure.setName(dto.getName());
        if (dto.getComponentName() != null) structure.setComponentName(dto.getComponentName());
        if (dto.getRoomCode() != null) structure.setRoomCode(dto.getRoomCode());
        if (dto.getVolumeName() != null) structure.setVolumeName(dto.getVolumeName());
        if (dto.getStructType() != null) structure.setStructType(dto.getStructType());
        if (dto.getPoi() != null) structure.setPoi(dto.getPoi());
        if (dto.getRot() != null) structure.setRot(dto.getRot());
        if (dto.getRou() != null) structure.setRou(dto.getRou());
        if (dto.getCog() != null) structure.setCog(dto.getCog());
        if (dto.getCogXMark1StructCode() != null) structure.setCogXMark1StructCode(dto.getCogXMark1StructCode());
        if (dto.getCogXMark2StructCode() != null) structure.setCogXMark2StructCode(dto.getCogXMark2StructCode());
    }
}
