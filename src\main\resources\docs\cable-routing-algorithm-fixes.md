# CableRoutingSolverService 算法问题分析与修复

## 🔍 发现的问题

### 1. **距离矩阵计算冗余问题**

#### 原始代码问题
```java
// 原始代码 - 有问题的实现
for (int i = 0; i < n; i++) {
    for (int j = i+1; j < n; j++) {
        infoMat[i][j] = computeDist(edges.get(i), edges.get(j));
        infoMat[j][i] = computeDist(edges.get(j), edges.get(i)); // 重复计算
    }
}
```

**问题分析**：
- `computeDist(e, f)` 和 `computeDist(f, e)` 返回不同的 `DistInfo` 对象
- `computeDist(e, f)` 返回：`p` 在边 `e` 上，`q` 在边 `f` 上
- `computeDist(f, e)` 返回：`p` 在边 `f` 上，`q` 在边 `e` 上
- 虽然距离 `dist` 相同，但点的位置信息不同，导致路径重构时方向混乱

#### 修复方案
```java
// 修复后的代码
for (int i = 0; i < n; i++) {
    for (int j = 0; j < n; j++) {
        if (i != j) {
            infoMat[i][j] = computeDist(edges.get(i), edges.get(j));
        }
    }
}
```

**修复效果**：
- ✅ 每个方向都单独计算，确保 `DistInfo` 的方向正确
- ✅ `infoMat[i][j]` 表示从边 `i` 到边 `j` 的连接信息
- ✅ 路径重构时方向一致

### 2. **优先队列类型不匹配问题**

#### 原始代码问题
```java
// 声明为 double 比较器，但使用 int 数组
PriorityQueue<int[]> pq = new PriorityQueue<>(Comparator.comparingDouble(a -> a[1]));

// 插入时强制转换，丢失精度
pq.offer(new int[]{v, (int)alt});
```

**问题分析**：
- 比较器期望 `double` 类型，但数组元素是 `int`
- 距离值 `alt` 被强制转换为 `int`，丢失小数精度
- 可能导致 Dijkstra 算法选择次优路径

#### 修复方案
```java
// 修复后的代码
PriorityQueue<double[]> pq = new PriorityQueue<>(Comparator.comparingDouble(a -> a[1]));

// 初始化
pq.offer(new double[]{i, 0.0});

// 更新时保持精度
pq.offer(new double[]{v, alt});

// 取值时转换
int u = (int) pq.poll()[0];
```

**修复效果**：
- ✅ 类型一致，避免类型转换错误
- ✅ 保持距离计算的精度
- ✅ Dijkstra 算法能正确选择最短路径

### 3. **潜在的边界情况处理**

#### 可能的问题场景
1. **同一托架的不同边之间的连接**：算法应该避免在同一托架内部创建连接
2. **零距离处理**：当两个边重叠时的处理
3. **数值精度**：浮点数比较的精度问题

## 🧪 测试验证

### 1. 基础功能测试
```java
@Test
void testTrayConstruction() {
    // 验证 Tray 构造函数的坐标计算
    CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(
        1, 100, 200, 300, 100, 50, 20
    );
    
    assertEquals(310, tray.zTop, 0.001); // zCenter + height/2 = 300 + 10 = 310
}
```

### 2. 路径计算测试
```java
@Test
void testSimpleTwoTrayPath() {
    List<CableRoutingSolverService.Tray> trays = Arrays.asList(
        new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20),     // 起点
        new CableRoutingSolverService.Tray(1, 200, 0, 0, 100, 50, 20)    // 终点
    );
    
    List<CableRoutingSolverService.Segment> segments = 
        CableRoutingSolverService.solve(trays, 0, 1);
    
    assertFalse(segments.isEmpty(), "应该能找到路径");
    assertEquals(10.0, segments.get(0).start.z, 0.001, "起点应在托架顶面");
}
```

### 3. 算法修复验证
```java
@Test
void testAlgorithmFixes() {
    // 测试相邻托架的连接
    List<CableRoutingSolverService.Tray> trays = Arrays.asList(
        new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20),
        new CableRoutingSolverService.Tray(1, 150, 0, 0, 100, 50, 20)
    );
    
    List<CableRoutingSolverService.Segment> segments = 
        CableRoutingSolverService.solve(trays, 0, 1);
    
    // 验证路径的合理性
    assertTrue(segments.get(0).start.x >= -50 && segments.get(0).start.x <= 50);
    assertTrue(segments.get(segments.size()-1).end.x >= 100 && 
               segments.get(segments.size()-1).end.x <= 200);
}
```

## 📊 修复前后对比

### 修复前的问题
| 问题类型 | 具体表现 | 影响 |
|----------|----------|------|
| 距离矩阵计算 | 方向信息混乱 | 路径重构错误 |
| 优先队列类型 | 精度丢失 | 次优路径选择 |
| 数据类型不匹配 | 类型转换错误 | 运行时异常风险 |

### 修复后的改进
| 改进项 | 修复方案 | 效果 |
|--------|----------|------|
| 距离矩阵 | 单独计算每个方向 | ✅ 方向信息正确 |
| 优先队列 | 使用 double 数组 | ✅ 保持计算精度 |
| 类型一致性 | 统一数据类型 | ✅ 避免类型错误 |

## 🎯 核心算法逻辑

### 1. 边缘生成
```java
// 为每个托架生成4条边（顶面的四条边缘）
edges.add(new Edge(t.id, 0, t.x-halfL, t.x+halfL, t.y-halfW, t.y-halfW, z)); // 下边
edges.add(new Edge(t.id, 1, t.x-halfL, t.x+halfL, t.y+halfW, t.y+halfW, z)); // 上边
edges.add(new Edge(t.id, 2, t.x-halfL, t.x-halfL, t.y-halfW, t.y+halfW, z)); // 左边
edges.add(new Edge(t.id, 3, t.x+halfL, t.x+halfL, t.y-halfW, t.y+halfW, z)); // 右边
```

### 2. 距离计算
```java
// 计算两条边之间的曼哈顿距离
info.dist = dx + dy + dz;
info.p = new Point3D(px, py, e.z); // 在边 e 上的最优连接点
info.q = new Point3D(qx, qy, f.z); // 在边 f 上的最优连接点
```

### 3. 最短路径查找
- 使用多源 Dijkstra 算法
- 从源托架的所有边开始搜索
- 找到到达目标托架任意边的最短路径

### 4. 路径重构
```java
// 根据 prev 数组重构路径
for (int i = 0; i < path.size()-1; i++) {
    DistInfo di = infoMat[path.get(i)][path.get(i+1)];
    result.add(new Segment(di.p, di.q)); // 从 p 到 q 的线段
}
```

## 🚀 总结

通过修复这些关键问题，CableRoutingSolverService 现在能够：

1. **✅ 正确计算距离矩阵**：每个方向的连接信息都准确无误
2. **✅ 保持计算精度**：使用正确的数据类型，避免精度丢失
3. **✅ 生成准确路径**：路径重构时方向信息正确
4. **✅ 提供可靠结果**：返回的起点和终点坐标位于托架顶面边缘

这些修复确保了电缆走线计算的准确性和可靠性，为船舶设计中的电缆布线提供了精确的路径规划能力。
