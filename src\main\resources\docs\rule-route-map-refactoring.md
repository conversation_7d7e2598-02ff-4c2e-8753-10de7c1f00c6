# 路线图规则重构总结

## 🎯 重构目标

将路线图计算规则从代码硬编码移入到数据库模型中，通过 Erupt 框架提供前端配置界面，实现规则的动态管理。

## ✅ 已完成的重构内容

### 1. 数据库模型增强

#### RuleRouteMap 主表
```java
@Entity
@Table(name = "tribon_rule_route_map")
public class RuleRouteMap extends BaseModel {
    private String ruleName;                    // 规则名称
    private String description;                 // 规则描述
    private ConnectionStrategy connectionStrategy; // 连接策略枚举
    private Boolean enableRoomRule;             // 启用房间规则
    private Boolean enableThroughPieceRule;     // 启用贯穿件规则
    private Integer maxConnections;             // 最大连接数
    private Set<RuleRouteMapConnectionPriority> ruleRouteMapConnectionPrioritys; // 连接优先级规则
}
```

#### RuleRouteMapConnectionPriority 详细规则表
```java
@Entity
@Table(name = "tribon_rule_route_map_conn_priority")
public class RuleRouteMapConnectionPriority extends BaseModel {
    private String sourceStructTypeCode;        // 源结构类型代码
    private String targetStructTypeCode;        // 目标结构类型代码
    private Integer priority;                   // 连接优先级
    private Double maxDistance;                 // 最大距离
    private Integer connectionLimit;            // 连接数量限制
    private ConnectionDirection connectionDirection; // 连接方向
    private Boolean enabled;                    // 是否启用
    private String ruleDescription;             // 规则描述
}
```

#### 枚举定义
```java
// 连接策略枚举
public enum ConnectionStrategy {
    NEAREST_FIRST,      // 最近优先
    PRIORITY_FIRST,     // 优先级优先
    BALANCED,           // 平衡策略
    MINIMUM_SPANNING    // 最小生成树
}

// 连接方向枚举
public enum ConnectionDirection {
    BIDIRECTIONAL,    // 双向连接
    UNIDIRECTIONAL,   // 单向连接（仅从源到目标）
    REVERSE_ONLY      // 反向连接（仅从目标到源）
}
```

### 2. 规则转换服务

#### RuleRouteMapConverterService
- **功能**：将数据库模型转换为算法使用的规则对象
- **核心方法**：
  - `convertToAdvancedRuleLibrary()` - 转换规则
  - `getDefaultRuleLibrary()` - 获取默认规则
  - `createAndSaveDefaultRule()` - 创建默认规则
  - `validateRule()` - 验证规则有效性

### 3. 算法规则库增强

#### AdvancedRuleLibrary 扩展
```java
public static class AdvancedRuleLibrary {
    private String connectionStrategy;                              // 连接策略
    private Boolean enableRoomRule;                                // 房间规则开关
    private Boolean enableThroughPieceRule;                       // 贯穿件规则开关
    private Integer maxConnections;                                // 最大连接数
    private Map<String, Map<String, Integer>> connectionPriorityRules;     // 优先级规则
    private Map<String, Map<String, ConnectionDirection>> connectionDirections; // 方向规则
    private Map<String, Map<String, Double>> maxDistanceRules;     // 距离规则
    private Map<String, Map<String, Integer>> connectionLimits;    // 数量限制规则
    private Map<String, Set<String>> exclusiveConnections;         // 互斥规则
    private Map<String, Set<String>> requiredConnections;          // 必需规则
    private List<ConditionalRule> conditionalRules;               // 条件规则
}
```

### 4. 前端配置界面

通过 Erupt 注解自动生成的管理界面：

#### 主规则配置
- 规则名称和描述
- 连接策略选择（下拉框）
- 房间规则和贯穿件规则开关
- 最大连接数设置

#### 详细规则配置
- 源结构类型和目标结构类型
- 连接优先级（数字输入）
- 最大距离限制
- 连接数量限制
- 连接方向选择
- 规则启用/禁用开关

## 🔧 使用方法

### 1. 前端配置规则
1. 登录 Erupt 管理后台
2. 找到"安装件路线图规则"菜单
3. 创建新规则或编辑现有规则
4. 配置连接优先级规则

### 2. 代码中使用规则
```java
// 方式1：使用指定规则ID
RuleRouteMapConverterService converterService = new RuleRouteMapConverterService();
AdvancedRuleLibrary ruleLibrary = converterService.convertToAdvancedRuleLibrary(ruleId);

// 方式2：使用默认规则
AdvancedRuleLibrary ruleLibrary = converterService.getDefaultRuleLibrary();

// 方式3：按名称查找规则
RuleRouteMap rule = converterService.findRuleByName("生产环境规则");
AdvancedRuleLibrary ruleLibrary = converterService.convertToAdvancedRuleLibrary(rule);
```

### 3. 算法调用
```java
// 使用数据库规则计算路线
GenerateRouteMapResponseDto result = RouteMapService.findConnectionsAdvanced(
    nodes, ruleLibrary, ConnectionStrategy.BALANCED);
```

## 📊 规则配置示例

### 默认规则配置
```
规则名称: 默认规则
连接策略: 平衡策略
房间规则: 启用
贯穿件规则: 启用
最大连接数: 5

连接优先级规则:
- 设备 → 扁条: 优先级1, 最大距离500, 连接限制2
- 设备 → 托架: 优先级2, 最大距离800, 连接限制2
- 设备 → 支架: 优先级3, 最大距离1000, 连接限制1
- 扁条 → 设备: 优先级1, 最大距离500, 连接限制3
- 扁条 → 托架: 优先级2, 最大距离600, 连接限制2
- 扁条 → 贯穿件: 优先级3, 最大距离1200, 连接限制1
```

## 🔍 规则验证

### 自动验证规则
- 规则名称不能为空
- 最大连接数必须大于0
- 至少需要一条连接优先级规则
- 源/目标结构类型不能为空
- 优先级必须大于0
- 距离和数量限制必须为正数

### 规则冲突检测
- 互斥规则与必需规则冲突检测
- 方向性规则一致性检查
- 优先级规则完整性验证

## 🚀 扩展功能

### 1. 规则模板
- 预定义行业规则模板
- 快速应用常用配置
- 规则导入导出功能

### 2. 规则版本管理
- 规则变更历史记录
- 版本回滚功能
- 规则对比功能

### 3. 动态规则更新
- 热更新规则配置
- 规则生效时间控制
- A/B测试支持

## 📈 性能优化

### 1. 规则缓存
- 内存缓存常用规则
- 规则变更时自动刷新缓存
- 分布式缓存支持

### 2. 规则预编译
- 规则保存时预编译验证
- 运行时快速加载
- 规则索引优化

## 🔧 故障排除

### 常见问题
1. **规则不生效**：检查规则是否启用，优先级是否正确
2. **连接数量异常**：检查连接限制和最大连接数设置
3. **距离约束失效**：检查最大距离设置是否合理
4. **方向性错误**：检查连接方向配置

### 调试方法
1. 查看规则转换日志
2. 验证规则库内容
3. 检查算法输入参数
4. 分析连接结果

## 📋 后续计划

### 短期目标
- [ ] 完善规则验证逻辑
- [ ] 添加规则使用统计
- [ ] 实现规则导入导出
- [ ] 优化前端配置界面

### 长期目标
- [ ] 支持复杂条件规则
- [ ] 实现规则学习功能
- [ ] 添加可视化规则编辑器
- [ ] 集成机器学习优化

## 🎯 总结

通过这次重构，我们实现了：

1. **✅ 规则数据库化**：将硬编码规则移入数据库
2. **✅ 前端可配置**：通过 Erupt 提供用户友好的配置界面
3. **✅ 规则验证**：完善的规则验证和错误检测机制
4. **✅ 向后兼容**：保持原有算法接口不变
5. **✅ 扩展性强**：支持未来更复杂的规则需求

这个重构为路线图计算提供了更灵活、可维护的规则管理方案，大大提升了系统的可配置性和用户体验。
