package com.example.tribon.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;

@Component
@Slf4j
public class TribonService {
    public void existRemoteMode(){
        try (Socket socket = new Socket("192.168.116.142", 8000)) {
            PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
            BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            // 构造请求参数
            out.println("999"); // 发送消息
            StringBuilder responseBuilder = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                responseBuilder.append(line).append(System.lineSeparator());
                if (line.endsWith("%EOR%")) {
                    break;
                }
            }
            System.out.println("服务器回复: " + responseBuilder.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
