package com.example.tribon.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CableRoutingSolverService 测试类
 * 验证电缆走线最短路径计算的正确性
 */
@SpringBootTest
public class CableRoutingSolverServiceTest {

    @Test
    void testTrayConstruction() {
        // 测试Tray构造函数的坐标计算
        System.out.println("=== 测试Tray构造函数 ===");
        
        // 创建一个托架：中心坐标(100, 200, 300)，尺寸100x50x20
        CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(
            1, // ID
            100, 200, 300, // x, y, zCenter
            100, 50, 20    // length, width, height
        );
        
        System.out.println("输入参数:");
        System.out.println("  中心坐标: (100, 200, 300)");
        System.out.println("  尺寸: 长100, 宽50, 高20");
        
        System.out.println("Tray对象属性:");
        System.out.println("  x: " + tray.x);
        System.out.println("  y: " + tray.y);
        System.out.println("  zTop: " + tray.zTop + " (应该是 300 + 20/2 = 310)");
        System.out.println("  length: " + tray.length);
        System.out.println("  width: " + tray.width);
        
        // 验证计算结果
        assertEquals(100, tray.x, 0.001);
        assertEquals(200, tray.y, 0.001);
        assertEquals(310, tray.zTop, 0.001); // zCenter + height/2 = 300 + 10 = 310
        assertEquals(100, tray.length, 0.001);
        assertEquals(50, tray.width, 0.001);
    }

    @Test
    void testEdgeGeneration() {
        // 测试边缘生成逻辑
        System.out.println("\n=== 测试边缘生成 ===");
        
        // 创建一个简单的托架
        CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(
            1, 0, 0, 0, // ID=1, 中心坐标(0,0,0)
            100, 50, 20 // 长100, 宽50, 高20
        );
        
        System.out.println("托架信息:");
        System.out.println("  中心: (0, 0, 0)");
        System.out.println("  尺寸: 100x50x20");
        System.out.println("  zTop: " + tray.zTop);
        
        // 手动计算边缘坐标（模拟solve方法中的逻辑）
        double halfL = tray.length / 2; // 50
        double halfW = tray.width / 2;  // 25
        double z = tray.zTop;           // 10
        
        System.out.println("计算的边缘:");
        System.out.println("  halfL: " + halfL + ", halfW: " + halfW);
        
        // 四条边的坐标
        System.out.println("  边0 (下边): x1=" + (tray.x-halfL) + ", x2=" + (tray.x+halfL) + 
                          ", y1=" + (tray.y-halfW) + ", y2=" + (tray.y-halfW) + ", z=" + z);
        System.out.println("  边1 (上边): x1=" + (tray.x-halfL) + ", x2=" + (tray.x+halfL) + 
                          ", y1=" + (tray.y+halfW) + ", y2=" + (tray.y+halfW) + ", z=" + z);
        System.out.println("  边2 (左边): x1=" + (tray.x-halfL) + ", x2=" + (tray.x-halfL) + 
                          ", y1=" + (tray.y-halfW) + ", y2=" + (tray.y+halfW) + ", z=" + z);
        System.out.println("  边3 (右边): x1=" + (tray.x+halfL) + ", x2=" + (tray.x+halfL) + 
                          ", y1=" + (tray.y-halfW) + ", y2=" + (tray.y+halfW) + ", z=" + z);
        
        // 预期的边缘坐标
        // 边0: (-50, 50, -25, -25, 10) - 下边
        // 边1: (-50, 50, 25, 25, 10)   - 上边  
        // 边2: (-50, -50, -25, 25, 10) - 左边
        // 边3: (50, 50, -25, 25, 10)   - 右边
    }

    @Test
    void testSimpleTwoTrayPath() {
        // 测试两个托架之间的路径计算
        System.out.println("\n=== 测试两托架路径计算 ===");
        
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20),     // 起点托架
            new CableRoutingSolverService.Tray(1, 200, 0, 0, 100, 50, 20)    // 终点托架
        );
        
        System.out.println("托架配置:");
        System.out.println("  起点托架: 中心(0,0,0), 尺寸100x50x20, zTop=" + trays.get(0).zTop);
        System.out.println("  终点托架: 中心(200,0,0), 尺寸100x50x20, zTop=" + trays.get(1).zTop);
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        System.out.println("计算结果:");
        System.out.println("  路径段数: " + segments.size());
        
        if (!segments.isEmpty()) {
            for (int i = 0; i < segments.size(); i++) {
                CableRoutingSolverService.Segment seg = segments.get(i);
                System.out.println("  段" + i + ": " + seg.start + " -> " + seg.end);
            }
            
            // 验证起点和终点
            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);
            
            System.out.println("最终路径:");
            System.out.println("  起点: " + firstSeg.start);
            System.out.println("  终点: " + lastSeg.end);
            
            // 验证Z坐标应该是托架的顶面
            assertEquals(10.0, firstSeg.start.z, 0.001, "起点Z坐标应该在托架顶面");
            assertEquals(10.0, lastSeg.end.z, 0.001, "终点Z坐标应该在托架顶面");
            
        } else {
            System.out.println("  未找到路径！");
            fail("应该能找到路径");
        }
    }

    @Test
    void testVerticallyStackedTrays() {
        // 测试垂直堆叠的托架
        System.out.println("\n=== 测试垂直堆叠托架 ===");
        
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20),     // 下层托架，zTop=10
            new CableRoutingSolverService.Tray(1, 0, 0, 50, 100, 50, 20)     // 上层托架，zTop=60
        );
        
        System.out.println("托架配置:");
        System.out.println("  下层托架: 中心(0,0,0), zTop=" + trays.get(0).zTop);
        System.out.println("  上层托架: 中心(0,0,50), zTop=" + trays.get(1).zTop);
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        System.out.println("计算结果:");
        if (!segments.isEmpty()) {
            for (CableRoutingSolverService.Segment seg : segments) {
                System.out.println("  " + seg);
            }
            
            // 验证Z坐标差异
            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);
            
            System.out.println("Z坐标验证:");
            System.out.println("  起点Z: " + firstSeg.start.z + " (应该是10)");
            System.out.println("  终点Z: " + lastSeg.end.z + " (应该是60)");
            
        } else {
            System.out.println("  未找到路径");
        }
    }

    @Test
    void testCoordinateSystemConsistency() {
        // 测试坐标系统一致性
        System.out.println("\n=== 测试坐标系统一致性 ===");
        
        // 模拟StructRestService中的调用
        double[] startCoords = {100, 200, 300}; // 模拟解析的COG坐标
        double[] endCoords = {400, 500, 600};
        
        // 模拟Component尺寸
        double startLength = 120, startWidth = 60, startHeight = 25;
        double endLength = 100, endWidth = 50, endHeight = 20;
        
        List<CableRoutingSolverService.Tray> trays = Arrays.asList(
            new CableRoutingSolverService.Tray(0, startCoords[0], startCoords[1], startCoords[2], 
                                             startLength, startWidth, startHeight),
            new CableRoutingSolverService.Tray(1, endCoords[0], endCoords[1], endCoords[2], 
                                             endLength, endWidth, endHeight)
        );
        
        System.out.println("输入坐标:");
        System.out.println("  起点COG: (" + startCoords[0] + ", " + startCoords[1] + ", " + startCoords[2] + ")");
        System.out.println("  终点COG: (" + endCoords[0] + ", " + endCoords[1] + ", " + endCoords[2] + ")");
        
        System.out.println("Tray计算结果:");
        System.out.println("  起点zTop: " + trays.get(0).zTop + " (应该是 " + (startCoords[2] + startHeight/2) + ")");
        System.out.println("  终点zTop: " + trays.get(1).zTop + " (应该是 " + (endCoords[2] + endHeight/2) + ")");
        
        List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
        
        if (!segments.isEmpty()) {
            CableRoutingSolverService.Segment firstSeg = segments.get(0);
            CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);
            
            System.out.println("计算的路径端点:");
            System.out.println("  起点: " + firstSeg.start);
            System.out.println("  终点: " + lastSeg.end);
            
            // 验证Z坐标是否正确（应该在托架顶面）
            double expectedStartZ = startCoords[2] + startHeight / 2;
            double expectedEndZ = endCoords[2] + endHeight / 2;
            
            assertEquals(expectedStartZ, firstSeg.start.z, 0.001, "起点Z坐标不正确");
            assertEquals(expectedEndZ, lastSeg.end.z, 0.001, "终点Z坐标不正确");
            
            System.out.println("✅ 坐标系统一致性验证通过");
        } else {
            fail("未找到路径");
        }
    }
}
