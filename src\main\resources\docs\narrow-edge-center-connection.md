# 托架窄边中心点连接实现

## 🎯 需求背景

在船舶电缆走线设计中，托架的窄边（宽度方向的边）通常只允许电缆从中心点出入，而不是沿整条边连接。这是基于以下工程考虑：

1. **结构强度**：窄边的中心点通常是结构最强的位置
2. **安装便利性**：中心点连接便于电缆的固定和维护
3. **美观性**：中心点连接使电缆布局更加整齐
4. **标准化**：符合船舶设计的标准规范

## 🔧 实现方案

### 1. 边缘类型定义（基于长度判断）

```java
/**
 * 托架顶面的四条边：
 * 根据边的实际长度判断：长边允许沿整条边连接，窄边只允许在中心点连接
 */
public Edge[] getTopEdges() {
    Point3D[] vertices = getTopVertices();

    // 计算四条边的长度
    double edge0Length = distance(vertices[0], vertices[1]); // 下边：左下 → 右下
    double edge1Length = distance(vertices[2], vertices[3]); // 上边：右上 → 左上
    double edge2Length = distance(vertices[3], vertices[0]); // 左边：左上 → 左下
    double edge3Length = distance(vertices[1], vertices[2]); // 右边：右下 → 右上

    // 判断哪些是长边，哪些是窄边
    boolean edge0IsLong = edge0Length >= edge2Length; // 下边是否比左边长
    boolean edge1IsLong = edge1Length >= edge2Length; // 上边是否比左边长
    boolean edge2IsLong = edge2Length >= edge0Length; // 左边是否比下边长
    boolean edge3IsLong = edge3Length >= edge0Length; // 右边是否比下边长

    Edge[] edges = new Edge[4];

    // 根据长度判断每条边的类型
    for (int i = 0; i < 4; i++) {
        boolean isLong = (i == 0 && edge0IsLong) || (i == 1 && edge1IsLong) ||
                        (i == 2 && edge2IsLong) || (i == 3 && edge3IsLong);

        if (isLong) {
            // 长边：允许沿整条边连接
            edges[i] = new Edge(id, i, vertices[i], vertices[(i+1)%4]);
        } else {
            // 窄边：只在中心点连接
            Point3D center = new Point3D(
                (vertices[i].x + vertices[(i+1)%4].x) / 2,
                (vertices[i].y + vertices[(i+1)%4].y) / 2,
                (vertices[i].z + vertices[(i+1)%4].z) / 2
            );
            edges[i] = new Edge(id, i, center, center);
        }
    }

    return edges;
}

/**
 * 计算两点之间的欧几里得距离
 */
private double distance(Point3D p1, Point3D p2) {
    double dx = p1.x - p2.x;
    double dy = p1.y - p2.y;
    double dz = p1.z - p2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}
```

### 2. 几何计算

#### 托架顶点计算（考虑旋转）
```java
// 原始顶点（相对于托架中心，未旋转）
Point3D[] originalVertices = {
    new Point3D(-halfL, -halfW, height / 2), // 左下
    new Point3D(halfL, -halfW, height / 2),  // 右下
    new Point3D(halfL, halfW, height / 2),   // 右上
    new Point3D(-halfL, halfW, height / 2)   // 左上
};

// 应用旋转变换并平移到世界坐标
for (int i = 0; i < 4; i++) {
    rotatedVertices[i] = applyRotation(originalVertices[i], rotX, rotY, rotZ);
    rotatedVertices[i] = new Point3D(
        rotatedVertices[i].x + x,
        rotatedVertices[i].y + y,
        rotatedVertices[i].z + (zTop - height / 2)
    );
}
```

#### 窄边中心点计算
```java
// 左边中心点 = (左上顶点 + 左下顶点) / 2
Point3D leftEdgeCenter = new Point3D(
    (vertices[3].x + vertices[0].x) / 2,
    (vertices[3].y + vertices[0].y) / 2,
    (vertices[3].z + vertices[0].z) / 2
);

// 右边中心点 = (右上顶点 + 右下顶点) / 2
Point3D rightEdgeCenter = new Point3D(
    (vertices[1].x + vertices[2].x) / 2,
    (vertices[1].y + vertices[2].y) / 2,
    (vertices[1].z + vertices[2].z) / 2
);
```

### 3. 距离计算优化

```java
private static DistInfo computeDist(Edge e, Edge f) {
    // 检查是否为点边（窄边中心点）
    boolean eIsPoint = (e.start.x == e.end.x && e.start.y == e.end.y && e.start.z == e.end.z);
    boolean fIsPoint = (f.start.x == f.end.x && f.start.y == f.end.y && f.start.z == f.end.z);
    
    Point3D pointOnE, pointOnF;
    
    if (eIsPoint && fIsPoint) {
        // 两个都是点，直接连接
        pointOnE = e.start;
        pointOnF = f.start;
    } else if (eIsPoint) {
        // e是点，f是边，找f上距离e最近的点
        pointOnE = e.start;
        pointOnF = f.getClosestPointTo(e.start);
    } else if (fIsPoint) {
        // f是点，e是边，找e上距离f最近的点
        pointOnF = f.start;
        pointOnE = e.getClosestPointTo(f.start);
    } else {
        // 两个都是边，找最优连接点
        // 使用边的中点作为参考
        Point3D eMid = new Point3D(
            (e.start.x + e.end.x) / 2,
            (e.start.y + e.end.y) / 2,
            (e.start.z + e.end.z) / 2
        );
        Point3D fMid = new Point3D(
            (f.start.x + f.end.x) / 2,
            (f.start.y + f.end.y) / 2,
            (f.start.z + f.end.z) / 2
        );
        
        pointOnE = e.getClosestPointTo(fMid);
        pointOnF = f.getClosestPointTo(eMid);
    }
    
    // 计算曼哈顿距离
    double dx = Math.abs(pointOnE.x - pointOnF.x);
    double dy = Math.abs(pointOnE.y - pointOnF.y);
    double dz = Math.abs(pointOnE.z - pointOnF.z);
    
    DistInfo info = new DistInfo();
    info.dist = dx + dy + dz;
    info.p = pointOnE;
    info.q = pointOnF;
    return info;
}
```

## 📊 边缘类型对比（基于长度判断）

### 修改前（所有边都是线段）
```
托架尺寸：100×50×20，中心(0,0,0)

边0（下边）: (-50,-25,10) → (50,-25,10)   [线段，长度100]
边1（上边）: (-50,25,10) → (50,25,10)     [线段，长度100]
边2（左边）: (-50,-25,10) → (-50,25,10)   [线段，长度50]
边3（右边）: (50,-25,10) → (50,25,10)     [线段，长度50]
```

### 修改后（基于长度判断）
```
托架尺寸：100×50×20，中心(0,0,0)

边0（下边）: (-50,-25,10) → (50,-25,10)   [线段，长度100] ✅ 长边，允许沿边连接
边1（上边）: (-50,25,10) → (50,25,10)     [线段，长度100] ✅ 长边，允许沿边连接
边2（左边）: (-50,0,10) → (-50,0,10)      [点] ✅ 窄边，只在中心点连接
边3（右边）: (50,0,10) → (50,0,10)        [点] ✅ 窄边，只在中心点连接
```

### 不同尺寸托架的处理

#### 1. 长方形托架（120×60×20）
```
边0（下边）: 长度120 → [线段] ✅ 长边
边1（上边）: 长度120 → [线段] ✅ 长边
边2（左边）: 长度60 → [点] ✅ 窄边
边3（右边）: 长度60 → [点] ✅ 窄边
```

#### 2. 正方形托架（80×80×20）
```
边0（下边）: 长度80 → [线段] ✅ 所有边长度相等，都允许沿边连接
边1（上边）: 长度80 → [线段] ✅
边2（左边）: 长度80 → [线段] ✅
边3（右边）: 长度80 → [线段] ✅
```

#### 3. 窄长托架（30×100×20）
```
边0（下边）: 长度30 → [点] ✅ 窄边
边1（上边）: 长度30 → [点] ✅ 窄边
边2（左边）: 长度100 → [线段] ✅ 长边
边3（右边）: 长度100 → [线段] ✅ 长边
```

## 🧪 测试验证

### 1. 边缘类型识别测试
```java
@Test
void testEdgeTypeIdentification() {
    CableRoutingSolverService.Tray tray = new CableRoutingSolverService.Tray(0, 0, 0, 0, 100, 50, 20);
    CableRoutingSolverService.Edge[] edges = tray.getTopEdges();
    
    for (int i = 0; i < edges.length; i++) {
        CableRoutingSolverService.Edge edge = edges[i];
        boolean isPoint = (edge.start.x == edge.end.x && 
                          edge.start.y == edge.end.y && 
                          edge.start.z == edge.end.z);
        
        // 验证：长边应该是线段，窄边应该是点
        if (edge.side == 0 || edge.side == 1) {
            assertFalse(isPoint, "长边应该是线段，不是点");
        } else {
            assertTrue(isPoint, "窄边应该是点，不是线段");
        }
    }
}
```

### 2. 窄边中心点连接测试
```java
@Test
void testNarrowEdgeCenterConnection() {
    // 创建两个Y方向相邻的托架
    List<CableRoutingSolverService.Tray> trays = Arrays.asList(
        new CableRoutingSolverService.Tray(0, 0, 0, 0, 200, 50, 20),     // 起点
        new CableRoutingSolverService.Tray(1, 0, 100, 0, 200, 50, 20)    // 终点，Y偏移100
    );
    
    List<CableRoutingSolverService.Segment> segments = CableRoutingSolverService.solve(trays, 0, 1);
    
    // 验证连接点的合理性
    // 应该通过窄边（上下边）的中心点连接
    CableRoutingSolverService.Segment firstSeg = segments.get(0);
    CableRoutingSolverService.Segment lastSeg = segments.get(segments.size() - 1);
    
    assertTrue(Math.abs(firstSeg.start.y - 25) < 30, "起点应该接近起点托架的上边");
    assertTrue(Math.abs(lastSeg.end.y - 75) < 30, "终点应该接近终点托架的下边");
}
```

## 🎯 应用场景

### 1. 水平相邻托架
```
托架A: 中心(0,0,0), 尺寸200×50×20
托架B: 中心(300,0,0), 尺寸200×50×20

连接方式：
- 托架A的右边中心点 (100,0,10)
- 托架B的左边中心点 (200,0,10)
- 形成水平连接线段
```

### 2. 垂直相邻托架
```
托架A: 中心(0,0,0), 尺寸200×50×20
托架B: 中心(0,100,0), 尺寸200×50×20

连接方式：
- 托架A的上边任意点 (x,25,10)，x∈[-100,100]
- 托架B的下边任意点 (x,75,10)，x∈[-100,100]
- 算法选择最优的x坐标
```

### 3. 旋转托架
```
托架A: 中心(0,0,0), 尺寸200×50×20, 旋转(0,0,45°)
托架B: 中心(200,200,0), 尺寸200×50×20, 无旋转

连接方式：
- 考虑托架A的旋转，计算实际的边缘中心点
- 与托架B的相应边缘建立连接
- 保证连接点的几何正确性
```

## 🚀 优势总结

1. **✅ 工程合理性**：符合船舶设计的实际需求
2. **✅ 结构优化**：窄边中心点连接更加稳固
3. **✅ 算法精确性**：准确计算旋转后的中心点位置
4. **✅ 兼容性好**：保持与现有代码的兼容性
5. **✅ 测试完备**：提供全面的测试验证

这个实现确保了电缆走线在托架窄边上只能从中心点出入，提高了设计的工程合理性和实用性。
